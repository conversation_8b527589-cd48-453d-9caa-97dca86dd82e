#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced calendar picker functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta

def test_calendar_picker():
    """Test the calendar picker functionality"""
    try:
        from tkcalendar import Calendar
    except ImportError:
        messagebox.showerror("Missing Library", 
                           "tkcalendar library is required.\n"
                           "Install with: pip install tkcalendar")
        return

    # Create test window
    root = tk.Tk()
    root.title("📅 Enhanced Calendar Picker Test")
    root.geometry("600x400")
    
    # Main frame
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Title
    title_label = ttk.Label(main_frame, text="📅 Enhanced Date Range Picker Test",
                           font=("Segoe UI", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    # Date range frame
    date_frame = ttk.LabelFrame(main_frame, text="Date Range Selection", padding=15)
    date_frame.pack(fill=tk.X, pady=(0, 20))
    
    # Preset selection
    ttk.Label(date_frame, text="Preset:", font=("Segoe UI", 9, "bold")).grid(
        row=0, column=0, sticky=tk.W, pady=(0, 10))
    
    preset_var = tk.StringVar(value="Custom range")
    preset_combo = ttk.Combobox(date_frame, textvariable=preset_var,
                               values=["Last 7 days", "Last 30 days", "Last 90 days",
                                      "This month", "Last month", "Custom range"],
                               state="readonly", width=20)
    preset_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 10))
    
    # Custom date selection
    custom_frame = ttk.Frame(date_frame)
    custom_frame.grid(row=1, column=0, columnspan=3, sticky=tk.EW, pady=(5, 0))
    
    # From date
    from_frame = ttk.Frame(custom_frame)
    from_frame.grid(row=0, column=0, sticky=tk.W, padx=(0, 30))
    
    ttk.Label(from_frame, text="From:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
    from_input_frame = ttk.Frame(from_frame)
    from_input_frame.pack(fill=tk.X, pady=(2, 0))
    
    start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
    start_entry = ttk.Entry(from_input_frame, textvariable=start_date_var, width=12)
    start_entry.pack(side=tk.LEFT, padx=(0, 5))
    
    def open_start_calendar():
        open_calendar_picker("start", start_date_var, root)
    
    ttk.Button(from_input_frame, text="📅", width=3, command=open_start_calendar).pack(side=tk.LEFT)
    
    # To date
    to_frame = ttk.Frame(custom_frame)
    to_frame.grid(row=0, column=1, sticky=tk.W, padx=(0, 30))
    
    ttk.Label(to_frame, text="To:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
    to_input_frame = ttk.Frame(to_frame)
    to_input_frame.pack(fill=tk.X, pady=(2, 0))
    
    end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
    end_entry = ttk.Entry(to_input_frame, textvariable=end_date_var, width=12)
    end_entry.pack(side=tk.LEFT, padx=(0, 5))
    
    def open_end_calendar():
        open_calendar_picker("end", end_date_var, root)
    
    ttk.Button(to_input_frame, text="📅", width=3, command=open_end_calendar).pack(side=tk.LEFT)
    
    # Quick buttons
    quick_frame = ttk.Frame(custom_frame)
    quick_frame.grid(row=0, column=2, sticky=tk.W)
    
    ttk.Label(quick_frame, text="Quick Select:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
    quick_buttons_frame = ttk.Frame(quick_frame)
    quick_buttons_frame.pack(fill=tk.X, pady=(2, 0))
    
    def set_quick_range(days):
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        start_date_var.set(start_date.strftime("%Y-%m-%d"))
        end_date_var.set(end_date.strftime("%Y-%m-%d"))
    
    ttk.Button(quick_buttons_frame, text="7d", width=6,
              command=lambda: set_quick_range(7)).pack(side=tk.LEFT, padx=(0, 2))
    ttk.Button(quick_buttons_frame, text="30d", width=6,
              command=lambda: set_quick_range(30)).pack(side=tk.LEFT, padx=(0, 2))
    ttk.Button(quick_buttons_frame, text="90d", width=6,
              command=lambda: set_quick_range(90)).pack(side=tk.LEFT)
    
    # Result display
    result_frame = ttk.LabelFrame(main_frame, text="Selected Date Range", padding=15)
    result_frame.pack(fill=tk.X, pady=(0, 20))
    
    result_var = tk.StringVar()
    result_label = ttk.Label(result_frame, textvariable=result_var, 
                            font=("Segoe UI", 10), foreground="#0078d4")
    result_label.pack()
    
    def update_result():
        start = start_date_var.get()
        end = end_date_var.get()
        if start and end:
            try:
                start_dt = datetime.strptime(start, "%Y-%m-%d")
                end_dt = datetime.strptime(end, "%Y-%m-%d")
                days = (end_dt - start_dt).days + 1
                result_var.set(f"📅 {start} to {end} ({days} days)")
            except:
                result_var.set("❌ Invalid date format")
        else:
            result_var.set("❌ Please select both dates")
    
    # Update result when dates change
    start_date_var.trace('w', lambda *args: update_result())
    end_date_var.trace('w', lambda *args: update_result())
    
    # Initial update
    update_result()
    
    # Test button
    def test_selection():
        start = start_date_var.get()
        end = end_date_var.get()
        messagebox.showinfo("Date Range Selected", 
                           f"✅ Selected Range:\n\n"
                           f"From: {start}\n"
                           f"To: {end}\n\n"
                           f"This would be used for Google Ads data extraction!")
    
    ttk.Button(main_frame, text="🧪 Test Selection", 
              command=test_selection).pack(pady=10)
    
    root.mainloop()

def open_calendar_picker(date_type, date_var, parent):
    """Open calendar picker window"""
    from tkcalendar import Calendar
    
    # Create calendar window
    cal_window = tk.Toplevel(parent)
    cal_window.title(f"Select {'Start' if date_type == 'start' else 'End'} Date")
    cal_window.geometry("350x450")
    cal_window.transient(parent)
    cal_window.grab_set()
    
    # Center window
    cal_window.update_idletasks()
    x = (cal_window.winfo_screenwidth() // 2) - (350 // 2)
    y = (cal_window.winfo_screenheight() // 2) - (450 // 2)
    cal_window.geometry(f"350x450+{x}+{y}")
    
    # Main frame
    main_frame = ttk.Frame(cal_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Title
    title_text = f"📅 Select {'Start' if date_type == 'start' else 'End'} Date"
    ttk.Label(main_frame, text=title_text, font=("Segoe UI", 12, "bold")).pack(pady=(0, 15))
    
    # Get current date
    current_date = datetime.now()
    if date_var.get():
        try:
            current_date = datetime.strptime(date_var.get(), "%Y-%m-%d")
        except:
            pass
    
    # Calendar
    cal = Calendar(main_frame, 
                  selectmode='day',
                  year=current_date.year,
                  month=current_date.month,
                  day=current_date.day,
                  date_pattern='yyyy-mm-dd',
                  background='white',
                  foreground='black',
                  bordercolor='#cccccc',
                  headersbackground='#f0f0f0',
                  selectbackground='#0078d4',
                  selectforeground='white')
    cal.pack(pady=(0, 15))
    
    # Selected date display
    selected_frame = ttk.Frame(main_frame)
    selected_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(selected_frame, text="Selected:", font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
    selected_var = tk.StringVar(value=cal.get_date())
    ttk.Label(selected_frame, textvariable=selected_var, 
             font=("Segoe UI", 9), foreground="#0078d4").pack(side=tk.LEFT, padx=(10, 0))
    
    cal.bind("<<CalendarSelected>>", lambda e: selected_var.set(cal.get_date()))
    
    # Quick select
    quick_frame = ttk.LabelFrame(main_frame, text="Quick Select", padding=10)
    quick_frame.pack(fill=tk.X, pady=(0, 15))
    
    quick_dates = [("Today", 0), ("Yesterday", 1), ("1 Week Ago", 7), ("1 Month Ago", 30)]
    
    for i, (text, days_ago) in enumerate(quick_dates):
        def set_date(days=days_ago):
            target = datetime.now() - timedelta(days=days)
            cal.selection_set(target.date())
            selected_var.set(target.strftime("%Y-%m-%d"))
        
        ttk.Button(quick_frame, text=text, width=12, command=set_date).grid(
            row=i//2, column=i%2, padx=5, pady=2, sticky=tk.W)
    
    # Buttons
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def select_date():
        date_var.set(cal.get_date())
        cal_window.destroy()
    
    ttk.Button(button_frame, text="✅ Select", command=select_date).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="❌ Cancel", command=cal_window.destroy).pack(side=tk.LEFT)

if __name__ == "__main__":
    test_calendar_picker()
