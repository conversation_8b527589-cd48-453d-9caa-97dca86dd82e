import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import ttkbootstrap as ttk_bootstrap
from ttkbootstrap.constants import *
# Removed tkcalendar import due to compatibility issues
import threading
import os
import sys
import json
import datetime
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import pandas as pd
import tempfile
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np

# Check if we need to install required packages
try:
    from googleapiclient.discovery import build
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.oauth2.credentials import Credentials
    from google.auth.transport.requests import Request
    import openpyxl  # For Excel export
    import requests  # For Airtable API
except ImportError:
    print("Installing required packages...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install",
                          "google-api-python-client", "google-auth-httplib2",
                          "google-auth-oauthlib", "google-ads", "pandas",
                          "openpyxl", "requests"])

    # Now import again after installation
    from googleapiclient.discovery import build
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.oauth2.credentials import Credentials
    from google.auth.transport.requests import Request
    import openpyxl
    import requests

# Google Sheets specific credentials (for Google Sheets tab)
GOOGLE_SHEETS_CLIENT_ID = "268268196822-oksa3m7juen98g8hbm3d0bbsiu2jd3e7.apps.googleusercontent.com"
GOOGLE_SHEETS_CLIENT_SECRET = "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"

# Google Ads API specific credentials (for credentials tab)
GOOGLE_ADS_CLIENT_ID = "774457045565-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com"
GOOGLE_ADS_CLIENT_SECRET = "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"

# Legacy constants (for backward compatibility)
CLIENT_ID = GOOGLE_SHEETS_CLIENT_ID  # Default to Google Sheets for existing functionality
CLIENT_SECRET = GOOGLE_SHEETS_CLIENT_SECRET

# Google Ads API specific scopes (for credentials tab)
GOOGLE_ADS_SCOPES = ['https://www.googleapis.com/auth/adwords']

# Google Sheets specific scopes (for Google Sheets tab)
SHEETS_SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/drive.readonly'
]

# Fallback scopes if Drive API doesn't work
SHEETS_ONLY_SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets'
]

# Legacy combined scopes (kept for compatibility)
SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/adwords']
CONFIG_FILE = "ads_extractor_config.json"

# Airtable Configuration
AIRTABLE_BASE_ID = "app7ffftdM6e3yekG"  # QuickFix base
AIRTABLE_GOOGLE_ADS_TABLE_ID = "tblRBXdh6L6zm9CZn"  # Google Ads table
AIRTABLE_API_URL = "https://api.airtable.com/v0"

# Modern color scheme
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'success': '#28A745',
    'warning': '#FFC107',
    'danger': '#DC3545',
    'info': '#17A2B8',
    'light': '#F8F9FA',
    'dark': '#343A40',
    'background': '#FFFFFF',
    'surface': '#F5F5F5'
}

# Date range presets
DATE_PRESETS = {
    "Today": lambda: (datetime.datetime.now().strftime('%Y-%m-%d'),
                     datetime.datetime.now().strftime('%Y-%m-%d')),
    "Yesterday": lambda: ((datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                         (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
    "Last 7 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'),
                           datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last 30 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'),
                            datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last 90 days": lambda: ((datetime.datetime.now() - datetime.timedelta(days=90)).strftime('%Y-%m-%d'),
                            datetime.datetime.now().strftime('%Y-%m-%d')),
    "This month": lambda: (datetime.datetime.now().replace(day=1).strftime('%Y-%m-%d'),
                          datetime.datetime.now().strftime('%Y-%m-%d')),
    "Last month": lambda: ((datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d'),
                          (datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
    "Custom": lambda: (None, None)
}

class ModernProgressDialog:
    """Modern progress dialog with cancel functionality"""

    def __init__(self, parent, title="Processing...", message="Please wait..."):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (150 // 2)
        self.dialog.geometry(f"400x150+{x}+{y}")

        self.cancelled = False

        # Main frame
        main_frame = ttk_bootstrap.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # Message label
        self.message_label = ttk_bootstrap.Label(main_frame, text=message, font=('Arial', 11))
        self.message_label.pack(pady=(0, 15))

        # Progress bar
        self.progress = ttk_bootstrap.Progressbar(
            main_frame,
            mode='indeterminate',
            bootstyle="info-striped",
            length=300
        )
        self.progress.pack(pady=(0, 15))
        self.progress.start(10)

        # Cancel button
        self.cancel_btn = ttk_bootstrap.Button(
            main_frame,
            text="Cancel",
            command=self.cancel,
            bootstyle="outline-danger"
        )
        self.cancel_btn.pack()

    def update_message(self, message):
        self.message_label.config(text=message)
        self.dialog.update()

    def cancel(self):
        self.cancelled = True
        self.close()

    def close(self):
        self.progress.stop()
        self.dialog.destroy()

class ModernTooltip:
    """Modern tooltip implementation"""

    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        if self.tooltip:
            return

        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")

        label = ttk_bootstrap.Label(
            self.tooltip,
            text=self.text,
            background="#333333",
            foreground="white",
            padding=5,
            font=('Arial', 9)
        )
        label.pack()

    def hide_tooltip(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None



class ModernKPICard:
    """Modern KPI card widget"""

    def __init__(self, parent, title, value, change=None, color="primary"):
        self.frame = ttk_bootstrap.Frame(parent, padding=15, bootstyle=f"{color}")

        # Title
        title_label = ttk_bootstrap.Label(
            self.frame,
            text=title,
            font=('Arial', 10, 'bold'),
            bootstyle=f"{color}-inverse"
        )
        title_label.pack(anchor='w')

        # Value
        self.value_label = ttk_bootstrap.Label(
            self.frame,
            text=value,
            font=('Arial', 18, 'bold'),
            bootstyle=f"{color}-inverse"
        )
        self.value_label.pack(anchor='w', pady=(5, 0))

        # Change indicator
        if change:
            change_color = "success" if change.startswith('+') else "danger"
            change_label = ttk_bootstrap.Label(
                self.frame,
                text=change,
                font=('Arial', 9),
                bootstyle=f"{change_color}-inverse"
            )
            change_label.pack(anchor='w')

    def update_value(self, value, change=None):
        self.value_label.config(text=value)

    def pack(self, **kwargs):
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        self.frame.grid(**kwargs)

class AirtableManager:
    """Airtable API integration manager"""

    def __init__(self, api_key=None):
        self.api_key = api_key
        self.base_id = AIRTABLE_BASE_ID
        self.google_ads_table_id = AIRTABLE_GOOGLE_ADS_TABLE_ID
        self.ghl_table_id = "tblcdFVUC3zJrbmNf"  # GHL table
        self.pos_table_id = "tblHyyZHUsTdEb3BL"  # POS table
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        } if api_key else None

    def set_api_key(self, api_key):
        """Set Airtable API key"""
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    def test_connection(self):
        """Test Airtable API connection"""
        if not self.api_key:
            return False, "API key not set"

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            params = {'maxRecords': 1}

            # Enhanced debugging
            print(f"\n🔍 AIRTABLE API DEBUG:")
            print(f"   URL: {url}")
            print(f"   Headers: {self.headers}")
            print(f"   Params: {params}")
            print(f"   API Key (first 10 chars): {self.api_key[:10]}...")
            print(f"   Base ID: {self.base_id}")
            print(f"   Table ID: {self.google_ads_table_id}")

            response = requests.get(url, headers=self.headers, params=params)

            print(f"   Response Status: {response.status_code}")
            print(f"   Response Headers: {dict(response.headers)}")
            print(f"   Response Text: {response.text[:500]}...")

            if response.status_code == 200:
                return True, "Connection successful"
            else:
                return False, f"API Error: {response.status_code} - {response.text}"
        except Exception as e:
            return False, f"Connection error: {str(e)}"

    def upload_google_ads_data(self, dataframe, batch_size=10, mode="append"):
        """Upload Google Ads data to Airtable with different modes"""
        if not self.api_key:
            raise Exception("Airtable API key not set")

        if dataframe.empty:
            return 0, "No data to upload", 0

        url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
        uploaded_count = 0
        skipped_count = 0
        errors = []

        # Enhanced debugging for upload
        print(f"\n🔍 UPLOAD DEBUG:")
        print(f"   Upload URL: {url}")
        print(f"   Headers: {self.headers}")
        print(f"   Base ID: {self.base_id}")
        print(f"   Table ID: {self.google_ads_table_id}")
        print(f"   Data rows to upload: {len(dataframe)}")
        print(f"   Mode: {mode}")

        # Handle different sync modes
        filtered_data = dataframe.copy()

        if mode == "force_upload":
            # Force upload mode: Upload all data without any checks
            print(f"🚀 FORCE UPLOAD MODE: Uploading all {len(filtered_data)} records without checks")
            skipped_count = 0
        elif mode == "incremental":
            # Traditional content-based duplicate detection
            existing_keys = self.get_existing_record_keys()

            def is_new_record(row):
                date = row['Date'] if pd.notna(row['Date']) else None
                campaign_id = row['Campaign ID'] if pd.notna(row['Campaign ID']) else None

                if date and campaign_id:
                    key = f"{date}_{int(campaign_id)}"
                    return key not in existing_keys
                return True

            mask = filtered_data.apply(is_new_record, axis=1)
            filtered_data = filtered_data[mask]
            skipped_count = len(dataframe) - len(filtered_data)

        elif mode == "smart_incremental":
            # Position-based sync with content verification
            print("🚀 Using Smart Incremental (Position-based) sync")

            # Get last record from Airtable
            last_record, total_count = self.get_last_record_from_airtable(self.google_ads_table_id, 'Date')
            print(f"📊 Airtable has {total_count} existing records")

            if last_record:
                # Find matching position in local data
                start_position, matched_record = self.find_matching_position_in_local_data(
                    dataframe, last_record, 'google_ads'
                )

                if start_position > 0:
                    print(f"✅ Found matching record at position {start_position}")
                    print(f"📤 Will upload records from position {start_position + 1} onwards")

                    # Upload only records after the matched position
                    filtered_data = dataframe.iloc[start_position:].copy()
                    skipped_count = start_position
                else:
                    print("⚠️ No matching record found, falling back to full content-based sync")
                    # Fallback to traditional incremental
                    existing_keys = self.get_existing_record_keys()

                    def is_new_record(row):
                        date = row['Date'] if pd.notna(row['Date']) else None
                        campaign_id = row['Campaign ID'] if pd.notna(row['Campaign ID']) else None

                        if date and campaign_id:
                            key = f"{date}_{int(campaign_id)}"
                            return key not in existing_keys
                        return True

                    mask = filtered_data.apply(is_new_record, axis=1)
                    filtered_data = filtered_data[mask]
                    skipped_count = len(dataframe) - len(filtered_data)
            else:
                print("ℹ️ No existing records found, uploading all data")
                skipped_count = 0
        else:
            # Append or replace mode
            skipped_count = 0

        if filtered_data.empty:
            return 0, "No new records to upload", skipped_count

        # Process data in batches
        for i in range(0, len(filtered_data), batch_size):
            batch = filtered_data.iloc[i:i+batch_size]
            records = []

            for idx, row in batch.iterrows():
                # Map DataFrame columns to Airtable fields
                # Map to match the exact Airtable table structure
                # Helper function to safely convert values
                def safe_float(value, default=0):
                    try:
                        if pd.notna(value) and not np.isinf(value):
                            return float(value)
                        return default
                    except (ValueError, TypeError):
                        return default

                def safe_int(value, default=0):
                    try:
                        if pd.notna(value) and not np.isinf(value):
                            return int(value)
                        return default
                    except (ValueError, TypeError):
                        return default

                def safe_str(value):
                    try:
                        if pd.notna(value):
                            return str(value)
                        return None
                    except (ValueError, TypeError):
                        return None

                record = {
                    "fields": {
                        "Date": safe_str(row['Date']),
                        "Campaign ID": safe_str(row['Campaign ID']),
                        "Campaign Name": safe_str(row['Campaign Name']),
                        "Cost": safe_float(row['Cost']),
                        "Impressions": safe_int(row['Impressions']),
                        "Clicks": safe_int(row['Clicks']),
                        "Conversions": safe_float(row['Conversions']),
                        "CTR": safe_float(row['CTR']),
                        "CPC": safe_float(row['CPC']),
                        "Conv. Rate": safe_float(row['Conv. Rate']),
                        "Cost per Conv.": safe_float(row['Cost per Conv.']),
                    }
                }
                # Remove None values to avoid Airtable API issues
                record["fields"] = {k: v for k, v in record["fields"].items() if v is not None}
                records.append(record)

                # Debug: Show first record structure
                if idx == 0 and i == 0:  # First record of first batch
                    print(f"   🔍 FIRST RECORD DEBUG:")
                    print(f"   📋 MAPPED Record fields: {record['fields']}")
                    print(f"   📊 Available columns: {list(row.index)}")
                    print(f"   📝 Raw values: Date={row.get('Date')}, Campaign={row.get('Campaign Name')}, Cost={row.get('Cost')}")
                    print(f"   🎯 ACTUAL FIELDS BEING SENT: {list(record['fields'].keys())}")

            # Send batch to Airtable
            try:
                payload = {"records": records}
                response = requests.post(url, headers=self.headers, json=payload)

                if response.status_code == 200:
                    uploaded_count += len(records)
                else:
                    error_msg = f"Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}"
                    errors.append(error_msg)
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} error: {str(e)}"
                errors.append(error_msg)

        return uploaded_count, errors, skipped_count

    def upload_ghl_data(self, dataframe, batch_size=10, mode="append"):
        """Upload GHL lead data to Airtable with enhanced diagnostics"""
        if not self.api_key:
            raise Exception("Airtable API key not set")

        if dataframe.empty:
            return 0, "No data to upload", 0

        url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.ghl_table_id}"
        uploaded_count = 0
        skipped_count = 0
        errors = []

        # Enhanced logging for diagnostics
        print(f"\n🔍 ENHANCED GHL SYNC DIAGNOSTICS")
        print(f"=" * 60)
        print(f"📊 Input Data Analysis:")
        print(f"   - Total local records: {len(dataframe):,}")
        print(f"   - Sync mode: {mode}")
        print(f"   - Batch size: {batch_size}")

        # Analyze input data structure
        if not dataframe.empty:
            print(f"   - Columns: {list(dataframe.columns)}")
            print(f"   - First record Contact ID: {dataframe.iloc[0].get('Contact ID', 'N/A')}")
            print(f"   - Last record Contact ID: {dataframe.iloc[-1].get('Contact ID', 'N/A')}")

            # Check for missing critical fields
            critical_fields = ['Contact ID', 'Opportunity ID', 'Date Created']
            for field in critical_fields:
                if field in dataframe.columns:
                    missing_count = dataframe[field].isna().sum()
                    print(f"   - {field} missing values: {missing_count:,}")
                else:
                    print(f"   - ⚠️ {field} column not found!")

        # Handle different sync modes
        filtered_data = dataframe.copy()

        if mode == "incremental":
            # Traditional content-based duplicate detection
            existing_keys = self.get_existing_ghl_record_keys()

            def is_new_ghl_record(row):
                contact_id = row.get('Contact ID') if pd.notna(row.get('Contact ID', '')) else None
                email = row.get('email', '') if pd.notna(row.get('email', '')) else ''
                phone = row.get('phone', '') if pd.notna(row.get('phone', '')) else ''

                if contact_id:
                    return contact_id not in existing_keys
                elif email and phone:
                    key = f"{email}_{phone}"
                    return key not in existing_keys
                return True

            mask = filtered_data.apply(is_new_ghl_record, axis=1)
            filtered_data = filtered_data[mask]
            skipped_count = len(dataframe) - len(filtered_data)

        elif mode == "smart_incremental":
            print(f"\n⚡ ENHANCED Smart Incremental (Position-based) sync for GHL")
            print(f"=" * 60)

            # Get last record from Airtable with enhanced logging
            print(f"🔍 Step 1: Fetching last record from Airtable...")
            last_record, total_count = self.get_last_record_from_airtable(self.ghl_table_id, 'Date Created')
            print(f"📊 Airtable Analysis:")
            print(f"   - Total existing records: {total_count:,}")
            print(f"   - Expected missing records: {len(dataframe) - total_count:,}")

            if last_record:
                print(f"\n📋 Last Airtable Record Details:")
                print(f"   - Contact ID: {last_record.get('Contact ID', 'N/A')}")
                print(f"   - Opportunity ID: {last_record.get('Opportunity ID', 'N/A')}")
                print(f"   - Date Created: {last_record.get('Date Created', 'N/A')}")
                print(f"   - Contact Name: {last_record.get('contact name', 'N/A')}")

                # Find matching position in local data with enhanced diagnostics
                print(f"\n🔍 Step 2: Finding matching position in local data...")
                start_position, matched_record = self.find_matching_position_in_local_data_enhanced(
                    dataframe, last_record, 'ghl'
                )

                if start_position >= 0:
                    print(f"\n✅ POSITION MATCH FOUND!")
                    print(f"   - Matched at position: {start_position + 1:,} (1-based)")
                    print(f"   - Will upload from position: {start_position + 1:,} onwards")
                    print(f"   - Records to upload: {len(dataframe) - start_position:,}")

                    # Verify the match
                    if matched_record is not None:
                        print(f"\n🔍 Match Verification:")
                        print(f"   - Local Contact ID: {matched_record.get('Contact ID', 'N/A')}")
                        print(f"   - Local Opportunity ID: {matched_record.get('Opportunity ID', 'N/A')}")
                        print(f"   - Local Date Created: {matched_record.get('Date Created', 'N/A')}")
                        print(f"   - Local Contact Name: {matched_record.get('contact name', 'N/A')}")

                    # Upload only records after the matched position
                    filtered_data = dataframe.iloc[start_position:].copy()
                    skipped_count = start_position

                    print(f"\n📊 FINAL SYNC PLAN:")
                    print(f"   - Total local records: {len(dataframe):,}")
                    print(f"   - Existing in Airtable: {total_count:,}")
                    print(f"   - Starting from position: {start_position + 1:,}")
                    print(f"   - Records to upload: {len(filtered_data):,}")
                    print(f"   - Expected final count: {total_count + len(filtered_data):,}")

                    # Verify expected outcome
                    expected_final = total_count + len(filtered_data)
                    if expected_final != len(dataframe):
                        print(f"   ⚠️ WARNING: Expected final count ({expected_final:,}) != Local file count ({len(dataframe):,})")
                        print(f"   ⚠️ This indicates a potential sync issue!")
                    else:
                        print(f"   ✅ Expected final count matches local file count!")

                else:
                    print(f"\n⚠️ NO MATCHING RECORD FOUND - FALLBACK TO CONTENT-BASED SYNC")
                    print(f"   - This may indicate data inconsistency between file and Airtable")
                    print(f"   - Falling back to traditional incremental sync...")

                    # Fallback to traditional incremental
                    existing_keys = self.get_existing_ghl_record_keys()
                    print(f"   - Retrieved {len(existing_keys):,} existing keys for comparison")

                    def is_new_ghl_record(row):
                        contact_id = row.get('Contact ID') if pd.notna(row.get('Contact ID', '')) else None
                        email = row.get('email', '') if pd.notna(row.get('email', '')) else ''
                        phone = row.get('phone', '') if pd.notna(row.get('phone', '')) else ''

                        if contact_id:
                            return contact_id not in existing_keys
                        elif email and phone:
                            key = f"{email}_{phone}"
                            return key not in existing_keys
                        return True

                    mask = filtered_data.apply(is_new_ghl_record, axis=1)
                    filtered_data = filtered_data[mask]
                    skipped_count = len(dataframe) - len(filtered_data)
                    print(f"   - Records after content-based filtering: {len(filtered_data):,}")
                    print(f"   - Records skipped by content filter: {skipped_count:,}")
            else:
                print(f"\nℹ️ No existing records found in Airtable")
                print(f"   - This appears to be the first sync")
                print(f"   - Will upload all {len(dataframe):,} records")
                skipped_count = 0
        else:
            # Append or replace mode
            print(f"\n📤 {mode.title()} Mode:")
            print(f"   - Will upload all {len(dataframe):,} records")
            skipped_count = 0

        if filtered_data.empty:
            print(f"\n❌ NO RECORDS TO UPLOAD")
            print(f"   - All records were filtered out")
            print(f"   - This may indicate all records already exist in Airtable")
            return 0, "No new records to upload", skipped_count

        # Enhanced batch processing with detailed logging
        print(f"\n🚀 STARTING BATCH UPLOAD PROCESS")
        print(f"=" * 60)

        # Process data in batches with enhanced logging
        total_batches = (len(filtered_data) + batch_size - 1) // batch_size
        print(f"📦 Processing {len(filtered_data):,} records in {total_batches} batches of {batch_size}")

        for i in range(0, len(filtered_data), batch_size):
            batch_num = i // batch_size + 1
            batch = filtered_data.iloc[i:i+batch_size]
            records = []

            print(f"\n📦 Batch {batch_num}/{total_batches}: Processing records {i+1:,} to {min(i+batch_size, len(filtered_data)):,}")

            for idx, row in batch.iterrows():
                # Map DataFrame columns to Airtable fields
                record = {
                    "fields": {
                        "contact name": str(row.get('contact name', '')) if pd.notna(row.get('contact name', '')) else None,
                        "Location": str(row.get('Location', '')) if pd.notna(row.get('Location', '')) else None,
                        "phone": str(row.get('phone', '')) if pd.notna(row.get('phone', '')) else None,
                        "email": str(row.get('email', '')) if pd.notna(row.get('email', '')) else None,
                        "pipeline": str(row.get('pipeline', '')) if pd.notna(row.get('pipeline', '')) else None,
                        "stage": str(row.get('stage', '')) if pd.notna(row.get('stage', '')) else None,
                        "Lead Value": float(row.get('Lead Value', 0)) if pd.notna(row.get('Lead Value', 0)) else 0,
                        "Date Created": str(row.get('Date Created', '')) if pd.notna(row.get('Date Created', '')) else None,
                        "Traffic Source": str(row.get('Traffic Source', '')) if pd.notna(row.get('Traffic Source', '')) else None,
                        "Channel": str(row.get('Channel', '')) if pd.notna(row.get('Channel', '')) else None,
                        "Conversion Event": str(row.get('Conversion Event', '')) if pd.notna(row.get('Conversion Event', '')) else None,
                        "Opportunity ID": str(row.get('Opportunity ID', '')) if pd.notna(row.get('Opportunity ID', '')) else None,
                        "Contact ID": str(row.get('Contact ID', '')) if pd.notna(row.get('Contact ID', '')) else None
                    }
                }
                # Remove None values
                record["fields"] = {k: v for k, v in record["fields"].items() if v is not None}
                records.append(record)

            # Send batch to Airtable
            try:
                payload = {"records": records}
                response = requests.post(url, headers=self.headers, json=payload)

                if response.status_code == 200:
                    uploaded_count += len(records)
                    print(f"   ✅ Batch {batch_num} uploaded successfully: {len(records)} records")
                else:
                    error_msg = f"Batch {batch_num} failed: {response.status_code} - {response.text}"
                    errors.append(error_msg)
                    print(f"   ❌ Batch {batch_num} failed: {response.status_code}")
                    print(f"   📋 Full error response: {response.text}")

                    # Try to parse and show specific field errors
                    try:
                        error_data = response.json()
                        if 'error' in error_data:
                            print(f"   🔍 Error details: {error_data['error']}")
                        if 'errors' in error_data:
                            for idx, err in enumerate(error_data['errors']):
                                print(f"   🔍 Record {idx+1} error: {err}")
                    except:
                        pass
            except Exception as e:
                error_msg = f"Batch {batch_num} error: {str(e)}"
                errors.append(error_msg)
                print(f"   ❌ Batch {batch_num} error: {str(e)}")

        # Final results summary
        print(f"\n📊 UPLOAD COMPLETE - FINAL RESULTS:")
        print(f"=" * 60)
        print(f"   - Records processed: {len(filtered_data):,}")
        print(f"   - Records uploaded: {uploaded_count:,}")
        print(f"   - Records skipped: {skipped_count:,}")
        print(f"   - Batches processed: {total_batches}")
        print(f"   - Errors encountered: {len(errors)}")

        if errors:
            print(f"\n❌ ERRORS ENCOUNTERED:")
            for error in errors[:5]:  # Show first 5 errors
                print(f"   - {error}")
            if len(errors) > 5:
                print(f"   - ... and {len(errors) - 5} more errors")
        else:
            print(f"   ✅ No errors encountered!")

        # Calculate expected final Airtable count
        if mode == "smart_incremental" and 'total_count' in locals():
            expected_final = total_count + uploaded_count
            print(f"\n🎯 EXPECTED AIRTABLE STATE:")
            print(f"   - Previous count: {total_count:,}")
            print(f"   - Added records: {uploaded_count:,}")
            print(f"   - Expected final count: {expected_final:,}")
            print(f"   - Target count (local file): {len(dataframe):,}")

            if expected_final == len(dataframe):
                print(f"   ✅ SUCCESS: Expected count matches local file!")
            else:
                discrepancy = len(dataframe) - expected_final
                print(f"   ⚠️ DISCREPANCY: {abs(discrepancy):,} records {'missing' if discrepancy > 0 else 'extra'}")
                print(f"   ⚠️ This indicates the sync issue is still present!")

        return uploaded_count, errors, skipped_count

    def get_existing_ghl_record_keys(self):
        """Get existing GHL record keys for duplicate detection"""
        existing_records = self.get_existing_records_for_table(self.ghl_table_id, get_all=True)
        existing_keys = set()
        contact_id_count = 0
        email_phone_count = 0

        for record in existing_records:
            fields = record.get('fields', {})
            contact_id = fields.get('Contact ID')
            email = fields.get('email', '')
            phone = fields.get('phone', '')

            if contact_id:
                existing_keys.add(contact_id)
                contact_id_count += 1
            elif email and phone:
                key = f"{email}_{phone}"
                existing_keys.add(key)
                email_phone_count += 1

        print(f"📊 Found {len(existing_records)} existing GHL records in Airtable")
        print(f"   - {contact_id_count} with Contact ID")
        print(f"   - {email_phone_count} with email+phone combination")
        return existing_keys

    def get_existing_ghl_summary(self):
        """Get summary of existing GHL records for better sync analysis"""
        existing_records = self.get_existing_records_for_table(self.ghl_table_id, get_all=True)

        if not existing_records:
            return {
                'total_records': 0,
                'locations': set(),
                'pipelines': set(),
                'stages': set(),
                'date_range': None
            }

        locations = set()
        pipelines = set()
        stages = set()
        dates = []

        for record in existing_records:
            fields = record.get('fields', {})
            location = fields.get('Location')
            pipeline = fields.get('pipeline')
            stage = fields.get('stage')
            date_created = fields.get('Date Created')

            if location:
                locations.add(location)
            if pipeline:
                pipelines.add(pipeline)
            if stage:
                stages.add(stage)
            if date_created:
                dates.append(date_created)

        dates.sort()

        return {
            'total_records': len(existing_records),
            'locations': locations,
            'pipelines': pipelines,
            'stages': stages,
            'date_range': (dates[0], dates[-1]) if dates else None,
            'unique_dates': len(set(dates))
        }

    def get_existing_records_for_table(self, table_id, date_range=None, get_all=False):
        """Get existing records from any Airtable table"""
        if not self.api_key:
            return []

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{table_id}"
            all_records = []
            offset = None

            while True:
                params = {}
                if not get_all:
                    params['maxRecords'] = 100

                if date_range:
                    # Add date filter if provided
                    start_date, end_date = date_range
                    filter_formula = f"AND(IS_AFTER({{Date}}, '{start_date}'), IS_BEFORE({{Date}}, '{end_date}'))"
                    params['filterByFormula'] = filter_formula

                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=self.headers, params=params)

                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    all_records.extend(records)

                    # Check if there are more records
                    offset = data.get('offset')
                    if not offset or not get_all:
                        break
                else:
                    break

            return all_records
        except Exception:
            return []

    def get_existing_records(self, date_range=None, get_all=False):
        """Get existing records from Airtable"""
        if not self.api_key:
            return []

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            all_records = []
            offset = None

            while True:
                params = {}
                if not get_all:
                    params['maxRecords'] = 100

                if date_range:
                    # Add date filter if provided
                    start_date, end_date = date_range
                    filter_formula = f"AND(IS_AFTER({{Date}}, '{start_date}'), IS_BEFORE({{Date}}, '{end_date}'))"
                    params['filterByFormula'] = filter_formula

                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=self.headers, params=params)

                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    all_records.extend(records)

                    # Check if there are more records
                    offset = data.get('offset')
                    if not offset or not get_all:
                        break
                else:
                    break

            return all_records
        except Exception:
            return []

    def get_existing_record_keys(self):
        """Get existing record keys (Date + Campaign ID combinations) for duplicate detection"""
        existing_records = self.get_existing_records(get_all=True)
        existing_keys = set()
        record_count = 0

        for record in existing_records:
            fields = record.get('fields', {})
            date = fields.get('Date')
            campaign_id = fields.get('Campaign ID')

            if date and campaign_id:
                key = f"{date}_{campaign_id}"
                existing_keys.add(key)
                record_count += 1

        print(f"📊 Found {record_count} existing Google Ads records in Airtable")
        return existing_keys

    def get_existing_record_summary(self):
        """Get summary of existing records for better sync analysis"""
        existing_records = self.get_existing_records(get_all=True)

        if not existing_records:
            return {
                'total_records': 0,
                'date_range': None,
                'latest_date': None,
                'campaigns': set()
            }

        dates = []
        campaigns = set()

        for record in existing_records:
            fields = record.get('fields', {})
            date = fields.get('Date')
            campaign_name = fields.get('Campaign Name')

            if date:
                dates.append(date)
            if campaign_name:
                campaigns.add(campaign_name)

        dates.sort()

        return {
            'total_records': len(existing_records),
            'date_range': (dates[0], dates[-1]) if dates else None,
            'latest_date': dates[-1] if dates else None,
            'campaigns': campaigns,
            'unique_dates': len(set(dates))
        }

    def get_latest_date_in_airtable(self):
        """Get the most recent date in Airtable to determine where to start incremental update"""
        if not self.api_key:
            return None

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"
            params = {
                'maxRecords': 1,
                'sort[0][field]': 'Date',
                'sort[0][direction]': 'desc'
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                records = response.json().get('records', [])
                if records:
                    return records[0]['fields'].get('Date')

            return None
        except Exception:
            return None

    def get_last_record_from_airtable(self, table_id, sort_field='Date Created'):
        """Get the last record from Airtable for position-based sync"""
        if not self.api_key:
            return None, 0

        try:
            # First, get total record count
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{table_id}"
            count_response = requests.get(url, headers=self.headers, params={'maxRecords': 1})

            if count_response.status_code != 200:
                return None, 0

            # Get all records to find the actual last one (Airtable doesn't provide direct count)
            all_records = self.get_existing_records_for_table(table_id, get_all=True)
            total_count = len(all_records)

            if total_count == 0:
                return None, 0

            # Get the last record by creation order or specified sort field
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{table_id}"
            params = {
                'maxRecords': 1,
                'sort[0][field]': sort_field,
                'sort[0][direction]': 'desc'
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                records = response.json().get('records', [])
                if records:
                    last_record = records[0]['fields']
                    return last_record, total_count

            return None, total_count
        except Exception as e:
            print(f"Error getting last record: {str(e)}")
            return None, 0

    def find_matching_position_in_local_data(self, local_data, last_airtable_record, data_type='ghl'):
        """Find the position of the last Airtable record in local data for efficient sync"""
        if last_airtable_record is None or local_data.empty:
            return 0, None

        try:
            if data_type == 'ghl':
                # For GHL: Match using Contact ID + Opportunity ID + Date Created
                target_contact_id = last_airtable_record.get('Contact ID')
                target_opportunity_id = last_airtable_record.get('Opportunity ID')
                target_date = last_airtable_record.get('Date Created')

                print(f"🔍 Looking for GHL record with:")
                print(f"   Contact ID: {target_contact_id}")
                print(f"   Opportunity ID: {target_opportunity_id}")
                print(f"   Date Created: {target_date}")

                # Search for exact match
                for idx, row in local_data.iterrows():
                    local_contact_id = str(row.get('Contact ID', '')) if pd.notna(row.get('Contact ID', '')) else ''
                    local_opportunity_id = str(row.get('Opportunity ID', '')) if pd.notna(row.get('Opportunity ID', '')) else ''
                    local_date = str(row.get('Date Created', '')) if pd.notna(row.get('Date Created', '')) else ''

                    if (str(target_contact_id) == local_contact_id and
                        str(target_opportunity_id) == local_opportunity_id and
                        str(target_date) == local_date):
                        print(f"✅ Found exact match at position {idx + 1}")
                        return idx + 1, row  # Return next position to start uploading

                # If no exact match, try fuzzy matching around the expected position
                expected_position = len(local_data) - 100  # Look in last 100 records
                if expected_position < 0:
                    expected_position = 0

                print(f"⚠️ No exact match found, searching around position {expected_position}")

                # Search in a window around expected position
                search_start = max(0, expected_position - 50)
                search_end = min(len(local_data), expected_position + 50)

                for idx in range(search_start, search_end):
                    row = local_data.iloc[idx]
                    local_contact_id = str(row.get('Contact ID', '')) if pd.notna(row.get('Contact ID', '')) else ''

                    # Match by Contact ID only as fallback
                    if str(target_contact_id) == local_contact_id:
                        print(f"✅ Found Contact ID match at position {idx + 1}")
                        return idx + 1, row

            elif data_type == 'google_ads':
                # For Google Ads: Match using Date + Campaign ID + Campaign Name
                target_date = last_airtable_record.get('Date')
                target_campaign_id = last_airtable_record.get('Campaign ID')
                target_campaign_name = last_airtable_record.get('Campaign Name')

                print(f"🔍 Looking for Google Ads record with:")
                print(f"   Date: {target_date}")
                print(f"   Campaign ID: {target_campaign_id}")
                print(f"   Campaign Name: {target_campaign_name}")

                for idx, row in local_data.iterrows():
                    local_date = str(row.get('Date', '')) if pd.notna(row.get('Date', '')) else ''
                    local_campaign_id = str(row.get('Campaign ID', '')) if pd.notna(row.get('Campaign ID', '')) else ''
                    local_campaign_name = str(row.get('Campaign Name', '')) if pd.notna(row.get('Campaign Name', '')) else ''

                    if (str(target_date) == local_date and
                        str(target_campaign_id) == local_campaign_id and
                        str(target_campaign_name) == local_campaign_name):
                        print(f"✅ Found exact match at position {idx + 1}")
                        return idx + 1, row

            print(f"❌ No matching record found in local data")
            return 0, None

        except Exception as e:
            print(f"Error finding matching position: {str(e)}")
            return 0, None

    def find_matching_position_in_local_data_enhanced(self, local_data, last_airtable_record, data_type='ghl'):
        """Enhanced position finding with comprehensive diagnostics for sync troubleshooting"""
        if last_airtable_record is None or local_data.empty:
            print(f"❌ Cannot find position: {'No Airtable record' if last_airtable_record is None else 'Empty local data'}")
            return 0, None

        try:
            if data_type == 'ghl':
                # Enhanced GHL matching with detailed logging
                target_contact_id = last_airtable_record.get('Contact ID')
                target_opportunity_id = last_airtable_record.get('Opportunity ID')
                target_date = last_airtable_record.get('Date Created')

                print(f"🔍 ENHANCED POSITION SEARCH:")
                print(f"   - Target Contact ID: '{target_contact_id}'")
                print(f"   - Target Opportunity ID: '{target_opportunity_id}'")
                print(f"   - Target Date Created: '{target_date}'")
                print(f"   - Searching through {len(local_data):,} local records...")

                # Track search progress
                exact_matches = []
                contact_id_matches = []
                opportunity_id_matches = []
                date_matches = []

                # Search for exact match with detailed tracking
                print(f"\n🔍 Performing comprehensive search...")
                for idx, row in local_data.iterrows():
                    local_contact_id = str(row.get('Contact ID', '')) if pd.notna(row.get('Contact ID', '')) else ''
                    local_opportunity_id = str(row.get('Opportunity ID', '')) if pd.notna(row.get('Opportunity ID', '')) else ''
                    local_date = str(row.get('Date Created', '')) if pd.notna(row.get('Date Created', '')) else ''

                    # Track individual field matches
                    contact_match = str(target_contact_id) == local_contact_id
                    opportunity_match = str(target_opportunity_id) == local_opportunity_id
                    date_match = str(target_date) == local_date

                    if contact_match:
                        contact_id_matches.append(idx)
                    if opportunity_match:
                        opportunity_id_matches.append(idx)
                    if date_match:
                        date_matches.append(idx)

                    # Check for exact triple match
                    if contact_match and opportunity_match and date_match:
                        exact_matches.append(idx)
                        print(f"✅ EXACT MATCH FOUND at position {idx + 1:,} (0-based: {idx})")
                        print(f"   - Contact ID: '{local_contact_id}' ✅")
                        print(f"   - Opportunity ID: '{local_opportunity_id}' ✅")
                        print(f"   - Date Created: '{local_date}' ✅")
                        print(f"   - Contact Name: '{row.get('contact name', 'N/A')}'")
                        return idx + 1, row  # Return next position to start uploading

                # Report search results
                print(f"\n📊 SEARCH RESULTS SUMMARY:")
                print(f"   - Exact matches (all 3 fields): {len(exact_matches)}")
                print(f"   - Contact ID matches: {len(contact_id_matches)}")
                print(f"   - Opportunity ID matches: {len(opportunity_id_matches)}")
                print(f"   - Date Created matches: {len(date_matches)}")

                # If no exact match, try partial matches
                if len(exact_matches) == 0:
                    print(f"\n⚠️ NO EXACT MATCH FOUND - Analyzing partial matches...")

                    # Try Contact ID + Opportunity ID match (without date)
                    for idx in contact_id_matches:
                        if idx in opportunity_id_matches:
                            row = local_data.iloc[idx]
                            print(f"🔍 Found Contact ID + Opportunity ID match at position {idx + 1:,}")
                            print(f"   - Contact ID: '{row.get('Contact ID', 'N/A')}' ✅")
                            print(f"   - Opportunity ID: '{row.get('Opportunity ID', 'N/A')}' ✅")
                            print(f"   - Date Created: '{row.get('Date Created', 'N/A')}' ❌ (Expected: '{target_date}')")
                            print(f"   - Using this as fallback match")
                            return idx + 1, row

                    # Try Contact ID only match
                    if len(contact_id_matches) > 0:
                        idx = contact_id_matches[0]  # Use first match
                        row = local_data.iloc[idx]
                        print(f"🔍 Found Contact ID only match at position {idx + 1:,}")
                        print(f"   - Contact ID: '{row.get('Contact ID', 'N/A')}' ✅")
                        print(f"   - Opportunity ID: '{row.get('Opportunity ID', 'N/A')}' ❌ (Expected: '{target_opportunity_id}')")
                        print(f"   - Date Created: '{row.get('Date Created', 'N/A')}' ❌ (Expected: '{target_date}')")
                        print(f"   - Using this as last resort match")
                        return idx + 1, row

                # If still no match, provide detailed analysis
                print(f"\n❌ NO SUITABLE MATCH FOUND")
                print(f"🔍 DETAILED ANALYSIS:")

                # Show some sample records for comparison
                print(f"\n📋 Sample local records for comparison:")
                sample_size = min(5, len(local_data))
                for i in range(sample_size):
                    row = local_data.iloc[i]
                    print(f"   Record {i+1}: Contact ID='{row.get('Contact ID', 'N/A')}', "
                          f"Opp ID='{row.get('Opportunity ID', 'N/A')}', "
                          f"Date='{row.get('Date Created', 'N/A')}'")

                # Show last few records
                print(f"\n📋 Last few local records:")
                start_idx = max(0, len(local_data) - 3)
                for i in range(start_idx, len(local_data)):
                    row = local_data.iloc[i]
                    print(f"   Record {i+1}: Contact ID='{row.get('Contact ID', 'N/A')}', "
                          f"Opp ID='{row.get('Opportunity ID', 'N/A')}', "
                          f"Date='{row.get('Date Created', 'N/A')}'")

                print(f"\n💡 POSSIBLE CAUSES:")
                print(f"   1. Data format differences (spaces, case, encoding)")
                print(f"   2. The last Airtable record is not in the local file")
                print(f"   3. Records were modified after upload")
                print(f"   4. Data corruption or sync issues")

                return -1, None  # Indicate no match found

            elif data_type == 'google_ads':
                # Enhanced Google Ads matching (keeping existing logic but with more logging)
                target_date = last_airtable_record.get('Date')
                target_campaign_id = last_airtable_record.get('Campaign ID')
                target_campaign_name = last_airtable_record.get('Campaign Name')

                print(f"🔍 Looking for Google Ads record with:")
                print(f"   Date: {target_date}")
                print(f"   Campaign ID: {target_campaign_id}")
                print(f"   Campaign Name: {target_campaign_name}")

                for idx, row in local_data.iterrows():
                    local_date = str(row.get('Date', '')) if pd.notna(row.get('Date', '')) else ''
                    local_campaign_id = str(row.get('Campaign ID', '')) if pd.notna(row.get('Campaign ID', '')) else ''
                    local_campaign_name = str(row.get('Campaign Name', '')) if pd.notna(row.get('Campaign Name', '')) else ''

                    if (str(target_date) == local_date and
                        str(target_campaign_id) == local_campaign_id and
                        str(target_campaign_name) == local_campaign_name):
                        print(f"✅ Found exact match at position {idx + 1}")
                        return idx + 1, row

            print(f"❌ No matching record found in local data")
            return -1, None

        except Exception as e:
            print(f"❌ Error in enhanced position finding: {str(e)}")
            import traceback
            traceback.print_exc()
            return -1, None

    def delete_records(self, record_ids):
        """Delete records from Airtable"""
        if not self.api_key or not record_ids:
            return False

        try:
            url = f"{AIRTABLE_API_URL}/{self.base_id}/{self.google_ads_table_id}"

            # Delete in batches of 10 (Airtable limit)
            for i in range(0, len(record_ids), 10):
                batch_ids = record_ids[i:i+10]
                params = {'records[]': batch_ids}
                response = requests.delete(url, headers=self.headers, params=params)

                if response.status_code != 200:
                    return False

            return True
        except Exception:
            return False

class GoogleSheetsManager:
    """Google Sheets API integration manager"""

    def __init__(self):
        self.service = None
        self.drive_service = None
        self.credentials = None
        self.token_file = "sheets_token.json"
        self.credentials_file = "user.json"

    def authenticate(self):
        """Authenticate with Google Sheets API using OAuth"""
        creds = None

        # Load existing token
        if os.path.exists(self.token_file):
            try:
                creds = Credentials.from_authorized_user_file(self.token_file, SHEETS_SCOPES)
            except Exception as e:
                print(f"Error loading existing token: {e}")

        # If no valid credentials, run OAuth flow
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                except Exception as e:
                    print(f"Error refreshing token: {e}")
                    creds = None

            if not creds:
                if not os.path.exists(self.credentials_file):
                    raise FileNotFoundError(
                        f"Credentials file '{self.credentials_file}' not found. "
                        "Please download it from Google Cloud Console."
                    )

                # Handle both web and installed app credentials
                try:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, SHEETS_SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                except Exception as e:
                    # If that fails, try treating it as web credentials
                    import json
                    with open(self.credentials_file, 'r') as f:
                        cred_data = json.load(f)

                    # Convert web credentials to installed app format
                    if 'web' in cred_data:
                        web_creds = cred_data['web']
                        installed_creds = {
                            'installed': {
                                'client_id': web_creds['client_id'],
                                'client_secret': web_creds['client_secret'],
                                'auth_uri': web_creds['auth_uri'],
                                'token_uri': web_creds['token_uri'],
                                'redirect_uris': ['http://localhost']
                            }
                        }

                        # Create temporary file with correct format
                        temp_file = 'temp_credentials.json'
                        with open(temp_file, 'w') as f:
                            json.dump(installed_creds, f)

                        flow = InstalledAppFlow.from_client_secrets_file(
                            temp_file, SHEETS_SCOPES
                        )
                        creds = flow.run_local_server(port=0)

                        # Clean up temp file
                        os.remove(temp_file)
                    else:
                        raise e

            # Save credentials for next run
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())

        self.credentials = creds
        self.service = build('sheets', 'v4', credentials=creds)

        # Try to build Drive service, but don't fail if it's not available
        try:
            self.drive_service = build('drive', 'v3', credentials=creds)
        except Exception as e:
            print(f"Warning: Drive API not available: {e}")
            self.drive_service = None

        return True

    def get_spreadsheets(self):
        """Get list of all accessible spreadsheets"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        # If Drive API is not available, return empty list (will trigger manual entry)
        if not self.drive_service:
            print("Drive API not available - manual spreadsheet entry required")
            return []

        try:
            # First, test if Drive API is working with a simple call
            try:
                # Test Drive API access
                self.drive_service.files().list(pageSize=1).execute()
            except Exception as drive_error:
                if "has not been used" in str(drive_error) or "is disabled" in str(drive_error):
                    raise Exception(
                        "Google Drive API is not enabled. Please:\n"
                        "1. Go to Google Cloud Console\n"
                        "2. Navigate to APIs & Services → Library\n"
                        "3. Search for 'Google Drive API'\n"
                        "4. Click 'ENABLE'\n"
                        "5. Wait 2-3 minutes and try again"
                    )
                else:
                    raise drive_error

            # Query for spreadsheets
            query = "mimeType='application/vnd.google-apps.spreadsheet'"
            results = self.drive_service.files().list(
                q=query,
                pageSize=100,
                fields="nextPageToken, files(id, name, owners, shared, createdTime, modifiedTime)"
            ).execute()

            spreadsheets = results.get('files', [])

            # Add ownership info
            for sheet in spreadsheets:
                owners = sheet.get('owners', [])
                if owners:
                    sheet['owner_name'] = owners[0].get('displayName', 'Unknown')
                    sheet['is_owned'] = owners[0].get('me', False)
                else:
                    sheet['owner_name'] = 'Unknown'
                    sheet['is_owned'] = False

            return spreadsheets

        except Exception as e:
            raise Exception(f"Error fetching spreadsheets: {str(e)}")

    # Removed duplicate method - using the enhanced version below

    def get_sheet_data(self, spreadsheet_id, worksheet_name):
        """Get data from a specific worksheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            # Read all data from the worksheet
            range_name = f"{worksheet_name}!A:Z"  # Read columns A to Z
            result = self.service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()

            values = result.get('values', [])
            return values

        except Exception as e:
            raise Exception(f"Error reading sheet data: {str(e)}")

    def read_sheet_data(self, spreadsheet_id, range_name):
        """Read data from a specific range in a spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()

            values = result.get('values', [])
            return values

        except Exception as e:
            raise Exception(f"Error reading sheet data: {str(e)}")

    def get_worksheets(self, spreadsheet_id):
        """Get list of worksheets in a spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()

            sheets = []
            for sheet in spreadsheet.get('sheets', []):
                properties = sheet.get('properties', {})
                sheets.append({
                    'id': properties.get('sheetId'),
                    'title': properties.get('title'),
                    'index': properties.get('index'),
                    'row_count': properties.get('gridProperties', {}).get('rowCount', 0),
                    'column_count': properties.get('gridProperties', {}).get('columnCount', 0)
                })

            return sheets

        except Exception as e:
            raise Exception(f"Error fetching worksheets: {str(e)}")

    def read_sheet_data(self, spreadsheet_id, range_name):
        """Read data from a specific range in a spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()

            values = result.get('values', [])
            return values

        except Exception as e:
            raise Exception(f"Error reading sheet data: {str(e)}")

    def write_sheet_data(self, spreadsheet_id, range_name, values, value_input_option='RAW'):
        """Write data to a specific range in a spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            body = {
                'values': values
            }

            result = self.service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                body=body
            ).execute()

            return result

        except Exception as e:
            raise Exception(f"Error writing sheet data: {str(e)}")

    def append_sheet_data(self, spreadsheet_id, range_name, values, value_input_option='RAW'):
        """Append data to a spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            body = {
                'values': values
            }

            result = self.service.spreadsheets().values().append(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption=value_input_option,
                insertDataOption='INSERT_ROWS',
                body=body
            ).execute()

            return result

        except Exception as e:
            raise Exception(f"Error appending sheet data: {str(e)}")

    def create_spreadsheet(self, title):
        """Create a new spreadsheet"""
        if not self.service:
            raise Exception("Not authenticated. Call authenticate() first.")

        try:
            spreadsheet = {
                'properties': {
                    'title': title
                }
            }

            result = self.service.spreadsheets().create(
                body=spreadsheet,
                fields='spreadsheetId'
            ).execute()

            return result.get('spreadsheetId')

        except Exception as e:
            raise Exception(f"Error creating spreadsheet: {str(e)}")

class ClientManager:
    """Client-specific file and folder management"""

    def __init__(self, base_path="."):
        self.base_path = base_path
        self.clients_folder = os.path.join(base_path, "clients")
        self.ensure_clients_folder()

    def ensure_clients_folder(self):
        """Ensure the clients folder exists"""
        if not os.path.exists(self.clients_folder):
            os.makedirs(self.clients_folder)
            print(f"📁 Created clients folder: {self.clients_folder}")

    def get_client_folder(self, client_name):
        """Get the folder path for a specific client"""
        # Sanitize client name for folder creation
        safe_name = self.sanitize_folder_name(client_name)
        return os.path.join(self.clients_folder, safe_name)

    def sanitize_folder_name(self, name):
        """Sanitize a name to be safe for folder creation"""
        import re
        # Remove or replace invalid characters
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', name)
        safe_name = safe_name.strip('. ')  # Remove leading/trailing dots and spaces
        return safe_name if safe_name else "unnamed_client"

    def create_client_folder(self, client_name):
        """Create a folder for a client if it doesn't exist"""
        client_folder = self.get_client_folder(client_name)
        if not os.path.exists(client_folder):
            os.makedirs(client_folder)
            print(f"📁 Created client folder: {client_folder}")

            # Create subfolders for organization
            subfolders = ["exports", "data", "reports", "temp"]
            for subfolder in subfolders:
                subfolder_path = os.path.join(client_folder, subfolder)
                os.makedirs(subfolder_path, exist_ok=True)

            return True
        return False

    def get_existing_client_folders(self):
        """Scan and return list of existing client folders"""
        if not os.path.exists(self.clients_folder):
            return []

        existing_clients = []
        for item in os.listdir(self.clients_folder):
            item_path = os.path.join(self.clients_folder, item)
            if os.path.isdir(item_path):
                existing_clients.append(item)

        return sorted(existing_clients)

    def get_client_file_path(self, client_name, filename, subfolder="exports"):
        """Get the full path for a client-specific file"""
        client_folder = self.get_client_folder(client_name)
        subfolder_path = os.path.join(client_folder, subfolder)

        # Ensure subfolder exists
        os.makedirs(subfolder_path, exist_ok=True)

        return os.path.join(subfolder_path, filename)

    def list_client_files(self, client_name, subfolder="exports"):
        """List all files for a specific client in a subfolder"""
        client_folder = self.get_client_folder(client_name)
        subfolder_path = os.path.join(client_folder, subfolder)

        if not os.path.exists(subfolder_path):
            return []

        files = []
        for item in os.listdir(subfolder_path):
            item_path = os.path.join(subfolder_path, item)
            if os.path.isfile(item_path):
                files.append({
                    'name': item,
                    'path': item_path,
                    'size': os.path.getsize(item_path),
                    'modified': os.path.getmtime(item_path)
                })

        return sorted(files, key=lambda x: x['modified'], reverse=True)

class ConfigManager:
    """Enhanced configuration manager with validation"""

    def __init__(self, config_file=CONFIG_FILE):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """Load configuration from file with validation"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    return self.validate_config(config)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.default_config()
        return self.default_config()

    def validate_config(self, config):
        """Validate configuration structure"""
        default = self.default_config()

        # Ensure all required keys exist
        for key in default:
            if key not in config:
                config[key] = default[key]

        # Validate credentials structure
        if "credentials" not in config or not isinstance(config["credentials"], dict):
            config["credentials"] = default["credentials"]

        for cred_key in default["credentials"]:
            if cred_key not in config["credentials"]:
                config["credentials"][cred_key] = default["credentials"][cred_key]

        return config

    def save_config(self):
        """Save configuration to file with backup"""
        try:
            # Create backup
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup"
                with open(self.config_file, 'r') as src, open(backup_file, 'w') as dst:
                    dst.write(src.read())

            # Save new config
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False

    def default_config(self):
        """Return default configuration"""
        return {
            "credentials": {
                "customer_id": "",
                "developer_token": "",
                "refresh_token": "",
                "manager_account_id": "**********"
            },
            "google_ads_sync": {
                "api_key": "",
                "base_id": AIRTABLE_BASE_ID,
                "table_id": AIRTABLE_GOOGLE_ADS_TABLE_ID,
                "auto_sync": False,
                "sync_mode": "incremental"
            },
            "ghl_sync": {
                "api_key": "",
                "base_id": AIRTABLE_BASE_ID,
                "table_id": "tblcdFVUC3zJrbmNf",
                "auto_sync": False,
                "sync_mode": "incremental"
            },
            "clients": [],
            "saved_sheets": [],
            "last_date_preset": "Last 30 days",
            "last_export_format": "csv",
            "scheduled_tasks": [],
            "window_geometry": "1400x900",
            "theme": "flatly",
            "auto_save": True,
            "show_tooltips": True
        }

class ModernGoogleAdsExtractor:
    """Modern Google Ads Data Extractor with enhanced UI"""

    def __init__(self, master):
        self.master = master
        self.config_manager = ConfigManager()

        # Initialize Client Manager for file organization
        self.client_manager = ClientManager()

        # Initialize Airtable manager
        airtable_api_key = self.config_manager.config.get("airtable", {}).get("api_key", "")
        self.airtable_manager = AirtableManager(airtable_api_key)

        # Initialize theme
        self.theme = self.config_manager.config.get("theme", "flatly")
        self.style = ttk_bootstrap.Style(theme=self.theme)

        # Current selected client
        self.current_client = None

        # Configure main window
        master.title("🚀 Repair Lift Attribution Dashboards Data Collector - Professional Edition")
        master.geometry(self.config_manager.config.get("window_geometry", "1400x900"))
        master.minsize(1200, 700)

        # Set window icon (if available)
        try:
            master.iconbitmap("icon.ico")
        except:
            pass

        # Configure window closing
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize variables
        self.ad_spend_data = None
        self.credentials = None
        self.scheduler_thread = None
        self.current_progress = None

        # Create UI
        self.setup_menu()
        self.setup_main_ui()
        self.setup_status_bar()

        # Load settings
        self.load_settings()

        # Start scheduler
        self.start_scheduler()

        # Log initial message
        self.log("🚀 Repair Lift Attribution Dashboards Data Collector started successfully!")
        self.log(f"📊 Theme: {self.theme.title()}")
        self.log(f"🔑 Client ID: {CLIENT_ID[:10]}... is pre-configured")
        self.log(f"🔐 Client Secret: {CLIENT_SECRET[:5]}... is pre-configured")

    def create_scrollable_tab(self, parent_notebook, tab_text, debug_name=None):
        """
        Utility method to create a scrollable tab frame with proper mouse wheel handling
        Returns: (tab_frame, content_frame, canvas) where content_frame should be used for adding widgets
        """
        debug_prefix = f"[{debug_name or tab_text}]" if debug_name else f"[{tab_text}]"

        # Main tab frame
        tab_frame = ttk_bootstrap.Frame(parent_notebook)
        parent_notebook.add(tab_frame, text=tab_text)

        # Create scrollable canvas
        canvas = tk.Canvas(tab_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bootstrap.Frame(canvas)

        # Configure scrolling with debug logging
        def on_frame_configure(event):
            try:
                # Update scroll region
                canvas.configure(scrollregion=canvas.bbox("all"))
                print(f"{debug_prefix} Frame configured, scroll region updated")
            except Exception as e:
                print(f"{debug_prefix} Error in frame configure: {e}")

        scrollable_frame.bind("<Configure>", on_frame_configure)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add canvas-specific mouse wheel scrolling (NOT bind_all)
        def on_mousewheel(event):
            try:
                # Only scroll if canvas is visible and has focus area
                if canvas.winfo_viewable():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                    print(f"{debug_prefix} Mouse wheel scrolled: delta={event.delta}")
            except Exception as e:
                print(f"{debug_prefix} Error in mouse wheel: {e}")

        # Bind to canvas and its children for proper focus handling
        def bind_mousewheel_recursive(widget):
            """Recursively bind mouse wheel to widget and all its children"""
            try:
                widget.bind("<MouseWheel>", on_mousewheel)
                for child in widget.winfo_children():
                    bind_mousewheel_recursive(child)
            except Exception as e:
                print(f"{debug_prefix} Error binding mousewheel to {widget}: {e}")

        # Bind to canvas itself
        canvas.bind("<MouseWheel>", on_mousewheel)

        # Also bind when mouse enters/leaves canvas area
        def on_enter(event):
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            print(f"{debug_prefix} Mouse entered - wheel events bound")

        def on_leave(event):
            canvas.unbind_all("<MouseWheel>")
            print(f"{debug_prefix} Mouse left - wheel events unbound")

        canvas.bind("<Enter>", on_enter)
        canvas.bind("<Leave>", on_leave)

        # Content frame with padding
        content_frame = ttk_bootstrap.Frame(scrollable_frame, padding=15)
        content_frame.pack(fill=BOTH, expand=True)

        # Bind mousewheel to content frame and its future children
        content_frame.bind("<MouseWheel>", on_mousewheel)

        print(f"{debug_prefix} Scrollable tab created successfully")
        return tab_frame, content_frame, canvas

    def bind_mousewheel_to_canvas(self, canvas, widget, debug_name="Unknown"):
        """
        Bind mouse wheel events to a specific widget for a specific canvas
        This helps with dynamically created content
        """
        debug_prefix = f"[{debug_name}]"

        def on_mousewheel(event):
            try:
                if canvas.winfo_viewable():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
                    print(f"{debug_prefix} Dynamic widget mouse wheel: delta={event.delta}")
            except Exception as e:
                print(f"{debug_prefix} Error in dynamic mouse wheel: {e}")

        try:
            widget.bind("<MouseWheel>", on_mousewheel)
            print(f"{debug_prefix} Mouse wheel bound to dynamic widget: {widget}")
        except Exception as e:
            print(f"{debug_prefix} Error binding mouse wheel to widget: {e}")

    def update_canvas_scroll_region(self, canvas, debug_name="Unknown"):
        """
        Safely update canvas scroll region to prevent visual glitching
        """
        debug_prefix = f"[{debug_name}]"

        def delayed_update():
            try:
                if canvas.winfo_exists():
                    canvas.update_idletasks()  # Ensure all widgets are rendered
                    canvas.configure(scrollregion=canvas.bbox("all"))
                    print(f"{debug_prefix} Scroll region updated safely")
            except Exception as e:
                print(f"{debug_prefix} Error updating scroll region: {e}")

        # Delay the update slightly to prevent visual glitching
        canvas.after(10, delayed_update)

    def test_scrolling_debug(self):
        """Test method to verify scrolling is working in all tabs"""
        print("\n🧪 SCROLLING DEBUG TEST:")
        print("=" * 50)

        # Test each canvas
        canvases = []
        if hasattr(self, 'google_ads_canvas'):
            canvases.append(("Google Ads", self.google_ads_canvas))
        if hasattr(self, 'ghl_canvas'):
            canvases.append(("GHL Data Hub", self.ghl_canvas))
        if hasattr(self, 'sheets_canvas'):
            canvases.append(("Google Sheets", self.sheets_canvas))

        for name, canvas in canvases:
            try:
                viewable = canvas.winfo_viewable()
                exists = canvas.winfo_exists()
                scroll_region = canvas.cget('scrollregion')
                print(f"📊 {name}:")
                print(f"   - Exists: {exists}")
                print(f"   - Viewable: {viewable}")
                print(f"   - Scroll Region: {scroll_region}")
                print(f"   - Canvas ID: {id(canvas)}")
            except Exception as e:
                print(f"❌ {name}: Error - {e}")

        print("=" * 50)
        print("💡 Try scrolling in each tab to see debug messages!")
        print("🔍 Look for '[TAB_NAME] Mouse wheel scrolled' messages")

    def setup_menu(self):
        """Setup modern menu bar"""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Configuration", command=self.new_config)
        file_menu.add_command(label="Open Configuration...", command=self.import_config)
        file_menu.add_command(label="Save Configuration", command=self.save_settings, accelerator="Ctrl+S")
        file_menu.add_command(label="Export Configuration...", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing, accelerator="Ctrl+Q")

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)

        # Theme submenu
        theme_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="Theme", menu=theme_menu)

        themes = ["flatly", "darkly", "cosmo", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"]
        for theme in themes:
            theme_menu.add_command(label=theme.title(), command=lambda t=theme: self.change_theme(t))

        view_menu.add_separator()
        view_menu.add_command(label="Clear Log", command=self.clear_log)
        view_menu.add_command(label="Refresh Data", command=self.refresh_data)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Validate Credentials", command=self.validate_credentials)
        tools_menu.add_command(label="Test API Connection", command=self.test_api_connection)
        tools_menu.add_command(label="Data Preview", command=self.preview_data)
        tools_menu.add_separator()
        tools_menu.add_command(label="Bulk Export", command=self.bulk_export)
        tools_menu.add_command(label="Schedule Manager", command=self.open_schedule_manager)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="API Documentation", command=self.open_api_docs)
        help_menu.add_separator()
        help_menu.add_command(label="🧪 Test Scrolling", command=self.test_scrolling_debug)
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)

        # Bind keyboard shortcuts
        self.master.bind('<Control-s>', lambda e: self.save_settings())
        self.master.bind('<Control-q>', lambda e: self.on_closing())
        self.master.bind('<F5>', lambda e: self.refresh_data())

    def setup_main_ui(self):
        """Setup the main user interface"""
        # Create main container with padding
        main_container = ttk_bootstrap.Frame(self.master, padding=10)
        main_container.pack(fill=BOTH, expand=True)

        # Create paned window for resizable layout
        paned_window = ttk_bootstrap.PanedWindow(main_container, orient=HORIZONTAL)
        paned_window.pack(fill=BOTH, expand=True)

        # Left panel for controls
        left_panel = ttk_bootstrap.Frame(paned_window, padding=10)
        paned_window.add(left_panel, weight=1)

        # Right panel for data and charts
        right_panel = ttk_bootstrap.Frame(paned_window, padding=10)
        paned_window.add(right_panel, weight=2)

        # Setup left panel
        self.setup_left_panel(left_panel)

        # Setup right panel
        self.setup_right_panel(right_panel)

    def setup_left_panel(self, parent):
        """Setup the left control panel"""
        # Header
        header_frame = ttk_bootstrap.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        title_label = ttk_bootstrap.Label(
            header_frame,
            text="Repair Lift Attribution Dashboards Data Collector",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack()

        subtitle_label = ttk_bootstrap.Label(
            header_frame,
            text="Professional Edition",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        subtitle_label.pack()

        # Create notebook for organized tabs
        self.notebook = ttk_bootstrap.Notebook(parent, bootstyle="primary")
        self.notebook.pack(fill=BOTH, expand=True)

        # Setup tabs
        self.setup_credentials_tab()
        self.setup_data_tab()
        self.setup_export_tab()
        self.setup_google_ads_sync_tab()
        self.setup_ghl_sync_tab()
        self.setup_google_sheets_tab()
        self.setup_schedule_tab()
        self.setup_settings_tab()

    def setup_credentials_tab(self):
        """Setup Google Ads API credentials configuration tab"""
        creds_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(creds_frame, text="🔐 Credentials")

        # Info label to clarify purpose
        info_label = ttk_bootstrap.Label(
            creds_frame,
            text="🔐 Google Ads API Authentication - Configure credentials for Google Ads data extraction",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # Credentials section
        creds_section = ttk_bootstrap.LabelFrame(creds_frame, text="Google Ads API Credentials", padding=15)
        creds_section.pack(fill=X, pady=(0, 15))

        # Note: Customer ID moved to Google Ads Data Hub tab for better workflow integration

        # Developer Token
        ttk_bootstrap.Label(creds_section, text="Developer Token:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.developer_token_var = tk.StringVar()
        dev_token_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.developer_token_var, width=30, show="*", font=('Arial', 10))
        dev_token_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(dev_token_entry, "Your Google Ads API developer token")

        # Refresh Token
        ttk_bootstrap.Label(creds_section, text="Refresh Token:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.refresh_token_var = tk.StringVar()
        refresh_token_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.refresh_token_var, width=30, show="*", font=('Arial', 10))
        refresh_token_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(refresh_token_entry, "OAuth refresh token for API access")

        # Manager Account ID
        ttk_bootstrap.Label(creds_section, text="Manager Account:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky='w', pady=5)
        self.manager_account_var = tk.StringVar(value="**********")
        manager_entry = ttk_bootstrap.Entry(creds_section, textvariable=self.manager_account_var, width=30, font=('Arial', 10))
        manager_entry.grid(row=3, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(manager_entry, "Your Google Ads manager account ID")

        # Configure grid weights
        creds_section.columnconfigure(1, weight=1)

        # OAuth button
        oauth_btn = ttk_bootstrap.Button(
            creds_section,
            text="🔑 Get Google Ads OAuth Tokens",
            command=self.get_oauth_tokens,
            bootstyle="success-outline",
            width=25
        )
        oauth_btn.grid(row=4, column=0, columnspan=2, pady=15)

        # Validation button
        validate_btn = ttk_bootstrap.Button(
            creds_section,
            text="✓ Validate Credentials",
            command=self.validate_credentials,
            bootstyle="info-outline",
            width=20
        )
        validate_btn.grid(row=5, column=0, columnspan=2, pady=5)

        # Configuration status and instructions
        config_frame = ttk_bootstrap.LabelFrame(creds_frame, text="⚙️ Configuration Status", padding=15)
        config_frame.pack(fill=X, pady=(15, 0))

        # Check if Google Ads credentials are configured
        if GOOGLE_ADS_CLIENT_ID == "YOUR_GOOGLE_ADS_CLIENT_ID_HERE":
            status_text = (
                "❌ Google Ads API credentials are NOT configured\n\n"
                "🔧 Required Setup:\n"
                "1. Create a Google Cloud project for Google Ads API\n"
                "2. Enable Google Ads API in the project\n"
                "3. Create OAuth credentials for 'Desktop Application'\n"
                "4. Update GOOGLE_ADS_CLIENT_ID and GOOGLE_ADS_CLIENT_SECRET in the code\n\n"
                "📋 Current status: Using placeholder credentials\n"
                "🚨 OAuth authentication will not work until real credentials are provided"
            )
            bootstyle = "danger"
        else:
            status_text = (
                "✅ Google Ads API credentials are configured\n\n"
                "💡 Google Sheets authentication is handled separately in the 'Google Sheets' tab.\n"
                "This credentials tab is only for Google Ads API access."
            )
            bootstyle = "success"

        status_label = ttk_bootstrap.Label(
            config_frame,
            text=status_text,
            font=('Arial', 10),
            bootstyle=bootstyle,
            wraplength=600
        )
        status_label.pack()

    def setup_data_tab(self):
        """Setup data configuration tab"""
        data_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(data_frame, text="📊 Data")

        # Note: Date range selection moved to Google Ads Data Hub tab for unified workflow
        info_section = ttk_bootstrap.LabelFrame(data_frame, text="📋 Tab Information", padding=15)
        info_section.pack(fill=X, pady=(0, 15))

        ttk_bootstrap.Label(
            info_section,
            text="📊 Google Ads data extraction and date range selection have been moved to the 'Google Ads Data Hub' tab for a unified workflow.\n\n"
                 "This tab now focuses on client management and general data operations.",
            font=('Arial', 10),
            bootstyle="info",
            justify="left"
        ).pack(fill=X)

        # Client management section
        client_section = ttk_bootstrap.LabelFrame(data_frame, text="Client Management", padding=15)
        client_section.pack(fill=X, pady=(0, 15))

        # Client selection
        ttk_bootstrap.Label(client_section, text="Select Client:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.client_var = tk.StringVar()
        self.client_combo = ttk_bootstrap.Combobox(
            client_section,
            textvariable=self.client_var,
            state="readonly",
            width=30,
            font=('Arial', 10)
        )
        self.client_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        self.client_combo.bind('<<ComboboxSelected>>', self.on_client_select)

        # Client management buttons
        client_btn_frame = ttk_bootstrap.Frame(client_section)
        client_btn_frame.grid(row=1, column=0, columnspan=2, pady=10)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="➕ Add Client",
            command=self.add_client_dialog,
            bootstyle="success-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="✏️ Edit Client",
            command=self.edit_client_dialog,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            client_btn_frame,
            text="🗑️ Remove Client",
            command=self.remove_client_dialog,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        client_section.columnconfigure(1, weight=1)

        # Action buttons
        action_frame = ttk_bootstrap.Frame(data_frame)
        action_frame.pack(fill=X, pady=15)

        extract_btn = ttk_bootstrap.Button(
            action_frame,
            text="🚀 Extract Aggregated",
            command=self.start_extraction,
            bootstyle="primary",
            width=20
        )
        extract_btn.pack(side=LEFT, padx=5)

        load_btn = ttk_bootstrap.Button(
            action_frame,
            text="📂 Load CSV Data",
            command=self.load_csv_data,
            bootstyle="warning",
            width=20
        )
        load_btn.pack(side=LEFT, padx=5)

        preview_btn = ttk_bootstrap.Button(
            action_frame,
            text="👁️ Preview Data",
            command=self.preview_data,
            bootstyle="info-outline",
            width=20
        )
        preview_btn.pack(side=LEFT, padx=5)

        # Add granular conversion extraction button
        granular_btn = ttk_bootstrap.Button(
            action_frame,
            text="🎯 Granular Conversions",
            command=self.extract_granular_conversion_data,
            bootstyle="success",
            width=20
        )
        granular_btn.pack(side=LEFT, padx=5)

        # Add comparison analysis button
        compare_btn = ttk_bootstrap.Button(
            action_frame,
            text="🔍 Compare Data",
            command=self.compare_aggregated_vs_granular_conversions,
            bootstyle="warning",
            width=20
        )
        compare_btn.pack(side=LEFT, padx=5)

    def setup_export_tab(self):
        """Setup export configuration tab"""
        export_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(export_frame, text="📤 Export")

        # Export format section
        format_section = ttk_bootstrap.LabelFrame(export_frame, text="Export Format", padding=15)
        format_section.pack(fill=X, pady=(0, 15))

        self.export_format_var = tk.StringVar(value="csv")

        # Format options with icons
        formats = [
            ("📄 CSV", "csv", "Comma-separated values file"),
            ("📊 Excel", "excel", "Microsoft Excel spreadsheet"),
            ("🌐 Google Sheets", "sheets", "Export directly to Google Sheets")
        ]

        for i, (text, value, tooltip) in enumerate(formats):
            radio = ttk_bootstrap.Radiobutton(
                format_section,
                text=text,
                variable=self.export_format_var,
                value=value,
                bootstyle="primary"
            )
            radio.grid(row=i, column=0, sticky='w', pady=5)
            ModernTooltip(radio, tooltip)

        # Google Sheets section
        sheets_section = ttk_bootstrap.LabelFrame(export_frame, text="Google Sheets Options", padding=15)
        sheets_section.pack(fill=X, pady=(0, 15))

        # Sheet ID
        ttk_bootstrap.Label(sheets_section, text="Sheet ID:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.sheet_id_var = tk.StringVar()
        sheet_id_entry = ttk_bootstrap.Entry(sheets_section, textvariable=self.sheet_id_var, width=40, font=('Arial', 10))
        sheet_id_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(sheet_id_entry, "Leave blank to create a new spreadsheet")

        # Sheet name
        ttk_bootstrap.Label(sheets_section, text="Sheet Name:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.sheet_name_var = tk.StringVar(value="Ad Spend Data")
        sheet_name_entry = ttk_bootstrap.Entry(sheets_section, textvariable=self.sheet_name_var, width=40, font=('Arial', 10))
        sheet_name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        sheets_section.columnconfigure(1, weight=1)

        # Export buttons
        export_btn_frame = ttk_bootstrap.Frame(export_frame)
        export_btn_frame.pack(fill=X, pady=15)

        export_btn = ttk_bootstrap.Button(
            export_btn_frame,
            text="📤 Export Data",
            command=self.export_data,
            bootstyle="success",
            width=20
        )
        export_btn.pack(side=LEFT, padx=5)

        bulk_export_btn = ttk_bootstrap.Button(
            export_btn_frame,
            text="📦 Bulk Export",
            command=self.bulk_export,
            bootstyle="warning-outline",
            width=20
        )
        bulk_export_btn.pack(side=LEFT, padx=5)

    def setup_google_ads_sync_tab(self):
        """Setup unified Google Ads Data Hub tab"""
        # Use the improved scrollable tab utility
        tab_frame, content_frame, self.google_ads_canvas = self.create_scrollable_tab(
            self.notebook,
            "📊 Google Ads Data Hub",
            debug_name="GOOGLE_ADS_HUB"
        )

        # Enhanced Google Ads info with unified workflow
        info_label = ttk_bootstrap.Label(
            content_frame,
            text="📊 Google Ads Data Hub - Extract, validate, and sync Google Ads data with client-specific organization",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # Section 1: Client Context & Authentication
        client_context_section = ttk_bootstrap.LabelFrame(content_frame, text="📋 Client Context & Authentication", padding=15)
        client_context_section.pack(fill=X, pady=(0, 15))

        # Client selection and customer ID row
        client_row_frame = ttk_bootstrap.Frame(client_context_section)
        client_row_frame.pack(fill=X, pady=(0, 10))

        # Current client display (will be populated by existing client selection)
        ttk_bootstrap.Label(client_row_frame, text="Current Client:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.google_ads_current_client_label = ttk_bootstrap.Label(
            client_row_frame,
            text="No client selected",
            font=('Arial', 10),
            bootstyle="warning"
        )
        self.google_ads_current_client_label.pack(side=LEFT, padx=(10, 20))

        # Customer ID (moved from Credentials tab)
        ttk_bootstrap.Label(client_row_frame, text="Customer ID:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.customer_id_var = tk.StringVar()  # Moved from Credentials tab
        customer_id_entry = ttk_bootstrap.Entry(
            client_row_frame,
            textvariable=self.customer_id_var,
            width=20,
            font=('Arial', 10)
        )
        customer_id_entry.pack(side=LEFT, padx=(10, 20))
        ModernTooltip(customer_id_entry, "Format: 123-456-7890")

        # API connection test button
        self.google_ads_test_api_btn = ttk_bootstrap.Button(
            client_row_frame,
            text="🔗 Test API",
            command=self.test_api_connection,  # Will be moved here
            bootstyle="info-outline",
            width=12
        )
        self.google_ads_test_api_btn.pack(side=RIGHT, padx=5)

        # Client folder info
        self.google_ads_client_folder_label = ttk_bootstrap.Label(
            client_context_section,
            text="📁 Client folder: Not selected | 📊 Table: Not configured | ❌ API: Not tested",
            font=('Arial', 9),
            bootstyle="secondary"
        )
        self.google_ads_client_folder_label.pack(fill=X, pady=(5, 0))

        # Section 2: Data Processing & Extraction
        processing_section = ttk_bootstrap.LabelFrame(content_frame, text="🔍 Data Processing & Extraction", padding=15)
        processing_section.pack(fill=X, pady=(0, 15))

        # Data processing actions - first row
        processing_actions_frame = ttk_bootstrap.Frame(processing_section)
        processing_actions_frame.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="📂 Load & Preview File",
            command=self.load_and_preview_google_ads_data,  # Will be implemented
            bootstyle="info",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="📊 Extract from API",
            command=self.start_extraction,  # Will be moved here
            bootstyle="primary",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="🔍 Validate Data",
            command=self.validate_google_ads_data,  # Will be implemented
            bootstyle="warning",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="👁️ Preview Data",
            command=self.preview_data,  # Existing method
            bootstyle="secondary",
            width=20
        ).pack(side=LEFT, padx=5)

        # Date range selection (moved from Data tab)
        date_range_frame = ttk_bootstrap.Frame(processing_section)
        date_range_frame.pack(fill=X, pady=(10, 0))

        # Date preset and range controls
        ttk_bootstrap.Label(date_range_frame, text="Date Range:", font=('Arial', 10, 'bold')).pack(side=LEFT)

        # Date preset dropdown (moved from Data tab)
        self.date_preset_var = tk.StringVar()
        date_preset_combo = ttk_bootstrap.Combobox(
            date_range_frame,
            textvariable=self.date_preset_var,
            values=list(DATE_PRESETS.keys()),
            state="readonly",
            width=15,
            font=('Arial', 9)
        )
        date_preset_combo.pack(side=LEFT, padx=(10, 5))
        date_preset_combo.bind('<<ComboboxSelected>>', self.on_date_preset_change)
        ModernTooltip(date_preset_combo, "Select a preset date range")

        # Custom date range with default values
        ttk_bootstrap.Label(date_range_frame, text="From:", font=('Arial', 9)).pack(side=LEFT, padx=(10, 5))

        # Set default start date (30 days ago)
        default_start = datetime.datetime.now() - datetime.timedelta(days=30)
        self.start_date_var = tk.StringVar(value=default_start.strftime('%Y-%m-%d'))
        start_date_entry = ttk_bootstrap.Entry(date_range_frame, textvariable=self.start_date_var, width=12, font=('Arial', 9))
        start_date_entry.pack(side=LEFT, padx=(0, 5))
        ModernTooltip(start_date_entry, "Start date (YYYY-MM-DD)")

        ttk_bootstrap.Label(date_range_frame, text="To:", font=('Arial', 9)).pack(side=LEFT, padx=(5, 5))

        # Set default end date (today)
        default_end = datetime.datetime.now()
        self.end_date_var = tk.StringVar(value=default_end.strftime('%Y-%m-%d'))
        end_date_entry = ttk_bootstrap.Entry(date_range_frame, textvariable=self.end_date_var, width=12, font=('Arial', 9))
        end_date_entry.pack(side=LEFT, padx=(0, 5))
        ModernTooltip(end_date_entry, "End date (YYYY-MM-DD)")

        # Section 3: Data Status & Validation
        status_section = ttk_bootstrap.LabelFrame(content_frame, text="📊 Data Status & Validation", padding=15)
        status_section.pack(fill=X, pady=(0, 15))

        # Data validation status
        validation_status_frame = ttk_bootstrap.Frame(status_section)
        validation_status_frame.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Label(validation_status_frame, text="Validation Status:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.google_ads_validation_status = ttk_bootstrap.Label(
            validation_status_frame,
            text="No data loaded",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        self.google_ads_validation_status.pack(side=LEFT, padx=(10, 0))

        # Data summary
        summary_frame = ttk_bootstrap.Frame(status_section)
        summary_frame.pack(fill=X)

        ttk_bootstrap.Label(summary_frame, text="Data Summary:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.google_ads_data_summary = ttk_bootstrap.Label(
            summary_frame,
            text="No data available",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        self.google_ads_data_summary.pack(side=LEFT, padx=(10, 0))

        # Section 4: Airtable Configuration (enhanced existing section)
        api_section = ttk_bootstrap.LabelFrame(content_frame, text="⚙️ Airtable Configuration", padding=15)
        api_section = ttk_bootstrap.LabelFrame(content_frame, text="API Configuration", padding=15)
        api_section.pack(fill=X, pady=(0, 15))

        # API Key
        ttk_bootstrap.Label(api_section, text="Airtable API Key:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.airtable_api_key_var = tk.StringVar()
        api_key_entry = ttk_bootstrap.Entry(api_section, textvariable=self.airtable_api_key_var, width=40, show="*", font=('Arial', 10))
        api_key_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(api_key_entry, "Your Airtable Personal Access Token")

        # Base ID (editable for Google Ads)
        ttk_bootstrap.Label(api_section, text="Base ID:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.google_ads_base_id_var = tk.StringVar(value=AIRTABLE_BASE_ID)
        base_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.google_ads_base_id_var, width=40, font=('Arial', 10))
        base_id_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(base_id_entry, "Airtable Base ID for Google Ads data")

        # Table ID (editable for Google Ads)
        ttk_bootstrap.Label(api_section, text="Google Ads Table ID:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.google_ads_table_id_var = tk.StringVar(value=AIRTABLE_GOOGLE_ADS_TABLE_ID)
        table_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.google_ads_table_id_var, width=40, font=('Arial', 10))
        table_id_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(table_id_entry, "Google Ads Table ID in Airtable")

        api_section.columnconfigure(1, weight=1)

        # Test connection button
        test_btn = ttk_bootstrap.Button(
            api_section,
            text="🔗 Test Connection",
            command=self.test_airtable_connection,
            bootstyle="info-outline",
            width=20
        )
        test_btn.grid(row=3, column=0, columnspan=2, pady=15)

        # Sync Options section
        sync_section = ttk_bootstrap.LabelFrame(content_frame, text="Sync Options", padding=15)
        sync_section.pack(fill=X, pady=(0, 15))

        # Sync mode selection
        ttk_bootstrap.Label(sync_section, text="Sync Mode:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.google_ads_sync_mode_var = tk.StringVar(value="incremental")

        sync_modes = [
            ("🔄 Incremental (Content-based)", "incremental"),
            ("⚡ Smart Incremental (Position-based)", "smart_incremental"),
            ("➕ Append (Add all records)", "append"),
            ("🔄 Replace (Clear table first)", "replace")
        ]

        for i, (text, value) in enumerate(sync_modes):
            radio = ttk_bootstrap.Radiobutton(
                sync_section,
                text=text,
                variable=self.google_ads_sync_mode_var,
                value=value,
                bootstyle="primary"
            )
            radio.grid(row=i+1, column=0, sticky='w', pady=2, padx=20)

        # Mode descriptions
        mode_info = ttk_bootstrap.Label(
            sync_section,
            text="💡 Content-based: Compares all records | Smart: Finds last record position (faster)",
            font=('Arial', 9),
            bootstyle="info"
        )
        mode_info.grid(row=4, column=0, sticky='w', pady=(10, 5))

        # Auto-sync option
        self.google_ads_auto_sync_var = tk.BooleanVar()
        auto_sync_check = ttk_bootstrap.Checkbutton(
            sync_section,
            text="Auto-sync after data extraction",
            variable=self.google_ads_auto_sync_var,
            bootstyle="primary"
        )
        auto_sync_check.grid(row=5, column=0, sticky='w', pady=5)

        # Section 5: Client-Specific Actions (enhanced)
        actions_section = ttk_bootstrap.LabelFrame(content_frame, text="🔄 Client-Specific Actions", padding=15)
        actions_section.pack(fill=X, pady=(0, 15))

        # Primary action buttons (first row)
        sync_btn_frame = ttk_bootstrap.Frame(actions_section)
        sync_btn_frame.pack(fill=X)

        # Store button references for state management
        self.google_ads_sync_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="🔄 Sync to Client Airtable",
            command=self.sync_google_ads_to_airtable,
            bootstyle="success",
            width=22,
            state="disabled"  # Start disabled
        )
        self.google_ads_sync_btn.pack(side=LEFT, padx=5)

        self.google_ads_save_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="💾 Save to Client Folder",
            command=self.save_google_ads_to_client_folder,
            bootstyle="primary",
            width=22,
            state="disabled"  # Start disabled
        )
        self.google_ads_save_btn.pack(side=LEFT, padx=5)

        self.google_ads_view_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="📊 View Client Airtable",
            command=self.view_google_ads_airtable,
            bootstyle="info-outline",
            width=22,
            state="disabled"  # Start disabled
        )
        self.google_ads_view_btn.pack(side=LEFT, padx=5)

        # Second row of buttons for advanced operations
        advanced_btn_frame = ttk_bootstrap.Frame(actions_section)
        advanced_btn_frame.pack(fill=X, pady=(10, 0))

        self.google_ads_export_btn = ttk_bootstrap.Button(
            advanced_btn_frame,
            text="📤 Export to Excel",
            command=self.export_google_ads_for_client,
            bootstyle="info",
            width=22,
            state="disabled"  # Start disabled
        )
        self.google_ads_export_btn.pack(side=LEFT, padx=5)

        # Add granular conversion export button
        self.granular_export_btn = ttk_bootstrap.Button(
            advanced_btn_frame,
            text="🎯 Export Granular",
            command=self.export_granular_conversion_data,
            bootstyle="success",
            width=22,
            state="disabled"  # Start disabled
        )
        self.granular_export_btn.pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            advanced_btn_frame,
            text="🔍 Validate Sync Status",
            command=self.validate_google_ads_data,  # Will be implemented
            bootstyle="warning-outline",
            width=22
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            advanced_btn_frame,
            text="📈 Generate Report",
            command=self.generate_google_ads_report,  # Will be implemented
            bootstyle="secondary-outline",
            width=22
        ).pack(side=LEFT, padx=5)

        # Section 6: Advanced Operations
        advanced_section = ttk_bootstrap.LabelFrame(content_frame, text="🛠️ Advanced Operations", padding=15)
        advanced_section.pack(fill=X, pady=(0, 15))

        # Advanced operations buttons
        advanced_ops_frame = ttk_bootstrap.Frame(advanced_section)
        advanced_ops_frame.pack(fill=X)

        ttk_bootstrap.Button(
            advanced_ops_frame,
            text="🗑️ Clear Airtable Data",
            command=self.clear_google_ads_airtable,
            bootstyle="danger-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            advanced_ops_frame,
            text="📊 Show Statistics",
            command=self.show_google_ads_stats,
            bootstyle="secondary-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            advanced_ops_frame,
            text="🔄 Refresh Data View",
            command=self.refresh_data_view,
            bootstyle="info-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        # Section 7: Activity Log
        status_section = ttk_bootstrap.LabelFrame(content_frame, text="📋 Activity Log", padding=15)
        status_section.pack(fill=BOTH, expand=True)

        # Status text area - increased height for better readability
        self.google_ads_status_text = scrolledtext.ScrolledText(
            status_section,
            height=15,
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        self.google_ads_status_text.pack(fill=BOTH, expand=True)

        # Initialize credential variables (moved from Credentials tab)
        # These are needed for API operations but not displayed in this tab
        if not hasattr(self, 'developer_token_var'):
            self.developer_token_var = tk.StringVar()
        if not hasattr(self, 'refresh_token_var'):
            self.refresh_token_var = tk.StringVar()
        if not hasattr(self, 'manager_account_var'):
            self.manager_account_var = tk.StringVar()

        # Initialize export format variable (moved from Data tab)
        if not hasattr(self, 'export_format_var'):
            self.export_format_var = tk.StringVar(value="csv")

        # Initialize data state variables
        self.google_ads_data_loaded = False
        self.google_ads_data_source = None

        # Update client context display
        self.update_google_ads_client_context()

    def update_google_ads_client_context(self):
        """Update Google Ads client context display"""
        try:
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else "No client"

            if current_client_name and current_client_name != "No client":
                # Update current client label with enhanced visual styling
                if hasattr(self, 'google_ads_current_client_label'):
                    # Determine client status for color coding
                    client_status = self.get_client_visual_status(current_client_name)
                    self.google_ads_current_client_label.config(
                        text=f"🎯 {current_client_name}",
                        bootstyle=client_status['style']
                    )

                # Update folder info
                if hasattr(self, 'client_manager'):
                    client_folder = self.client_manager.get_client_folder(current_client_name)
                    folder_info = f"📁 Client folder: {client_folder}"

                    # Update table ID using enhanced management system
                    if hasattr(self, 'google_ads_table_id_var'):
                        # Try to retrieve existing table ID first
                        stored_table_id = self.get_client_table_id(current_client_name, "google_ads")

                        if stored_table_id:
                            # Use stored table ID
                            client_table_id = stored_table_id
                            self.google_ads_log(f"📊 Using stored table ID: {client_table_id}")
                        else:
                            # Generate new table ID and store it
                            client_table_id = self.generate_client_table_id(current_client_name, "google_ads")

                        self.google_ads_table_id_var.set(client_table_id)
                        folder_info += f" | 📊 Table: {client_table_id}"

                    # Enhanced status indicators
                    api_status = self.get_google_ads_api_status()
                    data_status = self.get_google_ads_data_status()
                    folder_status = self.get_client_folder_status(current_client_name)

                    folder_info += f" | {api_status} | {data_status} | {folder_status}"

                    if hasattr(self, 'google_ads_client_folder_label'):
                        # Enhanced folder info with visual indicators
                        enhanced_folder_info = self.enhance_folder_info_display(folder_info, current_client_name)
                        self.google_ads_client_folder_label.config(text=enhanced_folder_info)
            else:
                # No client selected - enhanced visual feedback
                if hasattr(self, 'google_ads_current_client_label'):
                    self.google_ads_current_client_label.config(
                        text="⚠️ No client selected",
                        bootstyle="warning"
                    )
                if hasattr(self, 'google_ads_client_folder_label'):
                    self.google_ads_client_folder_label.config(
                        text="📁 Folder: Not selected | 📊 Table: Not configured | ❌ API: Not tested | 📊 Data: Not loaded"
                    )

        except Exception as e:
            self.google_ads_log(f"❌ Error updating client context: {str(e)}")

    def get_client_visual_status(self, client_name):
        """
        Get visual status information for client display

        Args:
            client_name (str): Client name

        Returns:
            dict: Visual status with style and indicators
        """
        try:
            if not client_name or client_name == "No client":
                return {'style': 'warning', 'indicator': '⚠️', 'status': 'No client'}

            # Check various status indicators
            has_data = hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and not self.ad_spend_data.empty
            has_api_config = (hasattr(self, 'customer_id_var') and self.customer_id_var.get().strip() and
                            hasattr(self, 'developer_token_var') and self.developer_token_var.get().strip())
            has_airtable_config = (hasattr(self, 'airtable_api_key_var') and self.airtable_api_key_var.get().strip())

            # Determine overall status
            if has_data and has_api_config and has_airtable_config:
                return {'style': 'success', 'indicator': '✅', 'status': 'Fully configured'}
            elif has_api_config and has_airtable_config:
                return {'style': 'info', 'indicator': '🔧', 'status': 'Configured, no data'}
            elif has_api_config or has_airtable_config:
                return {'style': 'warning', 'indicator': '⚠️', 'status': 'Partially configured'}
            else:
                return {'style': 'danger', 'indicator': '❌', 'status': 'Not configured'}

        except Exception as e:
            return {'style': 'secondary', 'indicator': '❓', 'status': 'Status unknown'}

    def enhance_folder_info_display(self, folder_info, client_name):
        """
        Enhance folder info display with visual indicators and progress

        Args:
            folder_info (str): Basic folder info
            client_name (str): Client name

        Returns:
            str: Enhanced folder info with visual indicators
        """
        try:
            # Get detailed status for each component
            api_status = self.get_google_ads_api_status()
            data_status = self.get_google_ads_data_status()
            folder_status = self.get_client_folder_status(client_name)

            # Add progress indicator
            progress = self.calculate_client_progress(client_name)
            progress_bar = self.create_text_progress_bar(progress)

            # Enhanced display with color-coded components
            enhanced_info = f"{folder_info} | Progress: {progress_bar} ({progress}%)"

            return enhanced_info

        except Exception as e:
            return folder_info  # Fallback to basic info

    def calculate_client_progress(self, client_name):
        """
        Calculate overall client setup progress as percentage

        Args:
            client_name (str): Client name

        Returns:
            int: Progress percentage (0-100)
        """
        try:
            progress_items = []

            # Check client selection (20%)
            progress_items.append(client_name and client_name != "No client")

            # Check API configuration (20%)
            has_api = (hasattr(self, 'customer_id_var') and self.customer_id_var.get().strip() and
                      hasattr(self, 'developer_token_var') and self.developer_token_var.get().strip())
            progress_items.append(has_api)

            # Check Airtable configuration (20%)
            has_airtable = (hasattr(self, 'airtable_api_key_var') and self.airtable_api_key_var.get().strip())
            progress_items.append(has_airtable)

            # Check folder structure (20%)
            has_folder = False
            if hasattr(self, 'client_manager') and client_name:
                folder_path = self.client_manager.get_client_folder(client_name)
                has_folder = os.path.exists(folder_path)
            progress_items.append(has_folder)

            # Check data availability (20%)
            has_data = (hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and
                       not self.ad_spend_data.empty)
            progress_items.append(has_data)

            # Calculate percentage
            completed_items = sum(progress_items)
            total_items = len(progress_items)
            progress = int((completed_items / total_items) * 100) if total_items > 0 else 0

            return progress

        except Exception as e:
            return 0

    def create_text_progress_bar(self, percentage, width=10):
        """
        Create a text-based progress bar

        Args:
            percentage (int): Progress percentage (0-100)
            width (int): Width of progress bar in characters

        Returns:
            str: Text progress bar
        """
        try:
            filled = int((percentage / 100) * width)
            empty = width - filled

            # Use different characters for different progress levels
            if percentage >= 80:
                fill_char = "█"  # Full block for high progress
            elif percentage >= 60:
                fill_char = "▓"  # Medium block
            elif percentage >= 40:
                fill_char = "▒"  # Light block
            else:
                fill_char = "░"  # Very light block

            bar = fill_char * filled + "░" * empty
            return f"[{bar}]"

        except Exception as e:
            return "[??????????]"

    def get_status_color_mapping(self):
        """
        Get color mapping for different status types

        Returns:
            dict: Status to color/style mapping
        """
        return {
            'success': {'emoji': '✅', 'style': 'success'},
            'warning': {'emoji': '⚠️', 'style': 'warning'},
            'error': {'emoji': '❌', 'style': 'danger'},
            'info': {'emoji': 'ℹ️', 'style': 'info'},
            'loading': {'emoji': '🔄', 'style': 'primary'},
            'unknown': {'emoji': '❓', 'style': 'secondary'}
        }

    def update_visual_indicators(self):
        """
        Update all visual indicators across the Google Ads interface
        This method refreshes colors, progress bars, and status displays
        """
        try:
            # Update client context with latest visual styling
            self.update_google_ads_client_context()

            # Update validation status if available
            if hasattr(self, 'google_ads_validation_status'):
                current_client = self.client_var.get() if hasattr(self, 'client_var') else "No client"
                if current_client and current_client != "No client":
                    progress = self.calculate_client_progress(current_client)
                    if progress >= 80:
                        # High progress - ready for operations
                        pass  # Keep current status
                    elif progress >= 60:
                        # Medium progress - some setup needed
                        pass  # Keep current status
                    else:
                        # Low progress - significant setup needed
                        pass  # Keep current status

            self.google_ads_log("🎨 Visual indicators updated")

        except Exception as e:
            self.google_ads_log(f"❌ Error updating visual indicators: {str(e)}")

    def generate_client_table_id(self, client_name, data_type="google_ads"):
        """
        Generate smart, client-specific Airtable table ID with enhanced logic

        Args:
            client_name (str): The client name to generate table ID for
            data_type (str): Type of data ('google_ads', 'ghl', etc.)

        Returns:
            str: Generated table ID following Airtable conventions
        """
        try:
            if not client_name or client_name == "No client":
                return f"tbl{data_type.title()}Default"

            # Enhanced sanitization for client name
            safe_name = self.sanitize_client_name_for_table_id(client_name)

            # Create type suffix mapping
            type_suffixes = {
                "google_ads": "GAds",
                "ghl": "GHL",
                "sheets": "Sheets",
                "general": "Data"
            }

            suffix = type_suffixes.get(data_type.lower(), "Data")

            # Generate base table ID
            base_id = f"tbl{safe_name}{suffix}"

            # Ensure Airtable table ID length limit (17 characters max)
            if len(base_id) > 17:
                # Truncate client name part while preserving suffix
                max_client_length = 17 - len(f"tbl{suffix}")
                truncated_name = safe_name[:max_client_length]
                base_id = f"tbl{truncated_name}{suffix}"

            # Validate and ensure uniqueness if needed
            validated_id = self.validate_table_id(base_id, client_name, data_type)

            self.google_ads_log(f"📊 Generated table ID: {validated_id} for client: {client_name}")

            return validated_id

        except Exception as e:
            self.google_ads_log(f"❌ Error generating table ID: {str(e)}")
            # Fallback to simple generation
            return f"tbl{data_type.title()}Client"

    def sanitize_client_name_for_table_id(self, client_name):
        """
        Enhanced sanitization for client names to create valid table IDs

        Args:
            client_name (str): Original client name

        Returns:
            str: Sanitized name suitable for table ID
        """
        try:
            # Remove common business suffixes and words
            business_words = ['LLC', 'Inc', 'Corp', 'Ltd', 'Company', 'Co', 'Business', 'Enterprises', 'Group']
            name = client_name

            for word in business_words:
                # Remove with various cases and punctuation
                import re
                pattern = rf'\b{re.escape(word)}\b\.?'
                name = re.sub(pattern, '', name, flags=re.IGNORECASE)

            # Clean up the name
            name = name.strip()

            # Remove special characters and spaces, keep alphanumeric
            import re
            name = re.sub(r'[^a-zA-Z0-9]', '', name)

            # Ensure it starts with a letter (Airtable requirement)
            if name and not name[0].isalpha():
                name = 'C' + name

            # Capitalize first letter of each word for readability
            if len(name) > 1:
                # Split on capital letters to preserve camelCase
                words = re.findall(r'[A-Z][a-z]*|[a-z]+', name)
                name = ''.join(word.capitalize() for word in words)

            # Ensure minimum length
            if len(name) < 2:
                name = f"Client{name}" if name else "Client"

            # Ensure maximum reasonable length for table ID generation
            if len(name) > 10:  # Leave room for prefix and suffix
                name = name[:10]

            return name

        except Exception as e:
            self.google_ads_log(f"❌ Error sanitizing client name: {str(e)}")
            # Fallback to basic sanitization
            return re.sub(r'[^a-zA-Z0-9]', '', client_name)[:8] or "Client"

    def validate_table_id(self, table_id, client_name, data_type):
        """
        Validate table ID format and ensure uniqueness

        Args:
            table_id (str): Generated table ID
            client_name (str): Client name for context
            data_type (str): Data type for context

        Returns:
            str: Validated (and possibly modified) table ID
        """
        try:
            # Basic format validation
            if not table_id.startswith('tbl'):
                table_id = 'tbl' + table_id[3:] if len(table_id) > 3 else 'tblDefault'

            # Length validation
            if len(table_id) > 17:
                table_id = table_id[:17]
            elif len(table_id) < 6:
                table_id = table_id + 'Data'

            # Character validation (alphanumeric only)
            import re
            if not re.match(r'^tbl[a-zA-Z0-9]+$', table_id):
                # Clean invalid characters
                clean_id = 'tbl' + re.sub(r'[^a-zA-Z0-9]', '', table_id[3:])
                table_id = clean_id if len(clean_id) >= 6 else 'tblDefault'

            # Store table ID in client configuration for future reference
            self.store_client_table_id(client_name, data_type, table_id)

            return table_id

        except Exception as e:
            self.google_ads_log(f"❌ Error validating table ID: {str(e)}")
            return f"tbl{data_type.title()}Default"

    def store_client_table_id(self, client_name, data_type, table_id):
        """
        Store generated table ID in client configuration for consistency

        Args:
            client_name (str): Client name
            data_type (str): Data type
            table_id (str): Generated table ID
        """
        try:
            if hasattr(self, 'config_manager') and client_name and client_name != "No client":
                # Find or create client configuration
                clients = self.config_manager.config.get("clients", [])

                client_config = None
                for client in clients:
                    if client.get("client_name") == client_name:
                        client_config = client
                        break

                if client_config:
                    # Store table ID in client config
                    if "table_ids" not in client_config:
                        client_config["table_ids"] = {}

                    client_config["table_ids"][data_type] = table_id

                    # Save configuration
                    self.config_manager.save_config()

                    self.google_ads_log(f"💾 Stored table ID {table_id} for client {client_name} ({data_type})")

        except Exception as e:
            self.google_ads_log(f"❌ Error storing table ID: {str(e)}")

    def get_client_table_id(self, client_name, data_type="google_ads"):
        """
        Retrieve stored table ID for client and data type

        Args:
            client_name (str): Client name
            data_type (str): Data type

        Returns:
            str: Stored table ID or None if not found
        """
        try:
            if hasattr(self, 'config_manager') and client_name and client_name != "No client":
                clients = self.config_manager.config.get("clients", [])

                for client in clients:
                    if client.get("client_name") == client_name:
                        table_ids = client.get("table_ids", {})
                        return table_ids.get(data_type)

            return None

        except Exception as e:
            self.google_ads_log(f"❌ Error retrieving table ID: {str(e)}")
            return None

    def update_all_client_table_ids(self):
        """
        Update table IDs for all existing clients to ensure consistency
        This method can be called during application startup or when needed
        """
        try:
            if not hasattr(self, 'config_manager'):
                return

            clients = self.config_manager.config.get("clients", [])
            updated_count = 0

            for client in clients:
                client_name = client.get("client_name")
                if not client_name:
                    continue

                # Ensure table_ids section exists
                if "table_ids" not in client:
                    client["table_ids"] = {}

                # Update Google Ads table ID if not exists or invalid
                current_gads_id = client["table_ids"].get("google_ads")
                if not current_gads_id or not self.is_valid_table_id(current_gads_id):
                    new_gads_id = self.generate_client_table_id(client_name, "google_ads")
                    client["table_ids"]["google_ads"] = new_gads_id
                    updated_count += 1
                    self.google_ads_log(f"📊 Updated Google Ads table ID for {client_name}: {new_gads_id}")

                # Update GHL table ID if not exists or invalid
                current_ghl_id = client["table_ids"].get("ghl")
                if not current_ghl_id or not self.is_valid_table_id(current_ghl_id):
                    new_ghl_id = self.generate_client_table_id(client_name, "ghl")
                    client["table_ids"]["ghl"] = new_ghl_id
                    updated_count += 1
                    self.google_ads_log(f"📊 Updated GHL table ID for {client_name}: {new_ghl_id}")

            if updated_count > 0:
                self.config_manager.save_config()
                self.google_ads_log(f"✅ Updated table IDs for {updated_count} client configurations")

        except Exception as e:
            self.google_ads_log(f"❌ Error updating client table IDs: {str(e)}")

    def is_valid_table_id(self, table_id):
        """
        Validate if a table ID follows Airtable conventions

        Args:
            table_id (str): Table ID to validate

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            if not table_id or not isinstance(table_id, str):
                return False

            # Check basic format
            if not table_id.startswith('tbl'):
                return False

            # Check length (Airtable table IDs are typically 17 characters)
            if len(table_id) < 6 or len(table_id) > 17:
                return False

            # Check characters (alphanumeric only after 'tbl')
            import re
            if not re.match(r'^tbl[a-zA-Z0-9]+$', table_id):
                return False

            return True

        except Exception:
            return False

    def get_table_id_suggestions(self, client_name):
        """
        Get table ID suggestions for a client across different data types

        Args:
            client_name (str): Client name

        Returns:
            dict: Dictionary of data_type -> suggested_table_id
        """
        try:
            suggestions = {}
            data_types = ["google_ads", "ghl", "sheets", "general"]

            for data_type in data_types:
                suggestions[data_type] = self.generate_client_table_id(client_name, data_type)

            return suggestions

        except Exception as e:
            self.google_ads_log(f"❌ Error generating table ID suggestions: {str(e)}")
            return {}

    def sync_table_ids_with_airtable(self, client_name):
        """
        Sync table IDs with actual Airtable base to ensure they exist
        This is a placeholder for future Airtable integration

        Args:
            client_name (str): Client name to sync
        """
        try:
            # This would integrate with Airtable API to verify table existence
            # For now, just log the intent
            stored_ids = {}
            if hasattr(self, 'config_manager'):
                clients = self.config_manager.config.get("clients", [])
                for client in clients:
                    if client.get("client_name") == client_name:
                        stored_ids = client.get("table_ids", {})
                        break

            self.google_ads_log(f"📊 Table IDs for {client_name}: {stored_ids}")
            # Future: Verify these tables exist in Airtable and create if needed

        except Exception as e:
            self.google_ads_log(f"❌ Error syncing table IDs with Airtable: {str(e)}")

    def test_table_id_generation(self):
        """
        Comprehensive testing of table ID generation with various client names
        This method tests edge cases and validates the generation logic
        """
        try:
            self.google_ads_log("🧪 Starting comprehensive table ID generation tests...")

            # Test cases with various client name scenarios
            test_cases = [
                # Normal cases
                ("ABC Corporation", "google_ads"),
                ("XYZ Marketing LLC", "ghl"),
                ("Tech Solutions Inc", "google_ads"),

                # Edge cases
                ("A", "google_ads"),  # Very short name
                ("Very Long Business Name Corporation LLC", "google_ads"),  # Very long name
                ("123 Numbers Only", "ghl"),  # Starting with numbers
                ("Special!@#$%^&*()Characters", "google_ads"),  # Special characters
                ("", "google_ads"),  # Empty name
                ("   Spaces   Around   ", "ghl"),  # Extra spaces

                # Real-world examples
                ("Johnson & Associates", "google_ads"),
                ("Smith's Auto Repair", "ghl"),
                ("O'Connor Marketing Group", "google_ads"),
                ("McDonald's Restaurant", "ghl"),
                ("AT&T Business Solutions", "google_ads"),
            ]

            results = []
            passed_tests = 0
            total_tests = len(test_cases)

            for client_name, data_type in test_cases:
                try:
                    # Generate table ID
                    table_id = self.generate_client_table_id(client_name, data_type)

                    # Validate the generated ID
                    is_valid = self.is_valid_table_id(table_id)

                    # Additional checks
                    length_ok = 6 <= len(table_id) <= 17
                    format_ok = table_id.startswith('tbl')

                    test_passed = is_valid and length_ok and format_ok

                    if test_passed:
                        passed_tests += 1

                    results.append({
                        'client_name': client_name,
                        'data_type': data_type,
                        'table_id': table_id,
                        'valid': is_valid,
                        'length_ok': length_ok,
                        'format_ok': format_ok,
                        'passed': test_passed
                    })

                    status = "✅" if test_passed else "❌"
                    self.google_ads_log(f"{status} {client_name[:20]:<20} → {table_id}")

                except Exception as e:
                    results.append({
                        'client_name': client_name,
                        'data_type': data_type,
                        'table_id': 'ERROR',
                        'error': str(e),
                        'passed': False
                    })
                    self.google_ads_log(f"❌ {client_name[:20]:<20} → ERROR: {str(e)}")

            # Summary
            self.google_ads_log("=" * 50)
            self.google_ads_log(f"🧪 Table ID Generation Test Results:")
            self.google_ads_log(f"   Total tests: {total_tests}")
            self.google_ads_log(f"   Passed: {passed_tests}")
            self.google_ads_log(f"   Failed: {total_tests - passed_tests}")
            self.google_ads_log(f"   Success rate: {(passed_tests/total_tests)*100:.1f}%")

            # Test uniqueness
            self.test_table_id_uniqueness()

            return results

        except Exception as e:
            self.google_ads_log(f"❌ Error during table ID testing: {str(e)}")
            return []

    def test_table_id_uniqueness(self):
        """Test that different clients get unique table IDs"""
        try:
            self.google_ads_log("🔍 Testing table ID uniqueness...")

            # Test similar client names
            similar_clients = [
                "ABC Corp",
                "ABC Corporation",
                "ABC Company",
                "ABC Inc",
                "A.B.C. Corp"
            ]

            generated_ids = {}
            duplicates_found = 0

            for client in similar_clients:
                table_id = self.generate_client_table_id(client, "google_ads")

                if table_id in generated_ids:
                    duplicates_found += 1
                    self.google_ads_log(f"⚠️ Duplicate ID found: {table_id} for '{client}' and '{generated_ids[table_id]}'")
                else:
                    generated_ids[table_id] = client
                    self.google_ads_log(f"✅ Unique ID: {table_id} for '{client}'")

            if duplicates_found == 0:
                self.google_ads_log("✅ All table IDs are unique!")
            else:
                self.google_ads_log(f"⚠️ Found {duplicates_found} duplicate table IDs")

        except Exception as e:
            self.google_ads_log(f"❌ Error testing uniqueness: {str(e)}")

    def validate_airtable_compatibility(self, table_id):
        """
        Validate table ID against Airtable API requirements

        Args:
            table_id (str): Table ID to validate

        Returns:
            dict: Validation results with details
        """
        try:
            validation = {
                'valid': True,
                'errors': [],
                'warnings': []
            }

            # Check basic format
            if not table_id.startswith('tbl'):
                validation['valid'] = False
                validation['errors'].append("Table ID must start with 'tbl'")

            # Check length
            if len(table_id) < 6:
                validation['valid'] = False
                validation['errors'].append("Table ID too short (minimum 6 characters)")
            elif len(table_id) > 17:
                validation['valid'] = False
                validation['errors'].append("Table ID too long (maximum 17 characters)")

            # Check characters
            import re
            if not re.match(r'^tbl[a-zA-Z0-9]+$', table_id):
                validation['valid'] = False
                validation['errors'].append("Table ID contains invalid characters (only alphanumeric allowed after 'tbl')")

            # Check for common issues
            if table_id.lower() in ['tbldefault', 'tbltest', 'tbltemp']:
                validation['warnings'].append("Table ID uses a generic name that might conflict")

            # Check if it follows naming conventions
            if len(table_id) < 10:
                validation['warnings'].append("Table ID is quite short, consider a more descriptive name")

            return validation

        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': []
            }

    def run_table_id_diagnostics(self):
        """
        Run comprehensive diagnostics on the table ID system
        This can be called from the UI for troubleshooting
        """
        try:
            self.google_ads_log("🔧 Running table ID system diagnostics...")

            # Test the generation system
            test_results = self.test_table_id_generation()

            # Check existing client configurations
            if hasattr(self, 'config_manager'):
                clients = self.config_manager.config.get("clients", [])
                self.google_ads_log(f"📊 Found {len(clients)} clients in configuration")

                for client in clients:
                    client_name = client.get("client_name", "Unknown")
                    table_ids = client.get("table_ids", {})

                    if table_ids:
                        self.google_ads_log(f"   {client_name}: {table_ids}")

                        # Validate each stored table ID
                        for data_type, table_id in table_ids.items():
                            validation = self.validate_airtable_compatibility(table_id)
                            if not validation['valid']:
                                self.google_ads_log(f"   ⚠️ Invalid {data_type} table ID for {client_name}: {validation['errors']}")
                    else:
                        self.google_ads_log(f"   {client_name}: No table IDs configured")

            # Test current client if selected
            current_client = self.client_var.get() if hasattr(self, 'client_var') else None
            if current_client and current_client != "No client":
                self.google_ads_log(f"🎯 Testing current client: {current_client}")
                suggestions = self.get_table_id_suggestions(current_client)
                for data_type, suggestion in suggestions.items():
                    validation = self.validate_airtable_compatibility(suggestion)
                    status = "✅" if validation['valid'] else "❌"
                    self.google_ads_log(f"   {status} {data_type}: {suggestion}")

            self.google_ads_log("✅ Table ID diagnostics complete")

        except Exception as e:
            self.google_ads_log(f"❌ Error running diagnostics: {str(e)}")

    def get_google_ads_api_status(self):
        """
        Get current Google Ads API connection status

        Returns:
            str: Formatted status string with emoji and color coding
        """
        try:
            # Check if credentials are configured
            if not hasattr(self, 'customer_id_var') or not self.customer_id_var.get().strip():
                return "❌ API: No Customer ID"

            if not hasattr(self, 'developer_token_var') or not self.developer_token_var.get().strip():
                return "❌ API: No Dev Token"

            if not hasattr(self, 'refresh_token_var') or not self.refresh_token_var.get().strip():
                return "❌ API: No Refresh Token"

            # Check if API was recently tested (you could store this in a variable)
            # For now, we'll check if credentials look valid
            customer_id = self.customer_id_var.get().strip()
            if len(customer_id.replace("-", "")) == 10:  # Valid customer ID format
                return "✅ API: Ready"
            else:
                return "⚠️ API: Invalid Customer ID"

        except Exception as e:
            return f"❌ API: Error ({str(e)[:20]})"

    def get_google_ads_data_status(self):
        """
        Get current Google Ads data availability status

        Returns:
            str: Formatted status string with data information
        """
        try:
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and not self.ad_spend_data.empty:
                record_count = len(self.ad_spend_data)
                data_source = self.google_ads_data_source or "unknown"

                if record_count > 0:
                    return f"📊 Data: {record_count:,} records ({data_source})"
                else:
                    return "📊 Data: Empty dataset"
            else:
                return "📊 Data: Not loaded"

        except Exception as e:
            return f"📊 Data: Error ({str(e)[:20]})"

    def get_client_folder_status(self, client_name):
        """
        Get client folder status with detailed information

        Args:
            client_name (str): Client name to check

        Returns:
            str: Formatted status string with folder information
        """
        try:
            if not client_name or client_name == "No client":
                return "📁 Folder: Not selected"

            if hasattr(self, 'client_manager'):
                # Check if folder exists
                client_folder = self.client_manager.get_client_folder(client_name)

                if os.path.exists(client_folder):
                    # Count files in client folder
                    try:
                        file_count = len([f for f in os.listdir(client_folder)
                                        if os.path.isfile(os.path.join(client_folder, f))])
                        return f"📁 Folder: {file_count} files"
                    except:
                        return "📁 Folder: Exists"
                else:
                    return "📁 Folder: Will create"
            else:
                return "📁 Folder: Manager unavailable"

        except Exception as e:
            return f"📁 Folder: Error ({str(e)[:20]})"

    def get_comprehensive_client_status(self, client_name):
        """
        Get comprehensive status information for a client

        Args:
            client_name (str): Client name

        Returns:
            dict: Comprehensive status information
        """
        try:
            status = {
                'client_name': client_name,
                'api_status': self.get_google_ads_api_status(),
                'data_status': self.get_google_ads_data_status(),
                'folder_status': self.get_client_folder_status(client_name),
                'table_ids': {},
                'last_updated': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Get table IDs
            if hasattr(self, 'config_manager'):
                clients = self.config_manager.config.get("clients", [])
                for client in clients:
                    if client.get("client_name") == client_name:
                        status['table_ids'] = client.get("table_ids", {})
                        break

            # Add Airtable connection status
            if hasattr(self, 'airtable_api_key_var') and self.airtable_api_key_var.get().strip():
                status['airtable_configured'] = True
                status['airtable_status'] = "✅ Configured"
            else:
                status['airtable_configured'] = False
                status['airtable_status'] = "❌ Not configured"

            return status

        except Exception as e:
            return {
                'client_name': client_name,
                'error': str(e),
                'last_updated': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def update_client_status_indicators(self):
        """
        Update all client status indicators in the UI
        This method can be called periodically or after significant changes
        """
        try:
            current_client = self.client_var.get() if hasattr(self, 'client_var') else "No client"

            # Update the main client context
            self.update_google_ads_client_context()

            # Update button states based on current status
            self.update_google_ads_button_states(self.google_ads_data_loaded)

            # Log status update
            if current_client and current_client != "No client":
                status = self.get_comprehensive_client_status(current_client)
                self.google_ads_log(f"🔄 Status updated for {current_client}")

                # Optionally log detailed status
                if hasattr(self, 'debug_mode') and self.debug_mode:
                    for key, value in status.items():
                        if key not in ['client_name', 'last_updated']:
                            self.google_ads_log(f"   {key}: {value}")

        except Exception as e:
            self.google_ads_log(f"❌ Error updating status indicators: {str(e)}")

    def refresh_all_status_indicators(self):
        """
        Refresh all status indicators across the application
        This is a comprehensive refresh that can be triggered manually
        """
        try:
            self.google_ads_log("🔄 Refreshing all status indicators...")

            # Update Google Ads status
            self.update_client_status_indicators()

            # Update GHL status if available
            if hasattr(self, 'update_ghl_client_context'):
                self.update_ghl_client_context()

            # Update any other status indicators
            current_client = self.client_var.get() if hasattr(self, 'client_var') else "No client"
            if current_client and current_client != "No client":
                # Run diagnostics for current client
                status = self.get_comprehensive_client_status(current_client)

                # Display comprehensive status
                self.google_ads_log("📊 Current Client Status:")
                self.google_ads_log(f"   Client: {status.get('client_name', 'Unknown')}")
                self.google_ads_log(f"   API: {status.get('api_status', 'Unknown')}")
                self.google_ads_log(f"   Data: {status.get('data_status', 'Unknown')}")
                self.google_ads_log(f"   Folder: {status.get('folder_status', 'Unknown')}")
                self.google_ads_log(f"   Airtable: {status.get('airtable_status', 'Unknown')}")

                table_ids = status.get('table_ids', {})
                if table_ids:
                    self.google_ads_log(f"   Table IDs: {table_ids}")

            self.google_ads_log("✅ Status indicators refreshed")

        except Exception as e:
            self.google_ads_log(f"❌ Error refreshing status indicators: {str(e)}")

    def load_and_preview_google_ads_data(self):
        """Load and preview Google Ads data from file"""
        # Open file dialog
        file_path = filedialog.askopenfilename(
            title="Select Google Ads Data File",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )

        if not file_path:
            return

        if not os.path.exists(file_path):
            messagebox.showerror("File Not Found", f"File not found: {file_path}")
            return

        try:
            self.google_ads_log(f"📖 Loading Google Ads data from: {file_path}")

            # Determine file type and load accordingly
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension in ['.xlsx', '.xls']:
                self.ad_spend_data = pd.read_excel(file_path)
                self.google_ads_log(f"✅ Successfully loaded Excel file")
            elif file_extension == '.csv':
                # Try different encodings for CSV files
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                loaded_successfully = False

                for encoding in encodings:
                    try:
                        self.ad_spend_data = pd.read_csv(file_path, encoding=encoding)
                        self.google_ads_log(f"✅ Successfully loaded CSV with {encoding} encoding")
                        loaded_successfully = True
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.google_ads_log(f"❌ Error with {encoding} encoding: {str(e)}")
                        continue

                if not loaded_successfully:
                    raise Exception("Could not load CSV file with any supported encoding")
            else:
                # Try to load as CSV anyway
                self.ad_spend_data = pd.read_csv(file_path)
                self.google_ads_log(f"✅ Successfully loaded file as CSV")

            # Validate data
            if self.ad_spend_data.empty:
                raise Exception("The loaded file is empty")

            # Check if this is actually Google Ads data
            expected_ads_columns = ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks']
            ghl_columns = ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage']

            # Check which type of data this is
            if any(col.lower() in [c.lower() for c in self.ad_spend_data.columns] for col in ghl_columns):
                self.google_ads_log("⚠️ Warning: This appears to be GHL lead data, not Google Ads data")
                messagebox.showwarning("Data Type Mismatch",
                                     "This appears to be GHL lead data, not Google Ads data.\n"
                                     "Please select a proper Google Ads file with campaign data.")
                return

            # Validate Google Ads data structure
            missing_columns = []
            for col in expected_ads_columns:
                if not any(col.lower() == existing_col.lower() for existing_col in self.ad_spend_data.columns):
                    missing_columns.append(col)

            if missing_columns:
                self.google_ads_log(f"⚠️ Warning: Missing expected columns: {', '.join(missing_columns)}")
                response = messagebox.askyesno("Column Validation",
                                             f"Some expected Google Ads columns are missing:\n{', '.join(missing_columns)}\n\n"
                                             "Do you want to continue anyway?")
                if not response:
                    return

            self.google_ads_log(f"✅ Loaded {len(self.ad_spend_data)} records for preview")
            self.log(f"Google Ads data loaded from file: {len(self.ad_spend_data)} records")

            # Use unified data state update
            self.update_google_ads_data_state(data_source="file", show_success_message=True)

        except Exception as e:
            error_msg = f"Failed to load Google Ads data: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)

    def validate_google_ads_data(self):
        """Validate Google Ads data against Airtable to ensure consistency"""
        try:
            # Check if we have data to validate
            if not self.has_valid_google_ads_data():
                messagebox.showerror("No Data", "Please load Google Ads data first (from API or file).")
                return

            # Get data info for logging
            data_source = self.google_ads_data_source or "unknown"
            record_count = len(self.ad_spend_data) if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None else 0
            self.google_ads_log(f"🔍 Starting validation for {data_source} data ({record_count:,} records)...")

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before validating data.")
                return

            # Check Airtable configuration
            api_key = self.airtable_api_key_var.get().strip() if hasattr(self, 'airtable_api_key_var') else ""
            base_id = self.google_ads_base_id_var.get().strip() if hasattr(self, 'google_ads_base_id_var') else ""
            table_id = self.google_ads_table_id_var.get().strip() if hasattr(self, 'google_ads_table_id_var') else ""

            if not all([api_key, base_id, table_id]):
                messagebox.showerror("Configuration Error",
                                   "Please configure Airtable API key, Base ID, and Table ID before validation.")
                return

            self.google_ads_log(f"🔗 Connecting to Airtable for validation...")
            self.google_ads_log(f"   Client: {current_client_name}")

            # If client is selected, try to use client-specific table ID
            if current_client_name and current_client_name != "No client":
                client_table_id = self.get_client_table_id(current_client_name, "google_ads")
                if client_table_id:
                    table_id = client_table_id
                    self.google_ads_log(f"🎯 Using client-specific table ID: {table_id}")
                else:
                    # Generate and store client-specific table ID
                    client_table_id = self.generate_client_table_id(current_client_name, "google_ads")
                    self.store_client_table_id(current_client_name, "google_ads", client_table_id)
                    table_id = client_table_id
                    self.google_ads_log(f"🆕 Generated client-specific table ID: {table_id}")

            self.google_ads_log(f"   Base ID: {base_id}")
            self.google_ads_log(f"   Table ID: {table_id}")

            # Create Airtable manager and fetch existing data
            airtable_manager = AirtableManager(api_key)
            airtable_manager.base_id = base_id
            airtable_manager.google_ads_table_id = table_id

            # Test connection first
            success, message = airtable_manager.test_connection()
            if not success:
                self.google_ads_log(f"❌ Validation connection failed: {message}")
                messagebox.showerror("Validation Failed", f"Connection failed: {message}\n\nPlease check:\n1. API key permissions\n2. Base ID: {base_id}\n3. Table ID: {table_id}")
                return

            # Get existing records from Airtable
            existing_records = airtable_manager.get_google_ads_records()

            if existing_records:
                self.google_ads_log(f"📊 Found {len(existing_records)} existing records in Airtable")

                # Convert to DataFrame for comparison
                airtable_df = pd.DataFrame(existing_records)

                # Perform validation checks
                validation_results = {
                    'local_count': len(self.ad_spend_data),
                    'airtable_count': len(airtable_df),
                    'count_match': len(self.ad_spend_data) == len(airtable_df),
                    'first_record_match': False,
                    'last_record_match': False,
                    'column_comparison': {},
                    'validation_passed': False,
                    'date_range_match': False
                }

                # Check date range consistency
                if 'Date' in self.ad_spend_data.columns and 'Date' in airtable_df.columns:
                    local_dates = pd.to_datetime(self.ad_spend_data['Date']).dt.date
                    airtable_dates = pd.to_datetime(airtable_df['Date']).dt.date

                    local_min, local_max = local_dates.min(), local_dates.max()
                    airtable_min, airtable_max = airtable_dates.min(), airtable_dates.max()

                    validation_results['date_range_match'] = (local_min == airtable_min and local_max == airtable_max)

                    self.google_ads_log(f"📅 Date range comparison:")
                    self.google_ads_log(f"   Local: {local_min} to {local_max}")
                    self.google_ads_log(f"   Airtable: {airtable_min} to {airtable_max}")

                # Check first and last record matching
                if len(self.ad_spend_data) > 0 and len(airtable_df) > 0:
                    # Compare common columns
                    common_columns = set(self.ad_spend_data.columns) & set(airtable_df.columns)

                    if common_columns:
                        self.google_ads_log(f"🔍 Comparing {len(common_columns)} common columns: {', '.join(list(common_columns)[:5])}...")

                        # Check first record
                        local_first = self.ad_spend_data.iloc[0][list(common_columns)].to_dict()
                        airtable_first = airtable_df.iloc[0][list(common_columns)].to_dict()

                        first_match_count = 0
                        for col in common_columns:
                            local_val = str(local_first.get(col, '')).strip()
                            airtable_val = str(airtable_first.get(col, '')).strip()
                            if local_val == airtable_val:
                                first_match_count += 1

                        validation_results['first_record_match'] = first_match_count >= len(common_columns) * 0.8  # 80% match

                        # Check last record
                        local_last = self.ad_spend_data.iloc[-1][list(common_columns)].to_dict()
                        airtable_last = airtable_df.iloc[-1][list(common_columns)].to_dict()

                        last_match_count = 0
                        for col in common_columns:
                            local_val = str(local_last.get(col, '')).strip()
                            airtable_val = str(airtable_last.get(col, '')).strip()
                            if local_val == airtable_val:
                                last_match_count += 1

                        validation_results['last_record_match'] = last_match_count >= len(common_columns) * 0.8  # 80% match

                        validation_results['column_comparison'] = {
                            'common_columns': list(common_columns),
                            'local_only': list(set(self.ad_spend_data.columns) - set(airtable_df.columns)),
                            'airtable_only': list(set(airtable_df.columns) - set(self.ad_spend_data.columns))
                        }

                # Overall validation result
                validation_results['validation_passed'] = (
                    validation_results['count_match'] and
                    validation_results['first_record_match'] and
                    validation_results['last_record_match'] and
                    validation_results['date_range_match']
                )

                # Display validation results
                self.display_google_ads_validation_results(validation_results)

            else:
                self.google_ads_log("⚠️ No existing records found in Airtable")
                if hasattr(self, 'google_ads_validation_status'):
                    self.google_ads_validation_status.config(
                        text="Airtable table is empty - ready for initial sync",
                        bootstyle="info"
                    )
                messagebox.showinfo(
                    "Validation Complete",
                    "Airtable table is empty. Your Google Ads data is ready for initial synchronization."
                )

        except Exception as e:
            error_msg = f"Validation failed: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Validation Error", error_msg)

    def has_valid_google_ads_data(self):
        """Check if we have valid Google Ads data loaded"""
        return (hasattr(self, 'ad_spend_data') and
                self.ad_spend_data is not None and
                not self.ad_spend_data.empty)

    def display_google_ads_validation_results(self, results):
        """Display Google Ads validation results to user"""
        self.google_ads_log("📋 GOOGLE ADS VALIDATION RESULTS:")
        self.google_ads_log("=" * 50)
        self.google_ads_log(f"📊 Record Count Comparison:")
        self.google_ads_log(f"   Local data: {results['local_count']:,} records")
        self.google_ads_log(f"   Airtable data: {results['airtable_count']:,} records")
        self.google_ads_log(f"   Count match: {'✅ Yes' if results['count_match'] else '❌ No'}")

        self.google_ads_log(f"🔍 Record Content Validation:")
        self.google_ads_log(f"   First record match: {'✅ Yes' if results['first_record_match'] else '❌ No'}")
        self.google_ads_log(f"   Last record match: {'✅ Yes' if results['last_record_match'] else '❌ No'}")
        self.google_ads_log(f"   Date range match: {'✅ Yes' if results['date_range_match'] else '❌ No'}")

        if results['column_comparison']:
            self.google_ads_log(f"📋 Column Analysis:")
            self.google_ads_log(f"   Common columns: {len(results['column_comparison']['common_columns'])}")
            if results['column_comparison']['local_only']:
                self.google_ads_log(f"   Local-only columns: {', '.join(results['column_comparison']['local_only'])}")
            if results['column_comparison']['airtable_only']:
                self.google_ads_log(f"   Airtable-only columns: {', '.join(results['column_comparison']['airtable_only'])}")

        self.google_ads_log("=" * 50)

        # Update UI status
        if results['validation_passed']:
            if hasattr(self, 'google_ads_validation_status'):
                self.google_ads_validation_status.config(
                    text="✅ Validation passed - data is synchronized",
                    bootstyle="success"
                )
            messagebox.showinfo(
                "Validation Passed",
                f"✅ Google Ads data validation successful!\n\n"
                f"Local records: {results['local_count']:,}\n"
                f"Airtable records: {results['airtable_count']:,}\n"
                f"First/Last records match: ✅\n"
                f"Date range match: ✅\n\n"
                f"Your data is properly synchronized."
            )
        else:
            if hasattr(self, 'google_ads_validation_status'):
                self.google_ads_validation_status.config(
                    text="❌ Validation failed - data mismatch detected",
                    bootstyle="danger"
                )

            # Build detailed error message
            issues = []
            if not results['count_match']:
                issues.append(f"Record count mismatch: Local({results['local_count']:,}) vs Airtable({results['airtable_count']:,})")
            if not results['first_record_match']:
                issues.append("First record content doesn't match")
            if not results['last_record_match']:
                issues.append("Last record content doesn't match")
            if not results['date_range_match']:
                issues.append("Date ranges don't match")

            messagebox.showwarning(
                "Validation Failed",
                f"❌ Google Ads data validation failed!\n\n"
                f"Issues found:\n" + "\n".join(f"• {issue}" for issue in issues) +
                f"\n\nPlease check the activity log for detailed information."
            )

    def generate_google_ads_report(self):
        """Generate comprehensive Google Ads report"""
        if not self.has_valid_google_ads_data():
            messagebox.showerror("No Data", "Please load Google Ads data first before generating a report.")
            return

        try:
            self.google_ads_log("📈 Generating comprehensive Google Ads report...")

            # Get current client info
            current_client = self.client_var.get() if hasattr(self, 'client_var') else "Unknown Client"
            data_source = self.google_ads_data_source or "unknown"

            # Create report window
            report_window = tk.Toplevel(self.master)
            report_window.title(f"📈 Google Ads Report - {current_client}")
            report_window.geometry("800x600")
            report_window.transient(self.master)

            # Center the window
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (report_window.winfo_screenheight() // 2) - (600 // 2)
            report_window.geometry(f"800x600+{x}+{y}")

            # Main frame
            main_frame = ttk_bootstrap.Frame(report_window, padding=15)
            main_frame.pack(fill=BOTH, expand=True)

            # Report content
            report_text = scrolledtext.ScrolledText(
                main_frame,
                height=30,
                font=('Consolas', 10),
                wrap=tk.WORD
            )
            report_text.pack(fill=BOTH, expand=True)

            # Generate report content
            report_content = self.generate_report_content(current_client, data_source)
            report_text.insert(tk.END, report_content)
            report_text.config(state='disabled')

            # Buttons frame
            buttons_frame = ttk_bootstrap.Frame(main_frame)
            buttons_frame.pack(fill=X, pady=(10, 0))

            ttk_bootstrap.Button(
                buttons_frame,
                text="💾 Save Report",
                command=lambda: self.save_google_ads_report(report_content, current_client),
                bootstyle="primary"
            ).pack(side=LEFT, padx=5)

            ttk_bootstrap.Button(
                buttons_frame,
                text="📋 Copy to Clipboard",
                command=lambda: self.copy_to_clipboard(report_content),
                bootstyle="info"
            ).pack(side=LEFT, padx=5)

            ttk_bootstrap.Button(
                buttons_frame,
                text="❌ Close",
                command=report_window.destroy,
                bootstyle="secondary"
            ).pack(side=RIGHT, padx=5)

            self.google_ads_log("✅ Report generated successfully")

        except Exception as e:
            error_msg = f"Failed to generate report: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Report Error", error_msg)

    def generate_report_content(self, client_name, data_source):
        """Generate detailed report content"""
        try:
            # Header
            report = f"""
📈 GOOGLE ADS PERFORMANCE REPORT
{'=' * 50}

Client: {client_name}
Data Source: {data_source.upper()}
Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Report Period: {self.start_date_var.get()} to {self.end_date_var.get()}

{'=' * 50}

📊 EXECUTIVE SUMMARY
{'=' * 50}
"""

            # Calculate key metrics
            total_records = len(self.ad_spend_data)
            total_cost = self.ad_spend_data['Cost'].sum() if 'Cost' in self.ad_spend_data.columns else 0
            total_clicks = self.ad_spend_data['Clicks'].sum() if 'Clicks' in self.ad_spend_data.columns else 0
            total_impressions = self.ad_spend_data['Impressions'].sum() if 'Impressions' in self.ad_spend_data.columns else 0
            total_conversions = self.ad_spend_data['Conversions'].sum() if 'Conversions' in self.ad_spend_data.columns else 0

            # Performance metrics
            avg_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            avg_cpc = (total_cost / total_clicks) if total_clicks > 0 else 0
            conv_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0
            cost_per_conv = (total_cost / total_conversions) if total_conversions > 0 else 0

            report += f"""
Total Records: {total_records:,}
Total Investment: ${total_cost:,.2f}
Total Clicks: {total_clicks:,}
Total Impressions: {total_impressions:,}
Total Conversions: {total_conversions:,.2f}

Key Performance Indicators:
• Click-Through Rate (CTR): {avg_ctr:.2f}%
• Cost Per Click (CPC): ${avg_cpc:.2f}
• Conversion Rate: {conv_rate:.2f}%
• Cost Per Conversion: ${cost_per_conv:.2f}

"""

            # Campaign analysis if available
            if 'Campaign Name' in self.ad_spend_data.columns:
                campaign_summary = self.ad_spend_data.groupby('Campaign Name').agg({
                    'Cost': 'sum',
                    'Clicks': 'sum',
                    'Impressions': 'sum',
                    'Conversions': 'sum'
                }).round(2)

                report += f"""
📢 CAMPAIGN PERFORMANCE
{'=' * 50}

Top Campaigns by Cost:
"""
                top_campaigns = campaign_summary.nlargest(5, 'Cost')
                for campaign, data in top_campaigns.iterrows():
                    ctr = (data['Clicks'] / data['Impressions'] * 100) if data['Impressions'] > 0 else 0
                    report += f"""
• {campaign}
  Cost: ${data['Cost']:,.2f} | Clicks: {data['Clicks']:,} | CTR: {ctr:.2f}%
"""

            # Date performance if available
            if 'Date' in self.ad_spend_data.columns:
                daily_summary = self.ad_spend_data.groupby('Date').agg({
                    'Cost': 'sum',
                    'Clicks': 'sum',
                    'Impressions': 'sum'
                }).round(2)

                report += f"""

📅 DAILY PERFORMANCE TRENDS
{'=' * 50}

Best Performing Days (by Cost):
"""
                top_days = daily_summary.nlargest(3, 'Cost')
                for date, data in top_days.iterrows():
                    report += f"""
• {date}: ${data['Cost']:,.2f} (Clicks: {data['Clicks']:,})
"""

            report += f"""

📋 DATA QUALITY SUMMARY
{'=' * 50}

Columns Available: {len(self.ad_spend_data.columns)}
Data Completeness: {"✅ Complete" if not self.ad_spend_data.isnull().any().any() else "⚠️ Has missing values"}
Date Range Coverage: {(pd.to_datetime(self.ad_spend_data['Date']).max() - pd.to_datetime(self.ad_spend_data['Date']).min()).days + 1} days

{'=' * 50}
End of Report
"""

            return report

        except Exception as e:
            return f"Error generating report content: {str(e)}"

    def save_google_ads_report(self, content, client_name):
        """Save report to client folder"""
        try:
            if hasattr(self, 'client_manager'):
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_client_name = self.client_manager.sanitize_folder_name(client_name)
                filename = f"{safe_client_name}_google_ads_report_{timestamp}.txt"

                file_path = self.client_manager.get_client_file_path(client_name, filename, "reports")

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.google_ads_log(f"💾 Report saved: {file_path}")
                messagebox.showinfo("Report Saved", f"Report saved successfully to:\n{file_path}")
            else:
                messagebox.showerror("Error", "Client manager not available")

        except Exception as e:
            error_msg = f"Failed to save report: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Save Error", error_msg)

    def copy_to_clipboard(self, content):
        """Copy content to clipboard"""
        try:
            self.master.clipboard_clear()
            self.master.clipboard_append(content)
            self.google_ads_log("📋 Report copied to clipboard")
            messagebox.showinfo("Copied", "Report copied to clipboard successfully!")
        except Exception as e:
            messagebox.showerror("Copy Error", f"Failed to copy to clipboard: {str(e)}")

    def google_ads_log(self, message):
        """Add message to Google Ads status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'google_ads_status_text'):
            self.google_ads_status_text.insert(tk.END, formatted_message + "\n")
            self.google_ads_status_text.see(tk.END)

    def update_google_ads_data_state(self, data_source="api", show_success_message=True):
        """Update Google Ads data state and UI elements"""
        try:
            self.google_ads_data_loaded = True
            self.google_ads_data_source = data_source

            # Update validation status
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None:
                record_count = len(self.ad_spend_data)
                date_range = f"{self.start_date_var.get()} to {self.end_date_var.get()}"

                # Update validation status label
                if hasattr(self, 'google_ads_validation_status'):
                    status_text = f"✅ Data loaded: {record_count:,} records from Google Ads {data_source.upper()}"
                    self.google_ads_validation_status.config(
                        text=status_text,
                        bootstyle="success"
                    )

                # Update data summary
                if hasattr(self, 'google_ads_data_summary'):
                    campaigns = self.ad_spend_data['Campaign Name'].nunique() if 'Campaign Name' in self.ad_spend_data.columns else 0
                    summary_text = f"{record_count:,} records | {date_range} | {campaigns} campaigns"
                    self.google_ads_data_summary.config(
                        text=summary_text,
                        bootstyle="info"
                    )

                # Enable action buttons
                self.update_google_ads_button_states(True)

                # Log to Google Ads tab
                self.google_ads_log(f"📊 Data state updated: {record_count:,} records from {data_source}")

                if show_success_message:
                    self.google_ads_log(f"✅ Google Ads data ready for sync and export operations")

        except Exception as e:
            self.google_ads_log(f"❌ Error updating data state: {str(e)}")

    def update_google_ads_button_states(self, enabled):
        """Update Google Ads button states based on data availability"""
        try:
            # Get current client status
            current_client = self.client_var.get() if hasattr(self, 'client_var') else None
            has_client = current_client and current_client != "No client"

            self.google_ads_log(f"🔘 Updating button states - enabled: {enabled}, has_client: {has_client}, client: {current_client}")

            # Sync buttons: enabled when data + client selected
            if hasattr(self, 'google_ads_sync_btn'):
                sync_state = "normal" if (enabled and has_client) else "disabled"
                self.google_ads_sync_btn.config(state=sync_state)
                self.google_ads_log(f"   🔄 Sync button: {sync_state}")

            # Save buttons: enabled when data available
            if hasattr(self, 'google_ads_save_btn'):
                save_state = "normal" if enabled else "disabled"
                self.google_ads_save_btn.config(state=save_state)
                self.google_ads_log(f"   💾 Save button: {save_state}")

            # Export buttons: enabled when data available
            if hasattr(self, 'google_ads_export_btn'):
                export_state = "normal" if enabled else "disabled"
                self.google_ads_export_btn.config(state=export_state)
                self.google_ads_log(f"   📤 Export button: {export_state}")

            # Granular export button: enabled when granular data available
            if hasattr(self, 'granular_export_btn'):
                granular_data_available = hasattr(self, 'granular_conversion_data') and self.granular_conversion_data is not None
                granular_state = "normal" if granular_data_available else "disabled"
                self.granular_export_btn.config(state=granular_state)
                self.google_ads_log(f"   🎯 Granular Export button: {granular_state}")

            # View buttons: enabled when client selected (regardless of data)
            if hasattr(self, 'google_ads_view_btn'):
                view_state = "normal" if has_client else "disabled"
                self.google_ads_view_btn.config(state=view_state)
                self.google_ads_log(f"   📊 View button: {view_state}")

            # Find and enable validation buttons
            def find_validation_buttons(widget):
                buttons = []
                try:
                    if hasattr(widget, 'cget') and hasattr(widget, 'config'):
                        text = widget.cget('text') if hasattr(widget, 'cget') else ''
                        if 'Validate' in str(text):
                            buttons.append(widget)

                    if hasattr(widget, 'winfo_children'):
                        for child in widget.winfo_children():
                            buttons.extend(find_validation_buttons(child))
                except:
                    pass
                return buttons

            if hasattr(self, 'google_ads_content_frame'):
                validation_buttons = find_validation_buttons(self.google_ads_content_frame)
                validate_state = "normal" if enabled else "disabled"
                for btn in validation_buttons:
                    try:
                        btn.config(state=validate_state)
                        self.google_ads_log(f"   🔍 Validation button: {validate_state}")
                    except:
                        pass

            # Update client status label
            if hasattr(self, 'google_ads_client_status'):
                if enabled:
                    data_info = self.get_google_ads_data_info()
                    if data_info:
                        self.google_ads_client_status.config(
                            text=f"✅ {data_info['record_count']:,} records ready",
                            bootstyle="success"
                        )
                else:
                    self.google_ads_client_status.config(
                        text="No data loaded",
                        bootstyle="secondary"
                    )

            self.google_ads_log(f"✅ Button state update completed")

        except Exception as e:
            self.google_ads_log(f"❌ Error updating button states: {str(e)}")
            import traceback
            self.google_ads_log(f"❌ Traceback: {traceback.format_exc()}")



    def update_comprehensive_workflow_state(self, data_source=None, show_success_message=False):
        """
        Comprehensive workflow state management for Google Ads data processing
        This unified method handles all aspects of data state updates across the workflow
        """
        try:
            # Initialize workflow state tracking
            workflow_state = {
                'timestamp': datetime.datetime.now(),
                'data_available': False,
                'data_source': data_source or "unknown",
                'record_count': 0,
                'client_selected': False,
                'api_configured': False,
                'airtable_configured': False,
                'workflow_stage': 'initial',
                'next_actions': [],
                'data_quality': {}
            }

            # Check data availability and analyze quality
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and not self.ad_spend_data.empty:
                workflow_state['data_available'] = True
                workflow_state['record_count'] = len(self.ad_spend_data)
                workflow_state['workflow_stage'] = 'data_loaded'

                # Update data source tracking
                self.google_ads_data_source = data_source or "unknown"
                self.google_ads_data_loaded = True

                # Analyze data quality and completeness
                workflow_state['data_quality'] = self.analyze_google_ads_data_quality()

            else:
                # No data available
                self.google_ads_data_loaded = False
                self.google_ads_data_source = None
                workflow_state['workflow_stage'] = 'no_data'

            # Check client configuration
            current_client = self.client_var.get() if hasattr(self, 'client_var') else None
            workflow_state['client_selected'] = current_client and current_client != "No client"
            workflow_state['current_client'] = current_client

            # Check API configuration
            workflow_state['api_configured'] = self.check_google_ads_api_configuration()

            # Check Airtable configuration
            workflow_state['airtable_configured'] = self.check_google_ads_airtable_configuration()

            # Determine workflow stage and next actions
            self.determine_google_ads_workflow_stage(workflow_state)

            # Update UI based on workflow state
            self.update_ui_from_google_ads_workflow_state(workflow_state)

            # Store workflow state for reference
            self.current_google_ads_workflow_state = workflow_state

            # Log workflow state
            self.log_google_ads_workflow_state(workflow_state)

            # Show success message if requested
            if show_success_message and workflow_state['data_available']:
                self.show_google_ads_workflow_success_message(workflow_state)

            return workflow_state

        except Exception as e:
            self.google_ads_log(f"❌ Error updating comprehensive workflow state: {str(e)}")
            return None

    def analyze_google_ads_data_quality(self):
        """
        Analyze the quality and completeness of loaded Google Ads data

        Returns:
            dict: Data quality analysis results
        """
        try:
            if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None:
                return {'status': 'no_data', 'issues': ['No data available']}

            analysis = {
                'status': 'good',
                'issues': [],
                'warnings': [],
                'metrics': {},
                'completeness_score': 0
            }

            # Check for missing data
            missing_data = self.ad_spend_data.isnull().sum()
            total_cells = len(self.ad_spend_data) * len(self.ad_spend_data.columns)
            missing_cells = missing_data.sum()

            if missing_cells > 0:
                missing_percentage = (missing_cells / total_cells) * 100
                analysis['metrics']['missing_percentage'] = missing_percentage

                if missing_percentage > 10:
                    analysis['issues'].append(f"High missing data: {missing_percentage:.1f}%")
                    analysis['status'] = 'issues'
                elif missing_percentage > 5:
                    analysis['warnings'].append(f"Some missing data: {missing_percentage:.1f}%")

            # Check for required columns
            required_cols = ['Date', 'Campaign Name', 'Cost', 'Clicks', 'Impressions']
            missing_required = [col for col in required_cols if col not in self.ad_spend_data.columns]
            if missing_required:
                analysis['issues'].append(f"Missing required columns: {missing_required}")
                analysis['status'] = 'issues'

            # Check date range and consistency
            if 'Date' in self.ad_spend_data.columns:
                try:
                    dates = pd.to_datetime(self.ad_spend_data['Date'])
                    date_range = (dates.max() - dates.min()).days
                    analysis['metrics']['date_range_days'] = date_range
                    analysis['metrics']['date_min'] = dates.min().strftime('%Y-%m-%d')
                    analysis['metrics']['date_max'] = dates.max().strftime('%Y-%m-%d')

                    if date_range > 365:
                        analysis['warnings'].append("Data spans more than 1 year")
                    elif date_range < 1:
                        analysis['warnings'].append("Data covers less than 1 day")
                except:
                    analysis['issues'].append("Invalid date format detected")
                    analysis['status'] = 'issues'

            # Calculate completeness score
            completeness_factors = []
            completeness_factors.append(1.0 if not missing_required else 0.5)  # Required columns
            completeness_factors.append(1.0 if missing_cells == 0 else max(0, 1 - (missing_cells / total_cells)))  # Data completeness
            completeness_factors.append(1.0 if 'Date' in self.ad_spend_data.columns else 0.0)  # Date column

            analysis['completeness_score'] = int(sum(completeness_factors) / len(completeness_factors) * 100)

            # Calculate basic metrics
            if 'Cost' in self.ad_spend_data.columns:
                analysis['metrics']['total_cost'] = float(self.ad_spend_data['Cost'].sum())
            if 'Clicks' in self.ad_spend_data.columns:
                analysis['metrics']['total_clicks'] = int(self.ad_spend_data['Clicks'].sum())
            if 'Campaign Name' in self.ad_spend_data.columns:
                analysis['metrics']['unique_campaigns'] = int(self.ad_spend_data['Campaign Name'].nunique())

            return analysis

        except Exception as e:
            return {'status': 'error', 'issues': [f"Analysis error: {str(e)}"]}

    def check_google_ads_api_configuration(self):
        """Check if Google Ads API is properly configured"""
        try:
            return (hasattr(self, 'customer_id_var') and self.customer_id_var.get().strip() and
                   hasattr(self, 'developer_token_var') and self.developer_token_var.get().strip() and
                   hasattr(self, 'refresh_token_var') and self.refresh_token_var.get().strip())
        except:
            return False

    def check_google_ads_airtable_configuration(self):
        """Check if Airtable is properly configured for Google Ads"""
        try:
            return (hasattr(self, 'airtable_api_key_var') and self.airtable_api_key_var.get().strip() and
                   hasattr(self, 'google_ads_base_id_var') and self.google_ads_base_id_var.get().strip() and
                   hasattr(self, 'google_ads_table_id_var') and self.google_ads_table_id_var.get().strip())
        except:
            return False

    def determine_google_ads_workflow_stage(self, workflow_state):
        """
        Determine the current workflow stage and next recommended actions

        Args:
            workflow_state (dict): Current workflow state
        """
        try:
            # Determine workflow stage based on current state
            if not workflow_state['client_selected']:
                workflow_state['workflow_stage'] = 'client_selection_needed'
                workflow_state['next_actions'] = ['Select a client from the dropdown']

            elif not workflow_state['api_configured'] and not workflow_state['data_available']:
                workflow_state['workflow_stage'] = 'api_configuration_needed'
                workflow_state['next_actions'] = [
                    'Configure Google Ads API credentials',
                    'Test API connection',
                    'Or import data from file'
                ]

            elif not workflow_state['data_available']:
                workflow_state['workflow_stage'] = 'data_loading_needed'
                workflow_state['next_actions'] = [
                    'Extract data from Google Ads API',
                    'Or import data from CSV/Excel file'
                ]

            elif not workflow_state['airtable_configured']:
                workflow_state['workflow_stage'] = 'airtable_configuration_needed'
                workflow_state['next_actions'] = [
                    'Configure Airtable API key and Base ID',
                    'Or proceed with file operations only'
                ]

            elif workflow_state['data_quality'].get('status') == 'issues':
                workflow_state['workflow_stage'] = 'data_quality_issues'
                workflow_state['next_actions'] = [
                    'Review data quality issues',
                    'Fix data problems or proceed with caution'
                ]

            else:
                workflow_state['workflow_stage'] = 'ready_for_operations'
                workflow_state['next_actions'] = [
                    'Validate data against Airtable',
                    'Preview data with enhanced analysis',
                    'Sync to Airtable',
                    'Save to client folder',
                    'Export in various formats',
                    'Generate comprehensive reports'
                ]

        except Exception as e:
            workflow_state['workflow_stage'] = 'error'
            workflow_state['next_actions'] = [f'Resolve error: {str(e)}']

    def update_ui_from_google_ads_workflow_state(self, workflow_state):
        """
        Update UI elements based on current workflow state

        Args:
            workflow_state (dict): Current workflow state
        """
        try:
            # Update validation status
            if hasattr(self, 'google_ads_validation_status'):
                if workflow_state['data_available']:
                    quality_score = workflow_state['data_quality'].get('completeness_score', 0)
                    quality_emoji = "✅" if quality_score >= 90 else "⚠️" if quality_score >= 70 else "❌"

                    status_text = (f"{quality_emoji} Data loaded: {workflow_state['record_count']:,} records "
                                 f"(Quality: {quality_score}%)")

                    style = "success" if quality_score >= 90 else "warning" if quality_score >= 70 else "danger"

                    self.google_ads_validation_status.config(
                        text=status_text,
                        bootstyle=style
                    )
                else:
                    self.google_ads_validation_status.config(
                        text="No data loaded",
                        bootstyle="secondary"
                    )

            # Update button states based on workflow stage
            self.update_google_ads_button_states(workflow_state['data_available'])

            # Update visual indicators
            self.update_visual_indicators()

        except Exception as e:
            self.google_ads_log(f"❌ Error updating UI from workflow state: {str(e)}")

    def log_google_ads_workflow_state(self, workflow_state):
        """
        Log comprehensive workflow state information

        Args:
            workflow_state (dict): Current workflow state
        """
        try:
            self.google_ads_log("🔄 WORKFLOW STATE UPDATE")
            self.google_ads_log("=" * 40)
            self.google_ads_log(f"Stage: {workflow_state['workflow_stage']}")
            self.google_ads_log(f"Client: {workflow_state.get('current_client', 'None')}")
            self.google_ads_log(f"Data: {'✅' if workflow_state['data_available'] else '❌'} "
                              f"({workflow_state['record_count']:,} records)")
            self.google_ads_log(f"API: {'✅' if workflow_state['api_configured'] else '❌'}")
            self.google_ads_log(f"Airtable: {'✅' if workflow_state['airtable_configured'] else '❌'}")

            # Log data quality if available
            if workflow_state['data_available'] and workflow_state['data_quality']:
                quality = workflow_state['data_quality']
                self.google_ads_log(f"Data Quality: {quality.get('completeness_score', 0)}% "
                                  f"({quality.get('status', 'unknown')})")

                if quality.get('issues'):
                    self.google_ads_log(f"Issues: {', '.join(quality['issues'])}")
                if quality.get('warnings'):
                    self.google_ads_log(f"Warnings: {', '.join(quality['warnings'])}")

            # Log next actions
            if workflow_state['next_actions']:
                self.google_ads_log("Next Actions:")
                for i, action in enumerate(workflow_state['next_actions'], 1):
                    self.google_ads_log(f"  {i}. {action}")

            self.google_ads_log("=" * 40)

        except Exception as e:
            self.google_ads_log(f"❌ Error logging workflow state: {str(e)}")

    def show_google_ads_workflow_success_message(self, workflow_state):
        """
        Show success message with workflow-specific information

        Args:
            workflow_state (dict): Current workflow state
        """
        try:
            quality = workflow_state.get('data_quality', {})
            quality_score = quality.get('completeness_score', 0)

            message = f"✅ Google Ads data is ready!\n\n"
            message += f"Client: {workflow_state.get('current_client', 'Unknown')}\n"
            message += f"Records: {workflow_state['record_count']:,}\n"
            message += f"Source: {workflow_state['data_source']}\n"
            message += f"Quality Score: {quality_score}%\n\n"

            if quality_score >= 90:
                message += "🎉 Excellent data quality! All operations available.\n\n"
            elif quality_score >= 70:
                message += "⚠️ Good data quality with minor issues.\n\n"
            else:
                message += "❌ Data quality issues detected. Review before proceeding.\n\n"

            message += "Available Operations:\n"
            for action in workflow_state['next_actions']:
                message += f"• {action}\n"

            messagebox.showinfo("Google Ads Workflow Ready", message)

        except Exception as e:
            self.google_ads_log(f"❌ Error showing workflow success message: {str(e)}")

    def get_workflow_progress_percentage(self):
        """
        Calculate overall workflow progress as percentage

        Returns:
            int: Progress percentage (0-100)
        """
        try:
            if not hasattr(self, 'current_google_ads_workflow_state'):
                return 0

            state = self.current_google_ads_workflow_state
            progress_items = []

            # Client selection (20%)
            progress_items.append(state.get('client_selected', False))

            # API or data availability (30%)
            progress_items.append(state.get('api_configured', False) or state.get('data_available', False))

            # Data loaded (30%)
            progress_items.append(state.get('data_available', False))

            # Airtable configured (20%)
            progress_items.append(state.get('airtable_configured', False))

            # Calculate weighted percentage
            weights = [20, 30, 30, 20]
            total_progress = sum(weight for i, weight in enumerate(weights) if progress_items[i])

            return total_progress

        except Exception as e:
            return 0

    def has_valid_google_ads_data(self):
        """
        Check if valid Google Ads data is currently loaded
        """
        return (
            hasattr(self, 'ad_spend_data') and
            self.ad_spend_data is not None and
            not self.ad_spend_data.empty
        )

    def get_google_ads_data_info(self):
        """
        Get information about currently loaded Google Ads data
        """
        if self.has_valid_google_ads_data():
            try:
                # Safely get date range
                date_start = str(self.ad_spend_data['Date'].min()) if 'Date' in self.ad_spend_data.columns else 'unknown'
                date_end = str(self.ad_spend_data['Date'].max()) if 'Date' in self.ad_spend_data.columns else 'unknown'

                return {
                    'source': getattr(self, 'google_ads_data_source', 'unknown'),
                    'record_count': len(self.ad_spend_data),
                    'column_count': len(self.ad_spend_data.columns),
                    'columns': self.ad_spend_data.columns.tolist(),
                    'date_range': {
                        'start': date_start,
                        'end': date_end
                    }
                }
            except Exception as e:
                # Fallback info if date parsing fails
                return {
                    'source': getattr(self, 'google_ads_data_source', 'unknown'),
                    'record_count': len(self.ad_spend_data),
                    'column_count': len(self.ad_spend_data.columns),
                    'columns': self.ad_spend_data.columns.tolist(),
                    'date_range': {
                        'start': 'unknown',
                        'end': 'unknown'
                    }
                }
        return None

    def save_google_ads_to_client_folder(self):
        """Save Google Ads data to client-specific folder"""
        try:
            # Check if we have valid data
            if not self.has_valid_google_ads_data():
                messagebox.showerror("No Data", "Please extract Google Ads data first.")
                return

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name or current_client_name == "No client":
                messagebox.showerror("No Client Selected", "Please select a client before saving data.")
                return

            # Get data info for logging
            data_info = self.get_google_ads_data_info()
            if not data_info:
                messagebox.showerror("Data Error", "Unable to get data information.")
                return

            self.google_ads_log(f"💾 Saving Google Ads data to client folder...")
            self.google_ads_log(f"👤 Client: {current_client_name}")
            self.google_ads_log(f"📊 Data: {data_info['record_count']:,} records from {data_info['source']}")

            # Generate filename with client-specific naming
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)

            # Create safe date range for filename
            date_start = str(data_info['date_range']['start']).replace('-', '').replace(':', '').replace(' ', '_')[:8]
            date_end = str(data_info['date_range']['end']).replace('-', '').replace(':', '').replace(' ', '_')[:8]
            date_range = f"{date_start}_to_{date_end}"

            csv_filename = f"{safe_client_name}_google_ads_{date_range}_{timestamp}.csv"
            xlsx_filename = f"{safe_client_name}_google_ads_{date_range}_{timestamp}.xlsx"

            # Save CSV to client folder
            csv_path = self.client_manager.get_client_file_path(current_client_name, csv_filename, "data")
            self.ad_spend_data.to_csv(csv_path, index=False)
            self.google_ads_log(f"✅ CSV saved to: {csv_path}")

            # Also save Excel version
            xlsx_path = self.client_manager.get_client_file_path(current_client_name, xlsx_filename, "exports")
            self.ad_spend_data.to_excel(xlsx_path, index=False, sheet_name="Google Ads Data")
            self.google_ads_log(f"✅ Excel saved to: {xlsx_path}")

            messagebox.showinfo(
                "Save Successful",
                f"✅ Google Ads data saved successfully!\n\n"
                f"Client: {current_client_name}\n"
                f"Records: {data_info['record_count']:,}\n"
                f"CSV: {csv_path}\n"
                f"Excel: {xlsx_path}"
            )

        except Exception as e:
            error_msg = f"Failed to save Google Ads data: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            self.google_ads_log(f"❌ Error details: {type(e).__name__}: {str(e)}")
            messagebox.showerror("Save Error", error_msg)

    def export_google_ads_for_client(self):
        """Export Google Ads data with client-specific options"""
        try:
            # Check if we have valid data
            if not self.has_valid_google_ads_data():
                messagebox.showerror("No Data", "Please extract Google Ads data first.")
                return

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before exporting data.")
                return

            # Create export dialog
            self.show_client_export_dialog(current_client_name)

        except Exception as e:
            error_msg = f"Failed to export Google Ads data: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Export Error", error_msg)

    def show_client_export_dialog(self, client_name):
        """Show client-specific export options dialog"""
        try:
            # Create dialog window
            dialog = tk.Toplevel(self.master)
            dialog.title(f"Export Google Ads Data - {client_name}")
            dialog.geometry("500x400")
            dialog.resizable(False, False)
            dialog.transient(self.master)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # Main frame
            main_frame = ttk_bootstrap.Frame(dialog, padding=20)
            main_frame.pack(fill=BOTH, expand=True)

            # Title
            title_label = ttk_bootstrap.Label(
                main_frame,
                text=f"📤 Export Google Ads Data for {client_name}",
                font=('Arial', 14, 'bold'),
                bootstyle="primary"
            )
            title_label.pack(pady=(0, 20))

            # Data summary
            data_info = self.get_google_ads_data_info()
            summary_frame = ttk_bootstrap.LabelFrame(main_frame, text="Data Summary", padding=15)
            summary_frame.pack(fill=X, pady=(0, 15))

            ttk_bootstrap.Label(
                summary_frame,
                text=f"Records: {data_info['record_count']:,}",
                font=('Arial', 10)
            ).pack(anchor='w')

            ttk_bootstrap.Label(
                summary_frame,
                text=f"Date Range: {data_info['date_range']['start']} to {data_info['date_range']['end']}",
                font=('Arial', 10)
            ).pack(anchor='w')

            ttk_bootstrap.Label(
                summary_frame,
                text=f"Source: {data_info['source']}",
                font=('Arial', 10)
            ).pack(anchor='w')

            # Export format selection
            format_frame = ttk_bootstrap.LabelFrame(main_frame, text="Export Format", padding=15)
            format_frame.pack(fill=X, pady=(0, 15))

            export_format_var = tk.StringVar(value="csv")

            ttk_bootstrap.Radiobutton(
                format_frame,
                text="📄 CSV (Comma Separated Values)",
                variable=export_format_var,
                value="csv",
                bootstyle="primary"
            ).pack(anchor='w', pady=2)

            ttk_bootstrap.Radiobutton(
                format_frame,
                text="📊 Excel (XLSX)",
                variable=export_format_var,
                value="excel",
                bootstyle="primary"
            ).pack(anchor='w', pady=2)

            # File naming preview
            preview_frame = ttk_bootstrap.LabelFrame(main_frame, text="File Name Preview", padding=15)
            preview_frame.pack(fill=X, pady=(0, 15))

            def update_preview():
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_client_name = self.client_manager.sanitize_folder_name(client_name)
                date_range = f"{data_info['date_range']['start']}_to_{data_info['date_range']['end']}"
                ext = ".xlsx" if export_format_var.get() == "excel" else ".csv"
                filename = f"{safe_client_name}_google_ads_{date_range}_{timestamp}{ext}"
                preview_text.config(state='normal')
                preview_text.delete(1.0, tk.END)
                preview_text.insert(1.0, filename)
                preview_text.config(state='disabled')

            preview_text = tk.Text(preview_frame, height=2, wrap=tk.WORD, font=('Consolas', 9))
            preview_text.pack(fill=X)

            # Bind preview update to format change
            export_format_var.trace('w', lambda *args: update_preview())
            update_preview()

            # Buttons frame
            buttons_frame = ttk_bootstrap.Frame(main_frame)
            buttons_frame.pack(fill=X, pady=(15, 0))

            def do_export():
                try:
                    format_choice = export_format_var.get()
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    safe_client_name = self.client_manager.sanitize_folder_name(client_name)
                    date_range = f"{data_info['date_range']['start']}_to_{data_info['date_range']['end']}"

                    if format_choice == "excel":
                        filename = f"{safe_client_name}_google_ads_{date_range}_{timestamp}.xlsx"
                        file_path = self.client_manager.get_client_file_path(client_name, filename, "exports")
                        self.ad_spend_data.to_excel(file_path, index=False, sheet_name="Google Ads Data")
                    else:
                        filename = f"{safe_client_name}_google_ads_{date_range}_{timestamp}.csv"
                        file_path = self.client_manager.get_client_file_path(client_name, filename, "exports")
                        self.ad_spend_data.to_csv(file_path, index=False)

                    self.google_ads_log(f"✅ Exported to: {file_path}")

                    dialog.destroy()

                    messagebox.showinfo(
                        "Export Successful",
                        f"✅ Google Ads data exported successfully!\n\n"
                        f"Client: {client_name}\n"
                        f"Format: {format_choice.upper()}\n"
                        f"File: {filename}\n"
                        f"Records: {data_info['record_count']:,}\n"
                        f"Location: {file_path}"
                    )

                except Exception as e:
                    dialog.destroy()
                    error_msg = f"Export failed: {str(e)}"
                    self.google_ads_log(f"❌ {error_msg}")
                    messagebox.showerror("Export Error", error_msg)

            ttk_bootstrap.Button(
                buttons_frame,
                text="📤 Export",
                command=do_export,
                bootstyle="success",
                width=15
            ).pack(side=RIGHT, padx=(5, 0))

            ttk_bootstrap.Button(
                buttons_frame,
                text="❌ Cancel",
                command=dialog.destroy,
                bootstyle="secondary",
                width=15
            ).pack(side=RIGHT)

        except Exception as e:
            error_msg = f"Failed to show export dialog: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            messagebox.showerror("Dialog Error", error_msg)

    def setup_ghl_sync_tab(self):
        """Setup enhanced GHL Data Hub with Google Sheets integration"""
        # Use the improved scrollable tab utility
        tab_frame, content_frame, self.ghl_canvas = self.create_scrollable_tab(
            self.notebook,
            "🎯 GHL Data Hub",
            debug_name="GHL_HUB"
        )

        # Initialize Google Sheets manager if not already done
        if not hasattr(self, 'sheets_manager'):
            self.sheets_manager = GoogleSheetsManager()

        # Enhanced GHL info with client context
        info_label = ttk_bootstrap.Label(
            content_frame,
            text="🎯 GHL Data Hub - Import, validate, and sync GoHighLevel data with client-specific organization",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # Client Context Section
        client_context_section = ttk_bootstrap.LabelFrame(content_frame, text="📋 Client Context", padding=15)
        client_context_section.pack(fill=X, pady=(0, 15))

        # Current client display
        client_info_frame = ttk_bootstrap.Frame(client_context_section)
        client_info_frame.pack(fill=X)

        ttk_bootstrap.Label(client_info_frame, text="Current Client:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_current_client_label = ttk_bootstrap.Label(
            client_info_frame,
            text="No client selected",
            font=('Arial', 10),
            bootstyle="warning"
        )
        self.ghl_current_client_label.pack(side=LEFT, padx=(10, 0))

        # Client folder info
        self.ghl_client_folder_label = ttk_bootstrap.Label(
            client_context_section,
            text="Select a client to see folder information",
            font=('Arial', 9),
            bootstyle="secondary"
        )
        self.ghl_client_folder_label.pack(anchor=W, pady=(5, 0))

        # Data Sources Section
        data_sources_section = ttk_bootstrap.LabelFrame(content_frame, text="📊 Data Sources", padding=15)
        data_sources_section.pack(fill=X, pady=(0, 15))

        # File Import subsection
        file_import_frame = ttk_bootstrap.LabelFrame(data_sources_section, text="📁 File Import", padding=10)
        file_import_frame.pack(fill=X, pady=(0, 10))

        # File selection
        file_frame = ttk_bootstrap.Frame(file_import_frame)
        file_frame.pack(fill=X, pady=5)

        ttk_bootstrap.Label(file_frame, text="GHL Data File:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_file_var = tk.StringVar()
        file_entry = ttk_bootstrap.Entry(file_frame, textvariable=self.ghl_file_var, width=50, font=('Arial', 10))
        file_entry.pack(side=LEFT, padx=(10, 5), fill=X, expand=True)

        ttk_bootstrap.Button(
            file_frame,
            text="📁 Browse",
            command=self.browse_ghl_file,
            bootstyle="secondary-outline",
            width=10
        ).pack(side=RIGHT)

        # Google Sheets Import subsection
        sheets_import_frame = ttk_bootstrap.LabelFrame(data_sources_section, text="📊 Google Sheets Import", padding=10)
        sheets_import_frame.pack(fill=X, pady=(0, 10))

        # Google Sheets authentication status
        sheets_auth_frame = ttk_bootstrap.Frame(sheets_import_frame)
        sheets_auth_frame.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Label(sheets_auth_frame, text="Authentication:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_sheets_auth_status = ttk_bootstrap.Label(
            sheets_auth_frame,
            text="❌ Not authenticated",
            font=('Arial', 10)
        )
        self.ghl_sheets_auth_status.pack(side=LEFT, padx=(10, 0))

        ttk_bootstrap.Button(
            sheets_auth_frame,
            text="🔐 Authenticate",
            command=self.authenticate_ghl_sheets,
            bootstyle="primary-outline",
            width=15
        ).pack(side=RIGHT)

        # Spreadsheet selection
        sheets_selection_frame = ttk_bootstrap.Frame(sheets_import_frame)
        sheets_selection_frame.pack(fill=X, pady=5)

        ttk_bootstrap.Label(sheets_selection_frame, text="Spreadsheet:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_spreadsheet_var = tk.StringVar()
        self.ghl_spreadsheet_combo = ttk_bootstrap.Combobox(
            sheets_selection_frame,
            textvariable=self.ghl_spreadsheet_var,
            width=40,
            font=('Arial', 10),
            state="readonly"
        )
        self.ghl_spreadsheet_combo.pack(side=LEFT, padx=(10, 5), fill=X, expand=True)

        ttk_bootstrap.Button(
            sheets_selection_frame,
            text="🔄 Refresh",
            command=self.refresh_ghl_spreadsheets,
            bootstyle="info-outline",
            width=10
        ).pack(side=RIGHT, padx=(5, 0))

        ttk_bootstrap.Button(
            sheets_selection_frame,
            text="➕ Manual",
            command=self.add_manual_ghl_spreadsheet,
            bootstyle="success-outline",
            width=10
        ).pack(side=RIGHT)

        # Worksheet selection
        worksheet_frame = ttk_bootstrap.Frame(sheets_import_frame)
        worksheet_frame.pack(fill=X, pady=5)

        ttk_bootstrap.Label(worksheet_frame, text="Worksheet:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_worksheet_var = tk.StringVar()
        self.ghl_worksheet_combo = ttk_bootstrap.Combobox(
            worksheet_frame,
            textvariable=self.ghl_worksheet_var,
            width=30,
            font=('Arial', 10),
            state="readonly"
        )
        self.ghl_worksheet_combo.pack(side=LEFT, padx=(10, 0), fill=X, expand=True)

        # Bind spreadsheet selection to worksheet refresh
        self.ghl_spreadsheet_combo.bind('<<ComboboxSelected>>', self.on_ghl_spreadsheet_select)

        # Bind worksheet selection to preview update
        self.ghl_worksheet_combo.bind('<<ComboboxSelected>>', self.on_ghl_worksheet_select)

        # Worksheet preview section
        preview_section = ttk_bootstrap.LabelFrame(sheets_import_frame, text="📋 Worksheet Preview", padding=10)
        preview_section.pack(fill=X, pady=(10, 0))

        # Preview info frame
        preview_info_frame = ttk_bootstrap.Frame(preview_section)
        preview_info_frame.pack(fill=X, pady=(0, 5))

        # Worksheet info labels
        self.ghl_worksheet_info = ttk_bootstrap.Label(
            preview_info_frame,
            text="Select a worksheet to see preview",
            font=('Arial', 9),
            bootstyle="secondary"
        )
        self.ghl_worksheet_info.pack(side=LEFT)

        # Preview button
        ttk_bootstrap.Button(
            preview_info_frame,
            text="👁️ Preview Data",
            command=self.preview_ghl_worksheet,
            bootstyle="info-outline",
            width=12
        ).pack(side=RIGHT)

        # API Configuration section (moved after data sources)
        api_section = ttk_bootstrap.LabelFrame(content_frame, text="🔧 API Configuration", padding=15)
        api_section.pack(fill=X, pady=(0, 15))

        # API Key
        ttk_bootstrap.Label(api_section, text="Airtable API Key:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.ghl_api_key_var = tk.StringVar()
        api_key_entry = ttk_bootstrap.Entry(api_section, textvariable=self.ghl_api_key_var, width=40, show="*", font=('Arial', 10))
        api_key_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(api_key_entry, "Your Airtable Personal Access Token")

        # Base ID (client-specific)
        ttk_bootstrap.Label(api_section, text="Base ID:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.ghl_base_id_var = tk.StringVar(value=AIRTABLE_BASE_ID)
        base_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.ghl_base_id_var, width=40, font=('Arial', 10))
        base_id_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(base_id_entry, "Airtable Base ID for GHL data")

        # Table ID (client-specific)
        ttk_bootstrap.Label(api_section, text="GHL Table ID:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.ghl_table_id_var = tk.StringVar(value="tblcdFVUC3zJrbmNf")
        table_id_entry = ttk_bootstrap.Entry(api_section, textvariable=self.ghl_table_id_var, width=40, font=('Arial', 10))
        table_id_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        ModernTooltip(table_id_entry, "GHL Table ID in Airtable (will be client-specific)")

        api_section.columnconfigure(1, weight=1)

        # Test connection button
        test_btn = ttk_bootstrap.Button(
            api_section,
            text="🔗 Test Connection",
            command=self.test_ghl_connection,
            bootstyle="info-outline",
            width=20
        )
        test_btn.grid(row=3, column=0, columnspan=2, pady=15)

        # Data Processing Section
        processing_section = ttk_bootstrap.LabelFrame(content_frame, text="🔍 Data Processing & Validation", padding=15)
        processing_section.pack(fill=X, pady=(0, 15))

        # Data processing actions
        processing_actions_frame = ttk_bootstrap.Frame(processing_section)
        processing_actions_frame.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="📂 Load & Preview File",
            command=self.load_and_preview_ghl_data,
            bootstyle="info",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="📊 Import from Sheets",
            command=self.import_ghl_from_sheets,
            bootstyle="primary",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            processing_actions_frame,
            text="🔍 Validate Data",
            command=self.validate_ghl_data,
            bootstyle="warning",
            width=20
        ).pack(side=LEFT, padx=5)

        # Data validation status
        validation_status_frame = ttk_bootstrap.Frame(processing_section)
        validation_status_frame.pack(fill=X, pady=(10, 0))

        ttk_bootstrap.Label(validation_status_frame, text="Validation Status:", font=('Arial', 10, 'bold')).pack(side=LEFT)
        self.ghl_validation_status = ttk_bootstrap.Label(
            validation_status_frame,
            text="No data loaded",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        self.ghl_validation_status.pack(side=LEFT, padx=(10, 0))

        # Sync Options section
        sync_section = ttk_bootstrap.LabelFrame(content_frame, text="Sync Options", padding=15)
        sync_section.pack(fill=X, pady=(0, 15))

        # Sync mode selection
        ttk_bootstrap.Label(sync_section, text="Sync Mode:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.ghl_sync_mode_var = tk.StringVar(value="incremental")

        sync_modes = [
            ("🔄 Incremental (Content-based)", "incremental"),
            ("⚡ Smart Incremental (Position-based)", "smart_incremental"),
            ("➕ Append (Add all records)", "append"),
            ("🔄 Replace (Clear table first)", "replace")
        ]

        for i, (text, value) in enumerate(sync_modes):
            radio = ttk_bootstrap.Radiobutton(
                sync_section,
                text=text,
                variable=self.ghl_sync_mode_var,
                value=value,
                bootstyle="primary"
            )
            radio.grid(row=i+1, column=0, sticky='w', pady=2, padx=20)

        # Mode descriptions
        mode_info = ttk_bootstrap.Label(
            sync_section,
            text="💡 Content-based: Compares all records | Smart: Finds last record position (faster)",
            font=('Arial', 9),
            bootstyle="info"
        )
        mode_info.grid(row=4, column=0, sticky='w', pady=(10, 5))

        # Auto-sync option
        self.ghl_auto_sync_var = tk.BooleanVar()
        auto_sync_check = ttk_bootstrap.Checkbutton(
            sync_section,
            text="Auto-sync after data import",
            variable=self.ghl_auto_sync_var,
            bootstyle="primary"
        )
        auto_sync_check.grid(row=5, column=0, sticky='w', pady=5)

        # Client-Specific Sync Actions section
        actions_section = ttk_bootstrap.LabelFrame(content_frame, text="🔄 Client-Specific Sync Actions", padding=15)
        actions_section.pack(fill=X, pady=(0, 15))

        # Primary action buttons
        sync_btn_frame = ttk_bootstrap.Frame(actions_section)
        sync_btn_frame.pack(fill=X)

        # Store button references for state management
        self.ghl_sync_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="🔄 Sync to Client Airtable",
            command=self.sync_ghl_to_client_airtable,
            bootstyle="success",
            width=22,
            state="disabled"  # Start disabled
        )
        self.ghl_sync_btn.pack(side=LEFT, padx=5)

        self.ghl_save_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="💾 Save to Client Folder",
            command=self.save_ghl_to_client_folder,
            bootstyle="primary",
            width=22,
            state="disabled"  # Start disabled
        )
        self.ghl_save_btn.pack(side=LEFT, padx=5)

        self.ghl_view_btn = ttk_bootstrap.Button(
            sync_btn_frame,
            text="📊 View Client Airtable",
            command=self.view_client_ghl_airtable,
            bootstyle="info-outline",
            width=22,
            state="disabled"  # Start disabled
        )
        self.ghl_view_btn.pack(side=LEFT, padx=5)



        # Second row of action buttons
        sync_btn_frame2 = ttk_bootstrap.Frame(actions_section)
        sync_btn_frame2.pack(fill=X, pady=(5, 0))

        ttk_bootstrap.Button(
            sync_btn_frame2,
            text="🔍 Find Duplicates",
            command=self.find_ghl_duplicates,
            bootstyle="warning-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            sync_btn_frame2,
            text="🧹 Clean Duplicates",
            command=self.clean_ghl_duplicates,
            bootstyle="danger-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            sync_btn_frame2,
            text="📊 Duplicate Report",
            command=self.show_duplicate_report,
            bootstyle="info-outline",
            width=20
        ).pack(side=LEFT, padx=5)

        # Status section
        status_section = ttk_bootstrap.LabelFrame(content_frame, text="Sync Status", padding=15)
        status_section.pack(fill=BOTH, expand=True)

        # Status text area - increased height for better readability
        self.ghl_status_text = scrolledtext.ScrolledText(
            status_section,
            height=15,
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        self.ghl_status_text.pack(fill=BOTH, expand=True)

        # Initialize with empty data
        self.ghl_data = None
        self.ghl_sheets_data = None  # Data from Google Sheets
        self.ghl_data_source = None  # Track data source: 'file' or 'sheets'

        # Update client context display
        self.update_ghl_client_context()

    def update_ghl_data_state(self, data_source=None, show_success_message=True):
        """
        Unified method to update GHL data state and UI after data loading
        """
        try:
            if self.ghl_data is not None and not self.ghl_data.empty:
                # Update data source tracking
                self.ghl_data_source = data_source or "unknown"

                # Update validation status
                record_count = len(self.ghl_data)
                source_text = "file" if data_source == "file" else "Google Sheets" if data_source == "sheets" else "unknown source"

                if hasattr(self, 'ghl_validation_status'):
                    self.ghl_validation_status.config(
                        text=f"✅ Data loaded: {record_count:,} records from {source_text}",
                        bootstyle="success"
                    )

                # Enable sync and save buttons
                self.update_ghl_button_states(True)

                # Log data summary
                self.ghl_log(f"📊 Data State Updated:")
                self.ghl_log(f"   - Source: {source_text}")
                self.ghl_log(f"   - Records: {record_count:,}")
                self.ghl_log(f"   - Columns: {len(self.ghl_data.columns)}")
                self.ghl_log(f"   - Column names: {', '.join(self.ghl_data.columns.tolist())}")

                # Show success message if requested
                if show_success_message:
                    current_client_name = self.client_var.get() if hasattr(self, 'client_var') else "No client"
                    messagebox.showinfo(
                        "Data Loaded Successfully",
                        f"✅ GHL data loaded successfully!\n\n"
                        f"Client: {current_client_name}\n"
                        f"Source: {source_text}\n"
                        f"Records: {record_count:,}\n"
                        f"Columns: {len(self.ghl_data.columns)}\n\n"
                        f"All sync and save operations are now available."
                    )

            else:
                # No data or empty data
                if hasattr(self, 'ghl_validation_status'):
                    self.ghl_validation_status.config(
                        text="No data loaded",
                        bootstyle="secondary"
                    )

                # Disable sync and save buttons
                self.update_ghl_button_states(False)

                self.ghl_log("⚠️ No valid data loaded")

        except Exception as e:
            self.ghl_log(f"❌ Error updating data state: {str(e)}")

    def update_ghl_button_states(self, enabled):
        """
        Enable or disable GHL sync and save buttons based on data availability
        """
        try:
            state = "normal" if enabled else "disabled"
            state_text = "enabled" if enabled else "disabled"

            # Update button states if they exist
            if hasattr(self, 'ghl_sync_btn'):
                self.ghl_sync_btn.config(state=state)
            if hasattr(self, 'ghl_save_btn'):
                self.ghl_save_btn.config(state=state)
            if hasattr(self, 'ghl_view_btn'):
                self.ghl_view_btn.config(state=state)

            self.ghl_log(f"🔘 Button states {state_text}: Sync to Airtable, Save to Folder, View Airtable")

        except Exception as e:
            self.ghl_log(f"❌ Error updating button states: {str(e)}")

    def has_valid_ghl_data(self):
        """
        Check if valid GHL data is currently loaded
        """
        return (
            hasattr(self, 'ghl_data') and
            self.ghl_data is not None and
            not self.ghl_data.empty
        )

    def get_ghl_data_info(self):
        """
        Get information about currently loaded GHL data
        """
        if self.has_valid_ghl_data():
            return {
                'source': getattr(self, 'ghl_data_source', 'unknown'),
                'record_count': len(self.ghl_data),
                'column_count': len(self.ghl_data.columns),
                'columns': self.ghl_data.columns.tolist()
            }
        return None

    def setup_google_sheets_tab(self):
        """Setup Google Sheets integration tab"""
        # Initialize Google Sheets manager
        self.sheets_manager = GoogleSheetsManager()

        # Use the improved scrollable tab utility
        tab_frame, main_container, self.sheets_canvas = self.create_scrollable_tab(
            self.notebook,
            "📊 Google Sheets",
            debug_name="SHEETS_TAB"
        )

        # Authentication section
        auth_section = ttk_bootstrap.LabelFrame(main_container, text="🔐 Authentication", padding=15)
        auth_section.pack(fill=X, pady=(0, 15))

        # Authentication status
        self.sheets_auth_status = ttk_bootstrap.Label(
            auth_section,
            text="❌ Not authenticated",
            font=('Arial', 10, 'bold')
        )
        self.sheets_auth_status.pack(anchor=W, pady=(0, 10))

        # Authentication buttons
        auth_btn_frame = ttk_bootstrap.Frame(auth_section)
        auth_btn_frame.pack(fill=X)

        ttk_bootstrap.Button(
            auth_btn_frame,
            text="🔑 Authenticate with Google",
            command=self.authenticate_google_sheets,
            bootstyle="success",
            width=25
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            auth_btn_frame,
            text="🔄 Refresh Connection",
            command=self.refresh_sheets_connection,
            bootstyle="info-outline",
            width=20
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            auth_btn_frame,
            text="📋 Test Connection",
            command=self.test_sheets_connection,
            bootstyle="secondary-outline",
            width=18
        ).pack(side=LEFT)

        # Spreadsheet selection section
        selection_section = ttk_bootstrap.LabelFrame(main_container, text="📂 Spreadsheet Selection", padding=15)
        selection_section.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Spreadsheet list frame
        list_frame = ttk_bootstrap.Frame(selection_section)
        list_frame.pack(fill=BOTH, expand=True)

        # Spreadsheet treeview
        columns = ('Name', 'Owner', 'Type', 'Modified', 'ID')
        self.sheets_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # Configure columns
        self.sheets_tree.heading('Name', text='Spreadsheet Name')
        self.sheets_tree.heading('Owner', text='Owner')
        self.sheets_tree.heading('Type', text='Type')
        self.sheets_tree.heading('Modified', text='Last Modified')
        self.sheets_tree.heading('ID', text='Spreadsheet ID')

        self.sheets_tree.column('Name', width=300)
        self.sheets_tree.column('Owner', width=150)
        self.sheets_tree.column('Type', width=80)
        self.sheets_tree.column('Modified', width=120)
        self.sheets_tree.column('ID', width=200)

        # Scrollbars for treeview
        sheets_v_scroll = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.sheets_tree.yview)
        sheets_h_scroll = ttk.Scrollbar(list_frame, orient=HORIZONTAL, command=self.sheets_tree.xview)
        self.sheets_tree.configure(yscrollcommand=sheets_v_scroll.set, xscrollcommand=sheets_h_scroll.set)

        self.sheets_tree.pack(side=LEFT, fill=BOTH, expand=True)
        sheets_v_scroll.pack(side=RIGHT, fill=Y)
        sheets_h_scroll.pack(side=BOTTOM, fill=X)

        # Bind selection event
        self.sheets_tree.bind('<<TreeviewSelect>>', self.on_spreadsheet_select)

        # Spreadsheet actions
        sheets_actions_frame = ttk_bootstrap.Frame(selection_section)
        sheets_actions_frame.pack(fill=X, pady=(10, 0))

        ttk_bootstrap.Button(
            sheets_actions_frame,
            text="🔄 Refresh List",
            command=self.refresh_spreadsheets_list,
            bootstyle="info-outline",
            width=15
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sheets_actions_frame,
            text="➕ Add Manual Entry",
            command=self.add_manual_spreadsheet,
            bootstyle="success",
            width=18
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sheets_actions_frame,
            text="📊 Load Selected",
            command=self.load_selected_spreadsheet,
            bootstyle="primary",
            width=15
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sheets_actions_frame,
            text="🌐 Open in Browser",
            command=self.open_spreadsheet_in_browser,
            bootstyle="secondary-outline",
            width=18
        ).pack(side=LEFT)

        # Worksheet selection
        worksheet_frame = ttk_bootstrap.LabelFrame(main_container, text="📋 Worksheet Selection", padding=15)
        worksheet_frame.pack(fill=X, pady=(0, 15))

        # Selected spreadsheet info
        self.selected_sheet_label = ttk_bootstrap.Label(
            worksheet_frame,
            text="No spreadsheet selected",
            font=('Arial', 9)
        )
        self.selected_sheet_label.pack(anchor=W, pady=(0, 10))

        # Worksheet dropdown
        worksheet_select_frame = ttk_bootstrap.Frame(worksheet_frame)
        worksheet_select_frame.pack(fill=X)

        ttk_bootstrap.Label(worksheet_select_frame, text="Worksheet:").pack(side=LEFT, padx=(0, 10))

        self.worksheet_var = tk.StringVar()
        self.worksheet_combo = ttk_bootstrap.Combobox(
            worksheet_select_frame,
            textvariable=self.worksheet_var,
            state="readonly",
            width=30
        )
        self.worksheet_combo.pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            worksheet_select_frame,
            text="📊 Preview Data",
            command=self.preview_worksheet_data,
            bootstyle="info-outline",
            width=15
        ).pack(side=LEFT)

        # Sync operations section
        sync_section = ttk_bootstrap.LabelFrame(main_container, text="🔄 Sync Operations", padding=15)
        sync_section.pack(fill=X, pady=(0, 15))

        # Sync buttons
        sync_btn_frame = ttk_bootstrap.Frame(sync_section)
        sync_btn_frame.pack(fill=X)

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="📥 Import from Sheets",
            command=self.import_from_sheets,
            bootstyle="success",
            width=20
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="📤 Export to Sheets",
            command=self.export_to_sheets,
            bootstyle="primary",
            width=18
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="🔄 Sync Both Ways",
            command=self.sync_bidirectional,
            bootstyle="warning",
            width=18
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bootstrap.Button(
            sync_btn_frame,
            text="💾 Save to Client Folder",
            command=self.save_sheets_to_client_folder,
            bootstyle="info",
            width=20
        ).pack(side=LEFT)

        # Status section
        status_section = ttk_bootstrap.LabelFrame(main_container, text="📊 Sync Status", padding=15)
        status_section.pack(fill=BOTH, expand=True)

        # Status text area - enhanced like GHL tab
        self.sheets_status_text = scrolledtext.ScrolledText(
            status_section,
            height=12,
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        self.sheets_status_text.pack(fill=BOTH, expand=True)

        # Initialize variables
        self.selected_spreadsheet_id = None
        self.selected_spreadsheet_name = None

    def setup_schedule_tab(self):
        """Setup scheduling tab"""
        schedule_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(schedule_frame, text="⏰ Schedule")

        # Schedule info
        info_label = ttk_bootstrap.Label(
            schedule_frame,
            text="Automated data extraction and export scheduling",
            font=('Arial', 11),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 15))

        # Schedule list
        schedule_list_frame = ttk_bootstrap.LabelFrame(schedule_frame, text="Scheduled Tasks", padding=15)
        schedule_list_frame.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Treeview for schedules
        columns = ('Type', 'Time', 'Format', 'Status', 'Last Run')
        self.schedule_tree = ttk_bootstrap.Treeview(
            schedule_list_frame,
            columns=columns,
            show='tree headings',
            height=8
        )

        # Configure columns
        self.schedule_tree.heading('#0', text='ID')
        self.schedule_tree.column('#0', width=50)

        for col in columns:
            self.schedule_tree.heading(col, text=col)
            self.schedule_tree.column(col, width=100)

        # Scrollbar for treeview
        schedule_scrollbar = ttk_bootstrap.Scrollbar(schedule_list_frame, orient=VERTICAL, command=self.schedule_tree.yview)
        self.schedule_tree.configure(yscrollcommand=schedule_scrollbar.set)

        self.schedule_tree.pack(side=LEFT, fill=BOTH, expand=True)
        schedule_scrollbar.pack(side=RIGHT, fill=Y)

        # Schedule buttons
        schedule_btn_frame = ttk_bootstrap.Frame(schedule_frame)
        schedule_btn_frame.pack(fill=X)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="➕ Add Schedule",
            command=self.add_schedule_dialog,
            bootstyle="success-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="✏️ Edit Schedule",
            command=self.edit_schedule_dialog,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="🗑️ Remove Schedule",
            command=self.remove_schedule_dialog,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            schedule_btn_frame,
            text="▶️ Run Now",
            command=self.run_schedule_now,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

    def setup_settings_tab(self):
        """Setup settings tab"""
        settings_frame = ttk_bootstrap.Frame(self.notebook, padding=15)
        self.notebook.add(settings_frame, text="⚙️ Settings")

        # Theme section
        theme_section = ttk_bootstrap.LabelFrame(settings_frame, text="Appearance", padding=15)
        theme_section.pack(fill=X, pady=(0, 15))

        ttk_bootstrap.Label(theme_section, text="Theme:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.theme_var = tk.StringVar(value=self.theme)
        theme_combo = ttk_bootstrap.Combobox(
            theme_section,
            textvariable=self.theme_var,
            values=["flatly", "darkly", "cosmo", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            state="readonly",
            width=20
        )
        theme_combo.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        theme_combo.bind('<<ComboboxSelected>>', self.on_theme_change)

        theme_section.columnconfigure(1, weight=1)

        # Configuration section
        config_section = ttk_bootstrap.LabelFrame(settings_frame, text="Configuration", padding=15)
        config_section.pack(fill=X, pady=(0, 15))

        # Auto-save option
        self.auto_save_var = tk.BooleanVar(value=self.config_manager.config.get("auto_save", True))
        auto_save_check = ttk_bootstrap.Checkbutton(
            config_section,
            text="Auto-save settings",
            variable=self.auto_save_var,
            bootstyle="primary"
        )
        auto_save_check.grid(row=0, column=0, sticky='w', pady=5)

        # Show tooltips option
        self.show_tooltips_var = tk.BooleanVar(value=self.config_manager.config.get("show_tooltips", True))
        tooltips_check = ttk_bootstrap.Checkbutton(
            config_section,
            text="Show tooltips",
            variable=self.show_tooltips_var,
            bootstyle="primary"
        )
        tooltips_check.grid(row=1, column=0, sticky='w', pady=5)

        # Configuration buttons
        config_btn_frame = ttk_bootstrap.Frame(settings_frame)
        config_btn_frame.pack(fill=X, pady=15)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="💾 Save Settings",
            command=self.save_settings,
            bootstyle="success"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="📁 Export Config",
            command=self.export_config,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="📂 Import Config",
            command=self.import_config,
            bootstyle="warning-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            config_btn_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_config,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

    def setup_right_panel(self, parent):
        """Setup the right panel with dashboard and data view"""
        # Create notebook for right panel tabs
        right_notebook = ttk_bootstrap.Notebook(parent, bootstyle="info")
        right_notebook.pack(fill=BOTH, expand=True)

        # Dashboard tab
        self.setup_dashboard_tab(right_notebook)

        # Data view tab
        self.setup_data_view_tab(right_notebook)

        # Log tab
        self.setup_log_tab(right_notebook)

    def setup_dashboard_tab(self, parent):
        """Setup dashboard with KPIs"""
        dashboard_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(dashboard_frame, text="📊 Dashboard")

        # KPI Cards container
        kpi_container = ttk_bootstrap.Frame(dashboard_frame)
        kpi_container.pack(fill=X, pady=(0, 20))

        # Create KPI cards
        self.kpi_cards = {}

        # Total Cost KPI
        self.kpi_cards['cost'] = ModernKPICard(
            kpi_container,
            "Total Cost",
            "$0.00",
            color="primary"
        )
        self.kpi_cards['cost'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Total Clicks KPI
        self.kpi_cards['clicks'] = ModernKPICard(
            kpi_container,
            "Total Clicks",
            "0",
            color="success"
        )
        self.kpi_cards['clicks'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Total Impressions KPI
        self.kpi_cards['impressions'] = ModernKPICard(
            kpi_container,
            "Total Impressions",
            "0",
            color="info"
        )
        self.kpi_cards['impressions'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Conversion Rate KPI
        self.kpi_cards['conv_rate'] = ModernKPICard(
            kpi_container,
            "Conv. Rate",
            "0.00%",
            color="warning"
        )
        self.kpi_cards['conv_rate'].pack(side=LEFT, fill=X, expand=True, padx=5)

        # Quick actions section
        actions_section = ttk_bootstrap.LabelFrame(dashboard_frame, text="Quick Actions", padding=15)
        actions_section.pack(fill=X, pady=(0, 20))

        # Quick action buttons
        quick_actions = [
            ("🚀 Quick Extract", self.quick_extract, "primary"),
            ("📊 Generate Report", self.generate_report, "success"),
            ("📤 Quick Export", self.quick_export, "info"),
            ("🔄 Refresh Data", self.refresh_data, "warning")
        ]

        for i, (text, command, style) in enumerate(quick_actions):
            btn = ttk_bootstrap.Button(
                actions_section,
                text=text,
                command=command,
                bootstyle=f"{style}-outline",
                width=15
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=5, sticky='ew')

        actions_section.columnconfigure(0, weight=1)
        actions_section.columnconfigure(1, weight=1)

        # Recent activity section
        activity_section = ttk_bootstrap.LabelFrame(dashboard_frame, text="Recent Activity", padding=15)
        activity_section.pack(fill=BOTH, expand=True)

        # Activity indicator and controls
        activity_controls = ttk_bootstrap.Frame(activity_section)
        activity_controls.pack(fill=X, pady=(0, 10))

        # Activity indicator
        self.activity_indicator = ttk_bootstrap.Label(
            activity_controls,
            text="🟢 Ready",
            font=('Arial', 10, 'bold'),
            bootstyle="success"
        )
        self.activity_indicator.pack(side=LEFT)

        # Clear activity button
        clear_activity_btn = ttk_bootstrap.Button(
            activity_controls,
            text="🗑️ Clear",
            command=self.clear_activity_log,
            bootstyle="secondary-outline",
            width=10
        )
        clear_activity_btn.pack(side=RIGHT)

        # Activity listbox
        self.activity_listbox = tk.Listbox(
            activity_section,
            height=8,
            font=('Arial', 9)
        )
        self.activity_listbox.pack(fill=BOTH, expand=True)

        # Add some sample activities
        self.add_activity("Application started")
        self.add_activity("Configuration loaded")

    def setup_data_view_tab(self, parent):
        """Setup data view tab with functional table"""
        data_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(data_frame, text="📋 Data View")

        # Data controls frame
        controls_frame = ttk_bootstrap.Frame(data_frame)
        controls_frame.pack(fill=X, pady=(0, 10))

        # Data action buttons
        ttk_bootstrap.Button(
            controls_frame,
            text="🔄 Refresh View",
            command=self.refresh_data_view,
            bootstyle="info-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            controls_frame,
            text="👁️ Preview Data",
            command=self.preview_data,
            bootstyle="primary-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            controls_frame,
            text="📤 Export Data",
            command=self.export_data,
            bootstyle="success-outline",
            width=15
        ).pack(side=LEFT, padx=5)

        # Data table frame
        table_frame = ttk_bootstrap.Frame(data_frame)
        table_frame.pack(fill=BOTH, expand=True)

        # Create treeview for data display
        columns = ('Date', 'Campaign', 'Cost', 'Clicks', 'Impressions', 'Conversions')
        self.data_tree = ttk_bootstrap.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=12,
            bootstyle="primary"
        )

        # Configure columns
        column_widths = {'Date': 100, 'Campaign': 250, 'Cost': 100, 'Clicks': 80,
                        'Impressions': 120, 'Conversions': 100}

        for col in columns:
            self.data_tree.heading(col, text=col, anchor='center')
            self.data_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Scrollbars
        v_scrollbar = ttk_bootstrap.Scrollbar(table_frame, orient=VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk_bootstrap.Scrollbar(table_frame, orient=HORIZONTAL, command=self.data_tree.xview)

        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for better control
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # Data summary frame
        summary_frame = ttk_bootstrap.Frame(data_frame)
        summary_frame.pack(fill=X, pady=(10, 0))

        self.data_summary_label = ttk_bootstrap.Label(
            summary_frame,
            text="No data loaded - Use 'Extract Data' or 'Load CSV Data' to populate this view",
            font=('Arial', 10),
            bootstyle="secondary"
        )
        self.data_summary_label.pack()

        # Initialize with empty data
        self.refresh_data_view()

    def refresh_data_view(self):
        """Refresh the data view tab with current data"""
        try:
            # Clear existing data
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # Check if we have data to display
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None and not self.ad_spend_data.empty:
                # Get the first 50 rows for display (to avoid performance issues)
                display_data = self.ad_spend_data.head(50)

                for index, row in display_data.iterrows():
                    # Format the data for display
                    values = []

                    # Date
                    values.append(str(row.get('Date', '')))

                    # Campaign Name
                    campaign_name = str(row.get('Campaign Name', ''))
                    if len(campaign_name) > 30:
                        campaign_name = campaign_name[:27] + "..."
                    values.append(campaign_name)

                    # Cost
                    cost = row.get('Cost', 0)
                    values.append(f"${cost:.2f}")

                    # Clicks
                    clicks = row.get('Clicks', 0)
                    values.append(f"{int(clicks):,}")

                    # Impressions
                    impressions = row.get('Impressions', 0)
                    values.append(f"{int(impressions):,}")

                    # Conversions
                    conversions = row.get('Conversions', 0)
                    values.append(f"{conversions:.2f}")

                    self.data_tree.insert('', 'end', values=values)

                # Update summary
                total_records = len(self.ad_spend_data)
                total_cost = self.ad_spend_data['Cost'].sum() if 'Cost' in self.ad_spend_data.columns else 0

                if total_records > 50:
                    summary_text = f"Showing first 50 of {total_records:,} records | Total Cost: ${total_cost:,.2f}"
                else:
                    summary_text = f"Showing {total_records:,} records | Total Cost: ${total_cost:,.2f}"

                self.data_summary_label.config(text=summary_text)

            else:
                # No data available
                self.data_summary_label.config(text="No data loaded - Use 'Extract Data' or 'Load CSV Data' to populate this view")

        except Exception as e:
            self.log(f"❌ Error refreshing data view: {str(e)}")
            self.data_summary_label.config(text="Error loading data view")

    def setup_log_tab(self, parent):
        """Setup log tab"""
        log_frame = ttk_bootstrap.Frame(parent, padding=15)
        parent.add(log_frame, text="📝 Log")

        # Log controls
        log_controls = ttk_bootstrap.Frame(log_frame)
        log_controls.pack(fill=X, pady=(0, 10))

        ttk_bootstrap.Button(
            log_controls,
            text="🗑️ Clear Log",
            command=self.clear_log,
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            log_controls,
            text="💾 Save Log",
            command=self.save_log,
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=20,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.log_text.pack(fill=BOTH, expand=True)

    def setup_status_bar(self):
        """Setup modern status bar"""
        status_frame = ttk_bootstrap.Frame(self.master)
        status_frame.pack(side=BOTTOM, fill=X)

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk_bootstrap.Label(
            status_frame,
            textvariable=self.status_var,
            font=('Arial', 9),
            padding=5
        )
        status_label.pack(side=LEFT)

        # Progress bar (hidden by default)
        self.status_progress = ttk_bootstrap.Progressbar(
            status_frame,
            mode='indeterminate',
            bootstyle="info-striped",
            length=200
        )

        # Connection status
        self.connection_status = ttk_bootstrap.Label(
            status_frame,
            text="🔴 Disconnected",
            font=('Arial', 9),
            padding=5
        )
        self.connection_status.pack(side=RIGHT)

        # Theme indicator
        theme_label = ttk_bootstrap.Label(
            status_frame,
            text=f"Theme: {self.theme.title()}",
            font=('Arial', 9),
            padding=5
        )
        theme_label.pack(side=RIGHT)

    # Core functionality methods (simplified for demo)
    def log(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # Add to log text widget
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, formatted_message + "\n")
            self.log_text.see(tk.END)

        # Add to activity list
        self.add_activity(message)

        # Update status
        self.status_var.set(message)

        print(formatted_message)  # Also print to console

    def add_activity(self, activity):
        """Add activity to recent activity list"""
        if hasattr(self, 'activity_listbox'):
            timestamp = datetime.datetime.now().strftime("%H:%M")
            self.activity_listbox.insert(0, f"{timestamp} - {activity}")

            # Keep only last 20 activities
            if self.activity_listbox.size() > 20:
                self.activity_listbox.delete(20, tk.END)

    def set_activity_indicator(self, status, message):
        """Update activity indicator"""
        if hasattr(self, 'activity_indicator'):
            if status == "working":
                self.activity_indicator.config(text=f"🔄 {message}", bootstyle="warning")
            elif status == "success":
                self.activity_indicator.config(text=f"✅ {message}", bootstyle="success")
            elif status == "error":
                self.activity_indicator.config(text=f"❌ {message}", bootstyle="danger")
            else:
                self.activity_indicator.config(text=f"🟢 {message}", bootstyle="success")

    def clear_activity_log(self):
        """Clear the activity log"""
        if hasattr(self, 'activity_listbox'):
            self.activity_listbox.delete(0, tk.END)
            self.add_activity("Activity log cleared")

    def clear_log(self):
        """Clear the log"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        self.log("Log cleared")

    def save_log(self):
        """Save log to file"""
        if hasattr(self, 'log_text'):
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                try:
                    with open(filename, 'w') as f:
                        f.write(self.log_text.get(1.0, tk.END))
                    self.log(f"Log saved to {filename}")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def change_theme(self, theme):
        """Change application theme"""
        try:
            self.theme = theme
            self.style = ttk_bootstrap.Style(theme=theme)
            self.config_manager.config["theme"] = theme
            if self.auto_save_var.get():
                self.config_manager.save_config()
            self.log(f"Theme changed to {theme.title()}")
        except Exception as e:
            messagebox.showerror("Theme Error", f"Failed to change theme: {str(e)}")

    def on_theme_change(self, event=None):
        """Handle theme change from combobox"""
        self.change_theme(self.theme_var.get())

    def on_date_preset_change(self, event=None):
        """Handle date preset change"""
        preset = self.date_preset_var.get()
        if preset in DATE_PRESETS and preset != "Custom":
            start_date, end_date = DATE_PRESETS[preset]()
            if start_date and end_date:
                self.start_date_var.set(start_date)
                self.end_date_var.set(end_date)
                self.log(f"Date range set to: {preset} ({start_date} to {end_date})")

    def on_client_select(self, event=None):
        """Enhanced client selection handler with dynamic context updates"""
        client_name = self.client_var.get()

        if client_name and client_name != "No client":
            self.log(f"🔄 Client selection changed to: {client_name}")

            # Find client in config
            client_found = False
            for client in self.config_manager.config.get("clients", []):
                if client.get("client_name") == client_name:
                    self.customer_id_var.set(client.get("customer_id_formatted", ""))
                    self.current_client = client
                    client_found = True
                    break

            if client_found:
                # Comprehensive client setup and context updates
                self.setup_client_context(client_name)
            else:
                self.log(f"⚠️ Client {client_name} not found in configuration")
        else:
            # Handle no client selected
            self.clear_client_context()

    def setup_client_context(self, client_name):
        """
        Comprehensive client context setup including folder creation and status updates

        Args:
            client_name (str): Name of the selected client
        """
        try:
            self.log(f"🔧 Setting up context for client: {client_name}")

            # 1. Ensure client folder structure exists
            if hasattr(self, 'client_manager'):
                self.client_manager.create_client_folder(client_name)

                # Create subfolders for different data types
                subfolders = ['google_ads', 'ghl', 'reports', 'exports']
                for subfolder in subfolders:
                    try:
                        subfolder_path = os.path.join(
                            self.client_manager.get_client_folder(client_name),
                            subfolder
                        )
                        os.makedirs(subfolder_path, exist_ok=True)
                    except Exception as e:
                        self.log(f"⚠️ Could not create subfolder {subfolder}: {str(e)}")

                self.log(f"📁 Client folder structure ready for {client_name}")

            # 2. Update table IDs for the client
            self.ensure_client_table_ids(client_name)

            # 3. Update all context displays
            self.update_all_client_contexts(client_name)

            # 4. Update button states
            self.update_all_button_states()

            # 5. Update status indicators
            self.update_client_status_indicators()

            # 6. Log comprehensive status
            self.log_client_status(client_name)

            # 7. Update main status
            self.status_var.set(f"Client: {client_name} - Ready")

        except Exception as e:
            error_msg = f"Error setting up client context: {str(e)}"
            self.log(f"❌ {error_msg}")

    def ensure_client_table_ids(self, client_name):
        """
        Ensure client has proper table IDs for all data types

        Args:
            client_name (str): Client name
        """
        try:
            # Check if client has table IDs configured
            clients = self.config_manager.config.get("clients", [])
            client_config = None

            for client in clients:
                if client.get("client_name") == client_name:
                    client_config = client
                    break

            if client_config:
                # Ensure table_ids section exists
                if "table_ids" not in client_config:
                    client_config["table_ids"] = {}

                # Generate missing table IDs
                data_types = ["google_ads", "ghl"]
                updated = False

                for data_type in data_types:
                    if data_type not in client_config["table_ids"]:
                        table_id = self.generate_client_table_id(client_name, data_type)
                        client_config["table_ids"][data_type] = table_id
                        updated = True
                        self.log(f"📊 Generated {data_type} table ID: {table_id}")

                if updated:
                    self.config_manager.save_config()
                    self.log(f"💾 Updated table IDs for {client_name}")

        except Exception as e:
            self.log(f"❌ Error ensuring table IDs: {str(e)}")

    def update_all_client_contexts(self, client_name):
        """
        Update all client context displays across the application

        Args:
            client_name (str): Client name
        """
        try:
            # Update Google Ads context
            if hasattr(self, 'google_ads_current_client_label'):
                self.update_google_ads_client_context()

            # Update GHL context
            if hasattr(self, 'ghl_current_client_label'):
                self.update_ghl_client_context()

            # Update any other context displays
            self.log(f"🔄 Updated all context displays for {client_name}")

        except Exception as e:
            self.log(f"❌ Error updating contexts: {str(e)}")

    def update_all_button_states(self):
        """Update button states across all tabs"""
        try:
            # Update Google Ads buttons
            if hasattr(self, 'google_ads_data_loaded'):
                self.update_google_ads_button_states(self.google_ads_data_loaded)

            # Update GHL buttons
            if hasattr(self, 'ghl_data_loaded'):
                self.update_ghl_button_states(self.ghl_data_loaded)

        except Exception as e:
            self.log(f"❌ Error updating button states: {str(e)}")

    def log_client_status(self, client_name):
        """
        Log comprehensive client status information

        Args:
            client_name (str): Client name
        """
        try:
            status = self.get_comprehensive_client_status(client_name)

            self.log("📊 Client Status Summary:")
            self.log(f"   Client: {client_name}")
            self.log(f"   API: {status.get('api_status', 'Unknown')}")
            self.log(f"   Data: {status.get('data_status', 'Unknown')}")
            self.log(f"   Folder: {status.get('folder_status', 'Unknown')}")
            self.log(f"   Airtable: {status.get('airtable_status', 'Unknown')}")

            table_ids = status.get('table_ids', {})
            if table_ids:
                self.log(f"   Table IDs: {table_ids}")

        except Exception as e:
            self.log(f"❌ Error logging client status: {str(e)}")

    def clear_client_context(self):
        """Clear client context when no client is selected"""
        try:
            self.log("🔄 Clearing client context")

            # Clear current client
            self.current_client = None

            # Update context displays
            if hasattr(self, 'google_ads_current_client_label'):
                self.update_google_ads_client_context()

            if hasattr(self, 'ghl_current_client_label'):
                self.update_ghl_client_context()

            # Update button states
            self.update_all_button_states()

            # Update main status
            self.status_var.set("Ready - No client selected")

        except Exception as e:
            self.log(f"❌ Error clearing client context: {str(e)}")

    def load_settings(self):
        """Load settings from configuration"""
        config = self.config_manager.config

        # Load credentials
        self.customer_id_var.set(config["credentials"].get("customer_id", ""))
        self.developer_token_var.set(config["credentials"].get("developer_token", ""))
        self.refresh_token_var.set(config["credentials"].get("refresh_token", ""))
        self.manager_account_var.set(config["credentials"].get("manager_account_id", "**********"))

        # Load Google Ads sync settings
        google_ads_config = config.get("google_ads_sync", {})
        if hasattr(self, 'airtable_api_key_var'):
            self.airtable_api_key_var.set(google_ads_config.get("api_key", ""))
        if hasattr(self, 'google_ads_base_id_var'):
            self.google_ads_base_id_var.set(google_ads_config.get("base_id", AIRTABLE_BASE_ID))
        if hasattr(self, 'google_ads_table_id_var'):
            self.google_ads_table_id_var.set(google_ads_config.get("table_id", AIRTABLE_GOOGLE_ADS_TABLE_ID))
        if hasattr(self, 'google_ads_auto_sync_var'):
            self.google_ads_auto_sync_var.set(google_ads_config.get("auto_sync", False))
        if hasattr(self, 'google_ads_sync_mode_var'):
            self.google_ads_sync_mode_var.set(google_ads_config.get("sync_mode", "incremental"))

        # Load GHL sync settings
        ghl_config = config.get("ghl_sync", {})
        if hasattr(self, 'ghl_api_key_var'):
            self.ghl_api_key_var.set(ghl_config.get("api_key", ""))
        if hasattr(self, 'ghl_base_id_var'):
            self.ghl_base_id_var.set(ghl_config.get("base_id", AIRTABLE_BASE_ID))
        if hasattr(self, 'ghl_table_id_var'):
            self.ghl_table_id_var.set(ghl_config.get("table_id", "tblcdFVUC3zJrbmNf"))
        if hasattr(self, 'ghl_auto_sync_var'):
            self.ghl_auto_sync_var.set(ghl_config.get("auto_sync", False))
        if hasattr(self, 'ghl_sync_mode_var'):
            self.ghl_sync_mode_var.set(ghl_config.get("sync_mode", "incremental"))

        # Load other settings
        self.date_preset_var.set(config.get("last_date_preset", "Last 30 days"))
        self.export_format_var.set(config.get("last_export_format", "csv"))

        # Load clients
        self.update_client_list()

        self.log("Settings loaded successfully")

    def save_settings(self):
        """Save current settings"""
        try:
            config = self.config_manager.config

            # Save credentials
            config["credentials"]["customer_id"] = self.customer_id_var.get()
            config["credentials"]["developer_token"] = self.developer_token_var.get()
            config["credentials"]["refresh_token"] = self.refresh_token_var.get()
            config["credentials"]["manager_account_id"] = self.manager_account_var.get()

            # Save Google Ads sync settings
            if hasattr(self, 'airtable_api_key_var'):
                config["google_ads_sync"]["api_key"] = self.airtable_api_key_var.get()
            if hasattr(self, 'google_ads_base_id_var'):
                config["google_ads_sync"]["base_id"] = self.google_ads_base_id_var.get()
            if hasattr(self, 'google_ads_table_id_var'):
                config["google_ads_sync"]["table_id"] = self.google_ads_table_id_var.get()
            if hasattr(self, 'google_ads_auto_sync_var'):
                config["google_ads_sync"]["auto_sync"] = self.google_ads_auto_sync_var.get()
            if hasattr(self, 'google_ads_sync_mode_var'):
                config["google_ads_sync"]["sync_mode"] = self.google_ads_sync_mode_var.get()

            # Save GHL sync settings
            if hasattr(self, 'ghl_api_key_var'):
                config["ghl_sync"]["api_key"] = self.ghl_api_key_var.get()
            if hasattr(self, 'ghl_base_id_var'):
                config["ghl_sync"]["base_id"] = self.ghl_base_id_var.get()
            if hasattr(self, 'ghl_table_id_var'):
                config["ghl_sync"]["table_id"] = self.ghl_table_id_var.get()
            if hasattr(self, 'ghl_auto_sync_var'):
                config["ghl_sync"]["auto_sync"] = self.ghl_auto_sync_var.get()
            if hasattr(self, 'ghl_sync_mode_var'):
                config["ghl_sync"]["sync_mode"] = self.ghl_sync_mode_var.get()

            # Save other settings
            config["last_date_preset"] = self.date_preset_var.get()
            config["last_export_format"] = self.export_format_var.get()
            config["theme"] = self.theme
            config["auto_save"] = self.auto_save_var.get()
            config["show_tooltips"] = self.show_tooltips_var.get()

            # Save window geometry
            config["window_geometry"] = self.master.geometry()

            # Save to file
            if self.config_manager.save_config():
                self.log("Settings saved successfully")
                messagebox.showinfo("Success", "Settings saved successfully!")
            else:
                messagebox.showerror("Error", "Failed to save settings")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def update_client_list(self):
        """Update client combobox with both configured and detected clients"""
        # Get clients from config
        config_clients = self.config_manager.config.get("clients", [])
        config_client_names = [client.get("client_name", "") for client in config_clients]

        # Get existing client folders
        existing_folders = self.client_manager.get_existing_client_folders()

        # Combine and deduplicate
        all_client_names = list(set(config_client_names + existing_folders))
        all_client_names.sort()

        # Auto-add detected clients to config if they're not already there
        for folder_name in existing_folders:
            if folder_name not in config_client_names:
                # Create a basic client entry for detected folder
                new_client = {
                    "client_name": folder_name,
                    "customer_id": "",
                    "customer_id_formatted": "",
                    "notes": f"Auto-detected from folder: {folder_name}",
                    "added_date": datetime.datetime.now().isoformat(),
                    "auto_detected": True
                }
                self.config_manager.config.setdefault("clients", []).append(new_client)
                self.log(f"📁 Auto-detected client folder: {folder_name}")

        if hasattr(self, 'client_combo'):
            self.client_combo['values'] = all_client_names
            if all_client_names and not self.client_var.get():
                self.client_var.set(all_client_names[0])

        # Save config if we added auto-detected clients
        if existing_folders and any(folder not in config_client_names for folder in existing_folders):
            self.config_manager.save_config()

    def open_simple_calendar(self, date_var):
        """Open a simple date picker dialog"""
        try:
            # Parse current date
            current_date_str = date_var.get()
            try:
                current_date = datetime.datetime.strptime(current_date_str, '%Y-%m-%d')
            except:
                current_date = datetime.datetime.now()

            # Create date picker dialog
            dialog = tk.Toplevel(self.master)
            dialog.title("📅 Select Date")
            dialog.geometry("350x200")
            dialog.resizable(False, False)
            dialog.transient(self.master)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (350 // 2)
            y = (dialog.winfo_screenheight() // 2) - (200 // 2)
            dialog.geometry(f"350x200+{x}+{y}")

            result = [None]  # Use list to store result for closure

            # Main frame
            main_frame = tk.Frame(dialog, padx=20, pady=20)
            main_frame.pack(fill='both', expand=True)

            # Date selection frame
            date_frame = tk.Frame(main_frame)
            date_frame.pack(pady=10)

            # Year selection
            tk.Label(date_frame, text="Year:", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
            year_var = tk.StringVar(value=str(current_date.year))
            year_spinbox = tk.Spinbox(date_frame, from_=2020, to=2030, textvariable=year_var, width=8, font=('Arial', 10))
            year_spinbox.grid(row=0, column=1, padx=5, pady=5)

            # Month selection
            tk.Label(date_frame, text="Month:", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
            months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
            month_var = tk.StringVar(value=f"{current_date.month:02d}")
            month_combo = ttk.Combobox(date_frame, textvariable=month_var, values=months, width=6, font=('Arial', 10))
            month_combo.grid(row=0, column=3, padx=5, pady=5)

            # Day selection
            tk.Label(date_frame, text="Day:", font=('Arial', 10, 'bold')).grid(row=1, column=0, padx=5, pady=5)
            day_var = tk.StringVar(value=f"{current_date.day:02d}")
            day_spinbox = tk.Spinbox(date_frame, from_=1, to=31, textvariable=day_var, width=8, font=('Arial', 10))
            day_spinbox.grid(row=1, column=1, padx=5, pady=5)

            # Current date display
            current_label = tk.Label(date_frame, text=f"Current: {current_date.strftime('%Y-%m-%d')}",
                                   font=('Arial', 9), fg='blue')
            current_label.grid(row=1, column=2, columnspan=2, padx=5, pady=5)

            # Button frame
            btn_frame = tk.Frame(main_frame)
            btn_frame.pack(pady=20)

            def on_ok():
                try:
                    year = int(year_var.get())
                    month = int(month_var.get())
                    day = int(day_var.get())
                    selected_date = datetime.date(year, month, day)
                    result[0] = selected_date.strftime('%Y-%m-%d')
                    dialog.destroy()
                except ValueError:
                    messagebox.showerror("Invalid Date", "Please enter a valid date")

            def on_cancel():
                result[0] = None
                dialog.destroy()

            def on_today():
                today = datetime.date.today()
                year_var.set(str(today.year))
                month_var.set(f"{today.month:02d}")
                day_var.set(f"{today.day:02d}")
                result[0] = today.strftime('%Y-%m-%d')
                dialog.destroy()

            # Buttons with modern styling
            tk.Button(btn_frame, text="Today", command=on_today, bg='#17A2B8', fg='white',
                     font=('Arial', 10), padx=15, pady=5).pack(side='left', padx=10)
            tk.Button(btn_frame, text="OK", command=on_ok, bg='#28A745', fg='white',
                     font=('Arial', 10), padx=20, pady=5).pack(side='right', padx=5)
            tk.Button(btn_frame, text="Cancel", command=on_cancel, bg='#DC3545', fg='white',
                     font=('Arial', 10), padx=15, pady=5).pack(side='right', padx=5)

            # Wait for dialog to close
            self.master.wait_window(dialog)

            # Update the date variable if a date was selected
            if result[0]:
                date_var.set(result[0])
                self.log(f"✅ Date selected: {result[0]}")
            else:
                self.log("Calendar dialog cancelled")

        except Exception as e:
            self.log(f"❌ Error opening calendar: {str(e)}")
            messagebox.showerror("Calendar Error", f"Failed to open calendar: {str(e)}")

    # Real Google Ads functionality (from working ads.py)
    def get_oauth_tokens(self):
        """Start OAuth flow to get refresh token for Google Ads API"""
        self.log("🔐 Starting Google Ads OAuth flow...")
        self.log(f"🔍 Debug: GOOGLE_ADS_CLIENT_ID = {GOOGLE_ADS_CLIENT_ID}")
        self.log(f"🔍 Debug: GOOGLE_ADS_SCOPES = {GOOGLE_ADS_SCOPES}")

        def oauth_thread():
            try:
                # Use the gads-secret.json file directly
                gads_secrets_file = "gads-secret.json"

                if not os.path.exists(gads_secrets_file):
                    self.log(f"❌ Google Ads credentials file not found: {gads_secrets_file}")
                    messagebox.showerror(
                        "Google Ads Credentials File Missing",
                        f"❌ Could not find {gads_secrets_file}\n\n"
                        "Please ensure the Google Ads credentials file is in the same folder as the application."
                    )
                    return

                self.log(f"📄 Using Google Ads credentials file: {gads_secrets_file}")

                # Start OAuth flow for Google Ads API only
                self.log(f"🚀 Starting Google Ads OAuth flow with scopes: {GOOGLE_ADS_SCOPES}")
                flow = InstalledAppFlow.from_client_secrets_file(
                    gads_secrets_file, GOOGLE_ADS_SCOPES)

                self.log("🌐 Opening browser for Google Ads authentication...")
                self.log("✅ Using Google Ads project: gads-460612")

                self.credentials = flow.run_local_server(port=0)

                # Update UI with refresh token
                self.refresh_token_var.set(self.credentials.refresh_token)
                self.log(f"✅ Successfully obtained refresh token: {self.credentials.refresh_token[:10]}...")
                self.log(f"🔍 Debug: Token scopes = {getattr(self.credentials, 'scopes', 'Not available')}")
                messagebox.showinfo("Success", "Google Ads OAuth tokens obtained successfully!")

            except Exception as e:
                self.log(f"❌ Error during OAuth flow: {str(e)}")
                self.log(f"🔍 Debug: Exception type = {type(e).__name__}")

                # Check if it's a scope-related error
                error_str = str(e).lower()
                if 'ghl' in error_str or 'sheets' in error_str or 'organization' in error_str:
                    self.log("🚨 DETECTED: This appears to be a Google Sheets authentication error!")
                    self.log("🔧 SOLUTION: The CLIENT_ID/CLIENT_SECRET are configured for Google Sheets, not Google Ads")
                    messagebox.showerror(
                        "Authentication Configuration Error",
                        "❌ The OAuth credentials are configured for Google Sheets, not Google Ads API.\n\n"
                        "🔧 To fix this:\n"
                        "1. Create a new Google Cloud project for Google Ads API\n"
                        "2. Enable Google Ads API in the project\n"
                        "3. Create new OAuth credentials\n"
                        "4. Update CLIENT_ID and CLIENT_SECRET in the code\n\n"
                        "Current credentials are for 'ghl-sheets' project."
                    )
                else:
                    messagebox.showerror("OAuth Error", f"Failed to obtain OAuth tokens: {str(e)}")

            finally:
                self.status_var.set("Ready")

        # Run OAuth flow in a separate thread to keep UI responsive
        self.status_var.set("Running OAuth flow...")
        threading.Thread(target=oauth_thread, daemon=True).start()

    def validate_credentials(self):
        """Validate credentials"""
        if not self.customer_id_var.get() or not self.developer_token_var.get() or not self.refresh_token_var.get():
            messagebox.showerror("Missing Credentials", "Please provide all required Google Ads credentials")
            return False
        return True

    def test_api_connection(self):
        """Test API connection - enhanced for Google Ads Data Hub"""
        if not self.validate_credentials():
            return

        self.google_ads_log("🔗 Testing Google Ads API connection...")
        try:
            # Create Google Ads client
            client_config = {
                "client_id": GOOGLE_ADS_CLIENT_ID,
                "client_secret": GOOGLE_ADS_CLIENT_SECRET,
                "refresh_token": self.refresh_token_var.get(),
                "developer_token": self.developer_token_var.get(),
                "login_customer_id": self.manager_account_var.get(),
                "use_proto_plus": True
            }

            client = GoogleAdsClient.load_from_dict(client_config)
            customer_id = self.customer_id_var.get().replace("-", "")

            # Simple test query
            ga_service = client.get_service("GoogleAdsService")
            query = "SELECT customer.id FROM customer LIMIT 1"

            search_request = client.get_type("SearchGoogleAdsRequest")
            search_request.customer_id = customer_id
            search_request.query = query

            results = ga_service.search(request=search_request)

            # If we get here, connection is successful
            self.google_ads_log("✅ API connection test successful!")
            self.log("✅ Google Ads API connection test successful!")
            messagebox.showinfo("Success", "Google Ads API connection test successful!")

            # Update client context to show API is working
            self.update_google_ads_client_context()

        except Exception as e:
            error_msg = f"API connection test failed: {str(e)}"
            self.google_ads_log(f"❌ {error_msg}")
            self.log(f"❌ Google Ads {error_msg}")
            messagebox.showerror("Connection Error", error_msg)

    def start_extraction(self):
        """Start the data extraction process"""
        # Validate inputs
        if not self.validate_credentials():
            return

        def extraction_thread():
            try:
                self.log("Starting data extraction...")
                self.google_ads_log("🚀 Starting Google Ads data extraction...")
                self.status_var.set("Extracting data...")

                # Get date range from StringVar
                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()

                # Create Google Ads client
                client_config = {
                    "client_id": GOOGLE_ADS_CLIENT_ID,
                    "client_secret": GOOGLE_ADS_CLIENT_SECRET,
                    "refresh_token": self.refresh_token_var.get(),
                    "developer_token": self.developer_token_var.get(),
                    "login_customer_id": self.manager_account_var.get(),
                    "use_proto_plus": True
                }

                client = GoogleAdsClient.load_from_dict(client_config)

                # Create query
                query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.conversions,
                    segments.date
                FROM campaign
                WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY segments.date
                """

                # Execute query
                ga_service = client.get_service("GoogleAdsService")
                customer_id = self.customer_id_var.get().replace("-", "")

                self.log(f"Executing query for customer ID: {customer_id}")
                self.log(f"Date range: {start_date} to {end_date}")

                # Make the request
                search_request = client.get_type("SearchGoogleAdsRequest")
                search_request.customer_id = customer_id
                search_request.query = query

                results = ga_service.search(request=search_request)

                # Process results
                data = []
                for row in results:
                    campaign_id = row.campaign.id
                    campaign_name = row.campaign.name
                    cost = row.metrics.cost_micros / 1000000  # Convert micros to actual currency
                    impressions = row.metrics.impressions
                    clicks = row.metrics.clicks
                    conversions = row.metrics.conversions
                    date = row.segments.date

                    data.append({
                        'Date': date,
                        'Campaign ID': campaign_id,
                        'Campaign Name': campaign_name,
                        'Cost': cost,
                        'Impressions': impressions,
                        'Clicks': clicks,
                        'Conversions': conversions,
                        'CTR': (clicks / impressions if impressions > 0 else 0),
                        'CPC': (cost / clicks if clicks > 0 else 0),
                        'Conv. Rate': (conversions / clicks if clicks > 0 else 0),
                        'Cost per Conv.': (cost / conversions if conversions > 0 else 0)
                    })

                # Convert to DataFrame
                self.ad_spend_data = pd.DataFrame(data)

                # Display summary
                if not data:
                    self.log("No data found for the specified date range.")
                else:
                    total_cost = self.ad_spend_data['Cost'].sum()
                    total_clicks = self.ad_spend_data['Clicks'].sum()
                    total_impressions = self.ad_spend_data['Impressions'].sum()
                    total_conversions = self.ad_spend_data['Conversions'].sum()

                    self.log(f"✅ AGGREGATED data extraction complete. Found {len(data)} records.")
                    self.log(f"⚠️ WARNING: This is AGGREGATED conversion data (all conversion types combined)")
                    self.log(f"⚠️ For accurate CPA calculations, use '🎯 Granular Conversions' instead")
                    self.log(f"💰 Total Cost: ${total_cost:.2f}")
                    self.log(f"👆 Total Clicks: {total_clicks}")
                    self.log(f"👁️ Total Impressions: {total_impressions}")
                    self.log(f"🎯 Total Conversions (AGGREGATED): {total_conversions:.2f}")

                    # Update data state for client-specific operations
                    self.google_ads_log("🔄 Updating data state and enabling buttons...")
                    self.update_google_ads_data_state(data_source="api", show_success_message=False)

                    # Update KPI cards if they exist
                    if hasattr(self, 'cost_card'):
                        self.cost_card.update_value(f"${total_cost:.2f}")
                    if hasattr(self, 'clicks_card'):
                        self.clicks_card.update_value(f"{total_clicks:,}")
                    if hasattr(self, 'impressions_card'):
                        self.impressions_card.update_value(f"{total_impressions:,}")
                    if hasattr(self, 'conversion_card'):
                        ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
                        self.conversion_card.update_value(f"{ctr:.2f}%")

                    # Show first few rows in log
                    self.log("\n📊 Sample data (first 3 rows):")
                    sample_data = self.ad_spend_data.head(3)
                    for _, row in sample_data.iterrows():
                        self.log(f"📅 {row['Date']} | 📢 {row['Campaign Name']} | 💰 ${row['Cost']:.2f} | 👆 {row['Clicks']}")

                    # Refresh the data view tab
                    self.refresh_data_view()

                    # Auto-sync to Airtable if enabled
                    if hasattr(self, 'google_ads_auto_sync_var') and self.google_ads_auto_sync_var.get():
                        api_key = self.airtable_api_key_var.get().strip() if hasattr(self, 'airtable_api_key_var') else ""
                        if api_key:
                            self.log("🔄 Auto-syncing to Airtable...")
                            try:
                                # Create manager with Google Ads settings
                                temp_manager = AirtableManager(api_key)
                                temp_manager.base_id = self.google_ads_base_id_var.get().strip()
                                temp_manager.google_ads_table_id = self.google_ads_table_id_var.get().strip()

                                sync_mode = self.google_ads_sync_mode_var.get() if hasattr(self, 'google_ads_sync_mode_var') else "incremental"
                                uploaded_count, errors, skipped_count = temp_manager.upload_google_ads_data(
                                    self.ad_spend_data,
                                    mode=sync_mode
                                )
                                if errors:
                                    self.log(f"⚠️ Google Ads auto-sync completed with {len(errors)} errors")
                                else:
                                    result_msg = f"✅ Auto-synced {uploaded_count} records to Airtable"
                                    if sync_mode == "incremental" and skipped_count > 0:
                                        result_msg += f" (skipped {skipped_count} existing)"
                                    self.log(result_msg)
                            except Exception as e:
                                self.log(f"❌ Google Ads auto-sync failed: {str(e)}")
                        else:
                            self.log("⚠️ Auto-sync enabled but no Airtable API key configured")

                self.status_var.set("Data extraction complete")

            except GoogleAdsException as ex:
                self.log(f"❌ Google Ads API error: {ex}")
                for error in ex.failure.errors:
                    self.log(f"Error: {error.message}")
                messagebox.showerror("Google Ads API Error", f"Failed to extract data: {ex.failure.errors[0].message}")

            except Exception as e:
                self.log(f"❌ Error during data extraction: {str(e)}")
                messagebox.showerror("Extraction Error", f"Failed to extract data: {str(e)}")

            finally:
                self.status_var.set("Ready")

        # Run extraction in a separate thread
        threading.Thread(target=extraction_thread, daemon=True).start()

    def extract_granular_conversion_data(self):
        """Extract granular conversion data with individual conversion actions"""
        def extraction_thread():
            try:
                self.status_var.set("Extracting granular conversion data...")
                self.log("🎯 Starting granular conversion data extraction...")

                # Validate credentials
                if not self.validate_credentials():
                    return

                # Get date range
                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()

                if not start_date or not end_date:
                    messagebox.showerror("Missing Dates", "Please select start and end dates")
                    return

                self.log(f"📅 Date range: {start_date} to {end_date}")

                # Setup Google Ads client
                client_config = {
                    "client_id": GOOGLE_ADS_CLIENT_ID,
                    "client_secret": GOOGLE_ADS_CLIENT_SECRET,
                    "refresh_token": self.refresh_token_var.get(),
                    "developer_token": self.developer_token_var.get(),
                    "login_customer_id": self.manager_account_var.get(),
                    "use_proto_plus": True
                }

                client = GoogleAdsClient.load_from_dict(client_config)

                # Enhanced query with conversion action segmentation
                # Note: Some metrics are not available with conversion action segmentation
                query = f"""
                SELECT
                    segments.date,
                    campaign.id,
                    campaign.name,
                    segments.conversion_action,
                    segments.conversion_action_name,
                    conversion_action.category,
                    conversion_action.type,
                    conversion_action.primary_for_goal,
                    conversion_action.counting_type,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.conversions,
                    metrics.conversions_value,
                    metrics.all_conversions,
                    metrics.all_conversions_value,
                    metrics.view_through_conversions
                FROM campaign
                WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
                AND metrics.impressions > 0
                ORDER BY segments.date, campaign.id, segments.conversion_action
                """

                # Execute query
                ga_service = client.get_service("GoogleAdsService")
                customer_id = self.customer_id_var.get().replace("-", "")

                self.log(f"🔍 Executing granular conversion query for customer ID: {customer_id}")

                # Make the request
                search_request = client.get_type("SearchGoogleAdsRequest")
                search_request.customer_id = customer_id
                search_request.query = query

                results = ga_service.search(request=search_request)

                # Process results with granular conversion data
                data = []
                conversion_actions_seen = set()

                for row in results:
                    # Basic campaign info
                    campaign_id = row.campaign.id
                    campaign_name = row.campaign.name
                    date = row.segments.date

                    # Conversion action details
                    conversion_action_id = row.segments.conversion_action
                    conversion_action_name = row.segments.conversion_action_name
                    conversion_category = row.conversion_action.category.name if hasattr(row.conversion_action.category, 'name') else 'Unknown'
                    conversion_type = row.conversion_action.type.name if hasattr(row.conversion_action.type, 'name') else 'Unknown'
                    is_primary = row.conversion_action.primary_for_goal
                    counting_type = row.conversion_action.counting_type.name if hasattr(row.conversion_action.counting_type, 'name') else 'Unknown'

                    # Metrics
                    cost = row.metrics.cost_micros / 1_000_000
                    impressions = row.metrics.impressions
                    clicks = row.metrics.clicks
                    conversions = row.metrics.conversions
                    conversion_value = row.metrics.conversions_value
                    all_conversions = row.metrics.all_conversions
                    all_conversion_value = row.metrics.all_conversions_value
                    view_through_conversions = row.metrics.view_through_conversions

                    # Calculate metrics manually since they're not available with conversion action segmentation
                    cost_per_conversion = cost / conversions if conversions > 0 else 0
                    cost_per_all_conversions = cost / all_conversions if all_conversions > 0 else 0
                    conversion_rate = (conversions / clicks * 100) if clicks > 0 else 0
                    all_conversion_rate = (all_conversions / clicks * 100) if clicks > 0 else 0
                    ctr = (clicks / impressions * 100) if impressions > 0 else 0
                    cpc = cost / clicks if clicks > 0 else 0

                    # Track unique conversion actions
                    conversion_actions_seen.add(conversion_action_name)

                    data.append({
                        'Date': date,
                        'Campaign ID': campaign_id,
                        'Campaign Name': campaign_name,
                        'Conversion Action ID': conversion_action_id,
                        'Conversion Action Name': conversion_action_name,
                        'Conversion Category': conversion_category,
                        'Conversion Type': conversion_type,
                        'Is Primary for Goal': is_primary,
                        'Counting Type': counting_type,
                        'Cost': cost,
                        'Impressions': impressions,
                        'Clicks': clicks,
                        'Conversions': conversions,
                        'Conversion Value': conversion_value,
                        'All Conversions': all_conversions,
                        'All Conversion Value': all_conversion_value,
                        'View Through Conversions': view_through_conversions,
                        'Cost per Conversion': cost_per_conversion,
                        'Cost per All Conversions': cost_per_all_conversions,
                        'Conversion Rate': conversion_rate,
                        'All Conversion Rate': all_conversion_rate,
                        'CTR': ctr,
                        'CPC': cpc
                    })

                # Convert to DataFrame
                self.granular_conversion_data = pd.DataFrame(data)

                # Display summary
                if not data:
                    self.log("❌ No granular conversion data found for the specified date range.")
                else:
                    self.log(f"✅ Granular conversion data extraction complete!")
                    self.log(f"📊 Found {len(data)} records across {len(conversion_actions_seen)} conversion actions")
                    self.log(f"🎯 Conversion Actions Found:")
                    for action in sorted(conversion_actions_seen):
                        action_data = self.granular_conversion_data[
                            self.granular_conversion_data['Conversion Action Name'] == action
                        ]
                        total_conversions = action_data['Conversions'].sum()
                        total_value = action_data['Conversion Value'].sum()
                        self.log(f"   • {action}: {total_conversions:.2f} conversions (${total_value:.2f} value)")

                    # Calculate totals
                    total_cost = self.granular_conversion_data['Cost'].sum()
                    total_clicks = self.granular_conversion_data['Clicks'].sum()
                    total_impressions = self.granular_conversion_data['Impressions'].sum()
                    total_conversions = self.granular_conversion_data['Conversions'].sum()
                    total_conversion_value = self.granular_conversion_data['Conversion Value'].sum()

                    self.log(f"💰 Total Cost: ${total_cost:.2f}")
                    self.log(f"👆 Total Clicks: {total_clicks:,}")
                    self.log(f"👁️ Total Impressions: {total_impressions:,}")
                    self.log(f"🎯 Total Conversions: {total_conversions:.2f}")
                    self.log(f"💎 Total Conversion Value: ${total_conversion_value:.2f}")

                    # Show sample data
                    self.log("\n📋 Sample granular data (first 3 rows):")
                    sample_data = self.granular_conversion_data.head(3)
                    for _, row in sample_data.iterrows():
                        self.log(f"   {row['Date']} | {row['Campaign Name'][:30]}... | "
                               f"{row['Conversion Action Name']} | {row['Conversions']:.2f} conv | "
                               f"${row['Cost']:.2f}")

                    # Update data state
                    self.google_ads_log("🔄 Updating data state for granular conversion data...")

                    messagebox.showinfo("Success",
                        f"Granular conversion data extracted successfully!\n\n"
                        f"• {len(data):,} records\n"
                        f"• {len(conversion_actions_seen)} conversion actions\n"
                        f"• ${total_cost:,.2f} total cost\n"
                        f"• {total_conversions:.2f} total conversions")

            except GoogleAdsException as ex:
                self.log(f"❌ Google Ads API error: {ex}")
                for error in ex.failure.errors:
                    self.log(f"Error: {error.message}")
                messagebox.showerror("Google Ads API Error", f"Failed to extract granular data: {ex.failure.errors[0].message}")

            except Exception as e:
                self.log(f"❌ Error extracting granular conversion data: {str(e)}")
                messagebox.showerror("Error", f"Failed to extract granular conversion data: {str(e)}")

            finally:
                self.status_var.set("Ready")

        # Run extraction in a separate thread
        threading.Thread(target=extraction_thread, daemon=True).start()

    def load_csv_data(self):
        """Load data from an existing CSV file"""
        try:
            self.log("📂 Opening file dialog to select CSV file...")

            # Open file dialog to select CSV file
            file_path = filedialog.askopenfilename(
                title="Select CSV file to load",
                filetypes=[
                    ("CSV files", "*.csv"),
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ],
                initialdir=os.getcwd()
            )

            if not file_path:
                self.log("File selection cancelled")
                return

            self.log(f"📄 Loading data from: {os.path.basename(file_path)}")
            self.status_var.set("Loading CSV data...")

            # Determine file type and load accordingly
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension == '.csv':
                # Try different encodings for CSV files
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                loaded_data = None

                for encoding in encodings:
                    try:
                        loaded_data = pd.read_csv(file_path, encoding=encoding)
                        self.log(f"✅ Successfully loaded CSV with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.log(f"❌ Error with {encoding} encoding: {str(e)}")
                        continue

                if loaded_data is None:
                    raise Exception("Could not load CSV file with any supported encoding")

            elif file_extension in ['.xlsx', '.xls']:
                loaded_data = pd.read_excel(file_path)
                self.log(f"✅ Successfully loaded Excel file")
            else:
                # Try to load as CSV anyway
                loaded_data = pd.read_csv(file_path)
                self.log(f"✅ Successfully loaded file as CSV")

            # Validate and process the loaded data
            if loaded_data.empty:
                raise Exception("The loaded file is empty")

            # Check if it looks like Google Ads data by looking for expected columns
            expected_columns = ['Date', 'Campaign Name', 'Cost', 'Clicks', 'Impressions']
            missing_columns = [col for col in expected_columns if col not in loaded_data.columns]

            if missing_columns:
                self.log(f"⚠️ Warning: Missing expected columns: {missing_columns}")
                self.log("📊 Available columns: " + ", ".join(loaded_data.columns.tolist()))

                # Ask user if they want to continue anyway
                response = messagebox.askyesno(
                    "Column Mismatch",
                    f"The loaded file is missing some expected Google Ads columns:\n\n"
                    f"Missing: {', '.join(missing_columns)}\n"
                    f"Available: {', '.join(loaded_data.columns.tolist())}\n\n"
                    f"Do you want to continue loading this data anyway?"
                )

                if not response:
                    self.log("Data loading cancelled by user")
                    self.status_var.set("Ready")
                    return

            # Store the loaded data
            self.ad_spend_data = loaded_data

            # Calculate summary statistics
            total_records = len(loaded_data)

            # Try to calculate metrics if the columns exist
            summary_stats = {}
            if 'Cost' in loaded_data.columns:
                summary_stats['Total Cost'] = loaded_data['Cost'].sum()
            if 'Clicks' in loaded_data.columns:
                summary_stats['Total Clicks'] = loaded_data['Clicks'].sum()
            if 'Impressions' in loaded_data.columns:
                summary_stats['Total Impressions'] = loaded_data['Impressions'].sum()
            if 'Conversions' in loaded_data.columns:
                summary_stats['Total Conversions'] = loaded_data['Conversions'].sum()

            # Update KPI cards if they exist and we have the data
            if hasattr(self, 'kpi_cards'):
                if 'Total Cost' in summary_stats:
                    self.kpi_cards['cost'].update_value(f"${summary_stats['Total Cost']:,.2f}")
                if 'Total Clicks' in summary_stats:
                    self.kpi_cards['clicks'].update_value(f"{summary_stats['Total Clicks']:,}")
                if 'Total Impressions' in summary_stats:
                    self.kpi_cards['impressions'].update_value(f"{summary_stats['Total Impressions']:,}")
                if 'Total Clicks' in summary_stats and 'Total Impressions' in summary_stats:
                    ctr = (summary_stats['Total Clicks'] / summary_stats['Total Impressions'] * 100) if summary_stats['Total Impressions'] > 0 else 0
                    self.kpi_cards['conv_rate'].update_value(f"{ctr:.2f}%")

            # Log success message with summary
            self.log(f"✅ Data loaded successfully!")
            self.log(f"📊 Records loaded: {total_records:,}")
            self.log(f"📋 Columns: {', '.join(loaded_data.columns.tolist())}")

            # Log summary statistics
            for stat_name, stat_value in summary_stats.items():
                if stat_name == 'Total Cost':
                    self.log(f"💰 {stat_name}: ${stat_value:,.2f}")
                else:
                    self.log(f"📈 {stat_name}: {stat_value:,.0f}")

            # If client is selected, offer to save a copy to client folder
            current_client_name = self.client_var.get()
            if current_client_name:
                result = messagebox.askyesno(
                    "Save to Client Folder",
                    f"Would you like to save a copy of this data to the '{current_client_name}' client folder?\n\n"
                    f"This will help keep all client data organized in one place."
                )

                if result:
                    try:
                        # Create filename with timestamp
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        original_filename = os.path.basename(file_path)
                        name_part, ext_part = os.path.splitext(original_filename)
                        safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)

                        new_filename = f"{safe_client_name}_imported_{name_part}_{timestamp}{ext_part}"
                        client_file_path = self.client_manager.get_client_file_path(current_client_name, new_filename, "data")

                        # Save copy to client folder
                        if file_extension in ['.xlsx', '.xls']:
                            loaded_data.to_excel(client_file_path, index=False)
                        else:
                            loaded_data.to_csv(client_file_path, index=False)

                        self.log(f"📁 Saved copy to client folder: {client_file_path}")
                        self.add_activity(f"Imported data file for {current_client_name}")

                    except Exception as e:
                        self.log(f"⚠️ Could not save to client folder: {str(e)}")

            # Refresh the data view tab
            self.refresh_data_view()

            # Show success message
            success_message = f"Successfully loaded {total_records:,} records from:\n{os.path.basename(file_path)}\n\n"
            success_message += f"You can now use 'Preview Data' to view the loaded data or check the 'Data View' tab."
            if current_client_name:
                success_message += f"\n\nClient: {current_client_name}"

            messagebox.showinfo("Data Loaded Successfully", success_message)

            self.status_var.set("Data loaded successfully")

        except Exception as e:
            error_msg = f"Failed to load CSV data: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)
            self.status_var.set("Ready")

    def preview_data(self):
        """Enhanced preview of Google Ads data with detailed statistics and analysis"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None or self.ad_spend_data.empty:
            messagebox.showerror("No Data", "Please extract or load Google Ads data first before previewing")
            self.google_ads_log("❌ No data available for preview")
            return

        try:
            self.google_ads_log("📊 Opening enhanced data preview window...")

            # Create preview window
            preview_window = tk.Toplevel(self.master)
            preview_window.title("📊 Google Ads Data Preview - Enhanced Analysis")
            preview_window.geometry("1400x800")
            preview_window.transient(self.master)

            # Center the window
            preview_window.update_idletasks()
            x = (preview_window.winfo_screenwidth() // 2) - (1400 // 2)
            y = (preview_window.winfo_screenheight() // 2) - (800 // 2)
            preview_window.geometry(f"1400x800+{x}+{y}")

            # Main frame
            main_frame = ttk_bootstrap.Frame(preview_window, padding=15)
            main_frame.pack(fill=BOTH, expand=True)

            # Header frame with enhanced summary info
            header_frame = ttk_bootstrap.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 15))

            # Data source and summary info
            data_source = self.google_ads_data_source or "unknown"
            current_client = self.client_var.get() if hasattr(self, 'client_var') else "No client"

            # Title with data source info
            title_frame = ttk_bootstrap.Frame(header_frame)
            title_frame.pack(fill=X, pady=(0, 10))

            ttk_bootstrap.Label(
                title_frame,
                text=f"📊 Google Ads Data Preview - {len(self.ad_spend_data):,} Records",
                font=('Arial', 14, 'bold'),
                bootstyle="primary"
            ).pack(side=LEFT)

            ttk_bootstrap.Label(
                title_frame,
                text=f"Source: {data_source.upper()} | Client: {current_client}",
                font=('Arial', 10),
                bootstyle="secondary"
            ).pack(side=RIGHT)

            # Enhanced statistics calculation
            total_records = len(self.ad_spend_data)

            # Safe column access with fallbacks
            cost_col = 'Cost' if 'Cost' in self.ad_spend_data.columns else None
            clicks_col = 'Clicks' if 'Clicks' in self.ad_spend_data.columns else None
            impressions_col = 'Impressions' if 'Impressions' in self.ad_spend_data.columns else None
            conversions_col = 'Conversions' if 'Conversions' in self.ad_spend_data.columns else None
            campaigns_col = 'Campaign Name' if 'Campaign Name' in self.ad_spend_data.columns else None
            date_col = 'Date' if 'Date' in self.ad_spend_data.columns else None

            # Calculate statistics safely
            total_cost = self.ad_spend_data[cost_col].sum() if cost_col else 0
            total_clicks = self.ad_spend_data[clicks_col].sum() if clicks_col else 0
            total_impressions = self.ad_spend_data[impressions_col].sum() if impressions_col else 0
            total_conversions = self.ad_spend_data[conversions_col].sum() if conversions_col else 0
            unique_campaigns = self.ad_spend_data[campaigns_col].nunique() if campaigns_col else 0

            # Date range analysis
            date_range_text = "N/A"
            if date_col:
                try:
                    dates = pd.to_datetime(self.ad_spend_data[date_col])
                    min_date = dates.min().strftime('%Y-%m-%d')
                    max_date = dates.max().strftime('%Y-%m-%d')
                    date_range_text = f"{min_date} to {max_date}"
                except:
                    date_range_text = "Invalid date format"

            # Performance metrics
            avg_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            avg_cpc = (total_cost / total_clicks) if total_clicks > 0 else 0
            conv_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0

            # Enhanced summary stats in multiple rows
            stats_frame = ttk_bootstrap.Frame(header_frame)
            stats_frame.pack(fill=X, pady=(10, 0))

            # Primary metrics (first row)
            primary_stats = [
                ("💰 Total Cost", f"${total_cost:,.2f}", "success"),
                ("👆 Total Clicks", f"{total_clicks:,}", "info"),
                ("👁️ Total Impressions", f"{total_impressions:,}", "warning"),
                ("🎯 Total Conversions", f"{total_conversions:,.2f}", "danger")
            ]

            for i, (label, value, style) in enumerate(primary_stats):
                stat_frame = ttk_bootstrap.Frame(stats_frame, bootstyle=style, padding=8)
                stat_frame.grid(row=0, column=i, padx=3, pady=3, sticky='ew')

                ttk_bootstrap.Label(
                    stat_frame,
                    text=label,
                    font=('Arial', 9, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

                ttk_bootstrap.Label(
                    stat_frame,
                    text=value,
                    font=('Arial', 11, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

            # Secondary metrics (second row)
            secondary_stats = [
                ("📊 Campaigns", f"{unique_campaigns:,}", "secondary"),
                ("📅 Date Range", date_range_text, "secondary"),
                ("📈 Avg CTR", f"{avg_ctr:.2f}%", "secondary"),
                ("💵 Avg CPC", f"${avg_cpc:.2f}", "secondary")
            ]

            for i, (label, value, style) in enumerate(secondary_stats):
                stat_frame = ttk_bootstrap.Frame(stats_frame, bootstyle=style, padding=8)
                stat_frame.grid(row=1, column=i, padx=3, pady=3, sticky='ew')

                ttk_bootstrap.Label(
                    stat_frame,
                    text=label,
                    font=('Arial', 9, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

                ttk_bootstrap.Label(
                    stat_frame,
                    text=value,
                    font=('Arial', 10, 'bold'),
                    bootstyle=f"{style}-inverse"
                ).pack()

            # Configure grid weights for both rows
            for i in range(4):
                stats_frame.columnconfigure(i, weight=1)

            # Data quality indicators
            quality_frame = ttk_bootstrap.LabelFrame(header_frame, text="📋 Data Quality Analysis", padding=10)
            quality_frame.pack(fill=X, pady=(15, 0))

            # Analyze data quality
            missing_data = {}
            for col in self.ad_spend_data.columns:
                missing_count = self.ad_spend_data[col].isna().sum()
                if missing_count > 0:
                    missing_data[col] = missing_count

            # Display data quality info
            quality_info_frame = ttk_bootstrap.Frame(quality_frame)
            quality_info_frame.pack(fill=X)

            # Column count and data types
            ttk_bootstrap.Label(
                quality_info_frame,
                text=f"📊 Columns: {len(self.ad_spend_data.columns)} | Rows: {len(self.ad_spend_data):,}",
                font=('Arial', 10, 'bold')
            ).pack(side=LEFT)

            # Missing data indicator
            if missing_data:
                missing_text = f"⚠️ Missing data in {len(missing_data)} columns"
                ttk_bootstrap.Label(
                    quality_info_frame,
                    text=missing_text,
                    font=('Arial', 10),
                    bootstyle="warning"
                ).pack(side=RIGHT)
            else:
                ttk_bootstrap.Label(
                    quality_info_frame,
                    text="✅ No missing data detected",
                    font=('Arial', 10),
                    bootstyle="success"
                ).pack(side=RIGHT)

            # Table frame with scrollbars
            table_frame = ttk_bootstrap.Frame(main_frame)
            table_frame.pack(fill=BOTH, expand=True, pady=(15, 0))

            # Create treeview with scrollbars
            tree_frame = ttk_bootstrap.Frame(table_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            # Define columns based on actual data
            columns = list(self.ad_spend_data.columns)

            # Create treeview
            tree = ttk_bootstrap.Treeview(
                tree_frame,
                columns=columns,
                show='headings',
                height=20,
                bootstyle="primary"
            )

            # Configure column headings and widths
            column_widths = {
                'Date': 100,
                'Campaign ID': 100,
                'Campaign Name': 250,
                'Cost': 100,
                'Impressions': 100,
                'Clicks': 80,
                'Conversions': 100,
                'CTR': 80,
                'CPC': 80,
                'Conv. Rate': 100,
                'Cost per Conv.': 120
            }

            for col in columns:
                tree.heading(col, text=col, anchor='center')
                width = column_widths.get(col, 120)
                tree.column(col, width=width, anchor='center')

            # Add scrollbars
            v_scrollbar = ttk_bootstrap.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            h_scrollbar = ttk_bootstrap.Scrollbar(tree_frame, orient=HORIZONTAL, command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

            # Pack treeview and scrollbars
            tree.grid(row=0, column=0, sticky='nsew')
            v_scrollbar.grid(row=0, column=1, sticky='ns')
            h_scrollbar.grid(row=1, column=0, sticky='ew')

            tree_frame.grid_rowconfigure(0, weight=1)
            tree_frame.grid_columnconfigure(0, weight=1)

            # Populate the treeview with data
            for index, row in self.ad_spend_data.iterrows():
                values = []
                for col in columns:
                    value = row[col]
                    # Format numeric values
                    if col == 'Cost' or col == 'CPC' or col == 'Cost per Conv.':
                        values.append(f"${value:.2f}")
                    elif col == 'CTR' or col == 'Conv. Rate':
                        values.append(f"{value:.2%}")
                    elif col == 'Conversions':
                        values.append(f"{value:.2f}")
                    elif col in ['Clicks', 'Impressions', 'Campaign ID']:
                        values.append(f"{int(value):,}")
                    else:
                        values.append(str(value))

                tree.insert('', 'end', values=values)

            # Button frame
            button_frame = ttk_bootstrap.Frame(main_frame)
            button_frame.pack(fill=X, pady=(15, 0))

            # Export button
            ttk_bootstrap.Button(
                button_frame,
                text="📤 Export Data",
                command=self.export_data,
                bootstyle="success",
                width=15
            ).pack(side=LEFT, padx=5)

            # Refresh button
            ttk_bootstrap.Button(
                button_frame,
                text="🔄 Refresh",
                command=lambda: self.refresh_preview_data(tree),
                bootstyle="info",
                width=15
            ).pack(side=LEFT, padx=5)

            # Close button
            ttk_bootstrap.Button(
                button_frame,
                text="❌ Close",
                command=preview_window.destroy,
                bootstyle="secondary",
                width=15
            ).pack(side=RIGHT, padx=5)

            self.log(f"✅ Data preview opened - showing {total_records:,} records")

        except Exception as e:
            self.log(f"❌ Error opening data preview: {str(e)}")
            messagebox.showerror("Preview Error", f"Failed to open data preview: {str(e)}")

    def refresh_preview_data(self, tree):
        """Refresh the preview data in the treeview"""
        try:
            # Clear existing data
            for item in tree.get_children():
                tree.delete(item)

            # Repopulate with current data
            if hasattr(self, 'ad_spend_data') and self.ad_spend_data is not None:
                columns = list(self.ad_spend_data.columns)
                for index, row in self.ad_spend_data.iterrows():
                    values = []
                    for col in columns:
                        value = row[col]
                        # Format numeric values
                        if col == 'Cost' or col == 'CPC' or col == 'Cost per Conv.':
                            values.append(f"${value:.2f}")
                        elif col == 'CTR' or col == 'Conv. Rate':
                            values.append(f"{value:.2%}")
                        elif col == 'Conversions':
                            values.append(f"{value:.2f}")
                        elif col in ['Clicks', 'Impressions', 'Campaign ID']:
                            values.append(f"{int(value):,}")
                        else:
                            values.append(str(value))

                    tree.insert('', 'end', values=values)

                self.log("✅ Preview data refreshed")
            else:
                self.log("⚠️ No data available to refresh")

        except Exception as e:
            self.log(f"❌ Error refreshing preview: {str(e)}")
            messagebox.showerror("Refresh Error", f"Failed to refresh data: {str(e)}")

    def export_data(self):
        """Export data to CSV or Excel in client-specific folder"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None:
            messagebox.showerror("No Data", "Please extract data first")
            return

        # Check if client is selected
        current_client_name = self.client_var.get()
        if not current_client_name:
            messagebox.showwarning("No Client Selected", "Please select a client before exporting data.")
            return

        try:
            export_format = self.export_format_var.get().lower()

            # Create filename with date range and client name
            start_date = self.start_date_var.get().replace("-", "")
            end_date = self.end_date_var.get().replace("-", "")
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # Sanitize client name for filename
            safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)

            if export_format == "csv":
                filename = f"{safe_client_name}_google_ads_{start_date}_to_{end_date}_{timestamp}.csv"
                file_path = self.client_manager.get_client_file_path(current_client_name, filename, "exports")
                self.ad_spend_data.to_csv(file_path, index=False)
            elif export_format == "excel":
                filename = f"{safe_client_name}_google_ads_{start_date}_to_{end_date}_{timestamp}.xlsx"
                file_path = self.client_manager.get_client_file_path(current_client_name, filename, "exports")
                self.ad_spend_data.to_excel(file_path, index=False, sheet_name="Google Ads Data")
            else:
                filename = f"{safe_client_name}_google_ads_{start_date}_to_{end_date}_{timestamp}.csv"
                file_path = self.client_manager.get_client_file_path(current_client_name, filename, "exports")
                self.ad_spend_data.to_csv(file_path, index=False)

            # Log export details
            self.log(f"✅ Data exported for client '{current_client_name}'")
            self.log(f"📁 File location: {file_path}")
            self.log(f"📊 Records exported: {len(self.ad_spend_data):,}")

            # Show success message with file location
            success_message = f"Data exported successfully!\n\n"
            success_message += f"Client: {current_client_name}\n"
            success_message += f"File: {filename}\n"
            success_message += f"Location: {file_path}\n"
            success_message += f"Records: {len(self.ad_spend_data):,}"

            messagebox.showinfo("Export Success", success_message)

            # Update activity
            self.add_activity(f"Exported {len(self.ad_spend_data):,} records for {current_client_name}")

        except Exception as e:
            self.log(f"❌ Error exporting data: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export data: {str(e)}")

    def export_granular_conversion_data(self):
        """Export granular conversion data to Excel with enhanced formatting"""
        if not hasattr(self, 'granular_conversion_data') or self.granular_conversion_data is None:
            messagebox.showerror("No Data", "Please extract granular conversion data first")
            return

        # Check if client is selected
        current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
        if not current_client_name or current_client_name == "No client":
            messagebox.showerror("No Client", "Please select a client first")
            return

        try:
            # Generate filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)

            # Get date range from data
            start_date = self.granular_conversion_data['Date'].min()
            end_date = self.granular_conversion_data['Date'].max()

            filename = f"{safe_client_name}_granular_conversions_{start_date}_to_{end_date}_{timestamp}.xlsx"
            file_path = self.client_manager.get_client_file_path(current_client_name, filename, "exports")

            # Create Excel writer with multiple sheets
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Sheet 1: Raw granular data
                self.granular_conversion_data.to_excel(
                    writer,
                    sheet_name="Granular Conversion Data",
                    index=False
                )

                # Sheet 2: Conversion action summary
                conversion_summary = self.granular_conversion_data.groupby('Conversion Action Name').agg({
                    'Cost': 'sum',
                    'Clicks': 'sum',
                    'Impressions': 'sum',
                    'Conversions': 'sum',
                    'Conversion Value': 'sum',
                    'All Conversions': 'sum',
                    'All Conversion Value': 'sum',
                    'View Through Conversions': 'sum'
                }).round(2)

                # Calculate metrics for summary
                conversion_summary['CPA'] = (conversion_summary['Cost'] / conversion_summary['Conversions']).round(2)
                conversion_summary['Conversion Rate'] = (conversion_summary['Conversions'] / conversion_summary['Clicks'] * 100).round(2)
                conversion_summary['CTR'] = (conversion_summary['Clicks'] / conversion_summary['Impressions'] * 100).round(2)
                conversion_summary['CPC'] = (conversion_summary['Cost'] / conversion_summary['Clicks']).round(2)

                conversion_summary.to_excel(
                    writer,
                    sheet_name="Conversion Action Summary"
                )

                # Sheet 3: Campaign performance by conversion action
                campaign_conversion_summary = self.granular_conversion_data.groupby(['Campaign Name', 'Conversion Action Name']).agg({
                    'Cost': 'sum',
                    'Clicks': 'sum',
                    'Impressions': 'sum',
                    'Conversions': 'sum',
                    'Conversion Value': 'sum'
                }).round(2)

                campaign_conversion_summary.to_excel(
                    writer,
                    sheet_name="Campaign x Conversion Analysis"
                )

                # Sheet 4: Daily conversion trends
                daily_summary = self.granular_conversion_data.groupby(['Date', 'Conversion Action Name']).agg({
                    'Cost': 'sum',
                    'Conversions': 'sum',
                    'Conversion Value': 'sum'
                }).round(2)

                daily_summary.to_excel(
                    writer,
                    sheet_name="Daily Conversion Trends"
                )

                # Sheet 5: Conversion action metadata
                conversion_metadata = self.granular_conversion_data[
                    ['Conversion Action Name', 'Conversion Category', 'Conversion Type',
                     'Is Primary for Goal', 'Counting Type']
                ].drop_duplicates()

                conversion_metadata.to_excel(
                    writer,
                    sheet_name="Conversion Action Metadata",
                    index=False
                )

            # Calculate summary stats for success message
            total_records = len(self.granular_conversion_data)
            unique_conversion_actions = self.granular_conversion_data['Conversion Action Name'].nunique()
            total_cost = self.granular_conversion_data['Cost'].sum()
            total_conversions = self.granular_conversion_data['Conversions'].sum()
            total_conversion_value = self.granular_conversion_data['Conversion Value'].sum()

            self.log(f"✅ Granular conversion data exported successfully!")
            self.log(f"📁 File: {filename}")
            self.log(f"📊 {total_records:,} records across {unique_conversion_actions} conversion actions")
            self.log(f"💰 Total Cost: ${total_cost:,.2f}")
            self.log(f"🎯 Total Conversions: {total_conversions:.2f}")
            self.log(f"💎 Total Conversion Value: ${total_conversion_value:,.2f}")

            messagebox.showinfo("Export Complete",
                f"Granular conversion data exported successfully!\n\n"
                f"📁 File: {filename}\n"
                f"📊 {total_records:,} records\n"
                f"🎯 {unique_conversion_actions} conversion actions\n"
                f"💰 ${total_cost:,.2f} total cost\n"
                f"🎯 {total_conversions:.2f} conversions\n"
                f"💎 ${total_conversion_value:,.2f} conversion value\n\n"
                f"📋 5 sheets created:\n"
                f"• Raw granular data\n"
                f"• Conversion action summary\n"
                f"• Campaign analysis\n"
                f"• Daily trends\n"
                f"• Conversion metadata")

        except Exception as e:
            self.log(f"❌ Error exporting granular conversion data: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export granular conversion data: {str(e)}")

    def compare_aggregated_vs_granular_conversions(self):
        """Compare aggregated vs granular conversion data to show the difference"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None:
            messagebox.showerror("No Aggregated Data", "Please extract regular Google Ads data first")
            return

        if not hasattr(self, 'granular_conversion_data') or self.granular_conversion_data is None:
            messagebox.showerror("No Granular Data", "Please extract granular conversion data first")
            return

        try:
            # Calculate aggregated totals
            aggregated_conversions = self.ad_spend_data['Conversions'].sum()
            aggregated_cost = self.ad_spend_data['Cost'].sum()
            aggregated_cpa = aggregated_cost / aggregated_conversions if aggregated_conversions > 0 else 0

            # Calculate granular totals
            granular_conversions = self.granular_conversion_data['Conversions'].sum()
            granular_cost = self.granular_conversion_data['Cost'].sum()
            granular_cpa = granular_cost / granular_conversions if granular_conversions > 0 else 0

            # Get conversion action breakdown
            conversion_breakdown = self.granular_conversion_data.groupby('Conversion Action Name').agg({
                'Conversions': 'sum',
                'Conversion Value': 'sum',
                'Cost': 'sum'
            }).round(2)

            conversion_breakdown['CPA'] = (conversion_breakdown['Cost'] / conversion_breakdown['Conversions']).round(2)

            # Create comparison report
            comparison_report = f"""
🔍 AGGREGATED vs GRANULAR CONVERSION COMPARISON
{'=' * 60}

📊 AGGREGATED DATA (Standard Google Ads Report):
   Total Conversions: {aggregated_conversions:.2f}
   Total Cost: ${aggregated_cost:,.2f}
   Cost per Conversion (CPA): ${aggregated_cpa:.2f}

🎯 GRANULAR DATA (By Conversion Action):
   Total Conversions: {granular_conversions:.2f}
   Total Cost: ${granular_cost:,.2f}
   Cost per Conversion (CPA): ${granular_cpa:.2f}

📈 DIFFERENCE:
   Conversion Difference: {granular_conversions - aggregated_conversions:.2f}
   Cost Difference: ${granular_cost - aggregated_cost:,.2f}
   CPA Difference: ${granular_cpa - aggregated_cpa:.2f}

🎯 CONVERSION ACTION BREAKDOWN:
{'=' * 40}
"""

            for action, data in conversion_breakdown.iterrows():
                conversion_pct = (data['Conversions'] / granular_conversions * 100) if granular_conversions > 0 else 0
                comparison_report += f"""
• {action}:
   Conversions: {data['Conversions']:.2f} ({conversion_pct:.1f}%)
   Value: ${data['Conversion Value']:,.2f}
   CPA: ${data['CPA']:.2f}
"""

            comparison_report += f"""
💡 KEY INSIGHTS:
{'=' * 40}
• Granular data shows {len(conversion_breakdown)} different conversion actions
• This explains why aggregated CPA calculations can be misleading
• Each conversion action has different values and costs
• Use granular data for accurate attribution and optimization
"""

            # Display in a new window
            comparison_window = tk.Toplevel(self.master)
            comparison_window.title("Aggregated vs Granular Conversion Comparison")
            comparison_window.geometry("800x600")

            # Create scrolled text widget
            text_widget = scrolledtext.ScrolledText(
                comparison_window,
                wrap=tk.WORD,
                font=("Consolas", 10),
                padx=10,
                pady=10
            )
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Insert the comparison report
            text_widget.insert(tk.END, comparison_report)
            text_widget.config(state=tk.DISABLED)

            # Add copy button
            button_frame = ttk_bootstrap.Frame(comparison_window)
            button_frame.pack(fill=tk.X, padx=10, pady=5)

            def copy_to_clipboard():
                comparison_window.clipboard_clear()
                comparison_window.clipboard_append(comparison_report)
                messagebox.showinfo("Copied", "Comparison report copied to clipboard!")

            ttk_bootstrap.Button(
                button_frame,
                text="📋 Copy to Clipboard",
                command=copy_to_clipboard,
                bootstyle="info"
            ).pack(side=tk.RIGHT, padx=5)

            ttk_bootstrap.Button(
                button_frame,
                text="❌ Close",
                command=comparison_window.destroy,
                bootstyle="secondary"
            ).pack(side=tk.RIGHT, padx=5)

            self.log("✅ Conversion comparison analysis completed")

        except Exception as e:
            self.log(f"❌ Error comparing conversion data: {str(e)}")
            messagebox.showerror("Comparison Error", f"Failed to compare conversion data: {str(e)}")

    def bulk_export(self):
        """Bulk export for multiple clients"""
        self.log("Starting bulk export...")

        # Get all clients from config
        clients = self.config_manager.config.get("clients", [])
        if not clients:
            messagebox.showinfo("No Clients", "No clients configured for bulk export")
            return

        def bulk_export_thread():
            try:
                self.status_var.set("Running bulk export...")
                successful_exports = 0

                for client in clients:
                    try:
                        client_name = client.get("client_name", "Unknown")
                        customer_id = client.get("customer_id_formatted", "")

                        if not customer_id:
                            self.log(f"⚠️ Skipping {client_name} - no customer ID")
                            continue

                        self.log(f"📊 Exporting data for {client_name}...")

                        # Temporarily set customer ID
                        original_customer_id = self.customer_id_var.get()
                        self.customer_id_var.set(customer_id)

                        # Run extraction for this client
                        # (This would need to be implemented as a synchronous version)
                        self.log(f"✅ Export completed for {client_name}")
                        successful_exports += 1

                        # Restore original customer ID
                        self.customer_id_var.set(original_customer_id)

                    except Exception as e:
                        self.log(f"❌ Error exporting {client_name}: {str(e)}")

                self.log(f"🎉 Bulk export completed. {successful_exports}/{len(clients)} clients exported successfully.")
                messagebox.showinfo("Bulk Export Complete", f"Exported data for {successful_exports} out of {len(clients)} clients")

            except Exception as e:
                self.log(f"❌ Bulk export error: {str(e)}")
                messagebox.showerror("Bulk Export Error", f"Bulk export failed: {str(e)}")
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=bulk_export_thread, daemon=True).start()

    def test_airtable_connection(self):
        """Test Airtable API connection using the same configuration as sync"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        # Use EXACTLY what the user entered in the UI (no client-specific logic)
        base_id = self.google_ads_base_id_var.get().strip()
        table_id = self.google_ads_table_id_var.get().strip()

        # Create temporary manager with EXACT UI settings
        temp_manager = AirtableManager(api_key)
        temp_manager.base_id = base_id
        temp_manager.google_ads_table_id = table_id

        self.google_ads_log(f"🔗 Testing connection with EXACT UI credentials:")
        self.google_ads_log(f"   - Base ID: {base_id}")
        self.google_ads_log(f"   - Table ID: {table_id}")
        self.google_ads_log(f"   - API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")

        success, message = temp_manager.test_connection()

        if success:
            self.log("✅ Airtable connection successful!")
            self.google_ads_log(f"✅ Connection test successful: {message}")
            messagebox.showinfo("Connection Success", f"Successfully connected to Airtable!\n\nBase: {base_id}\nTable: {table_id}")
        else:
            self.log(f"❌ Airtable connection failed: {message}")
            self.google_ads_log(f"❌ Connection test failed: {message}")
            self.google_ads_log(f"❌ Please verify:")
            self.google_ads_log(f"   1. API key has permissions for this base")
            self.google_ads_log(f"   2. Base ID exists: {base_id}")
            self.google_ads_log(f"   3. Table ID exists in base: {table_id}")
            messagebox.showerror("Connection Failed", f"Failed to connect to Airtable:\n{message}\n\nBase: {base_id}\nTable: {table_id}\n\nPlease check if this table exists in your Airtable base.")

    def sync_to_airtable(self):
        """Sync current Google Ads data to Airtable"""
        if not hasattr(self, 'ad_spend_data') or self.ad_spend_data is None or self.ad_spend_data.empty:
            messagebox.showerror("No Data", "Please extract Google Ads data first before syncing to Airtable")
            return

        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def sync_thread():
            try:
                self.airtable_log("🔄 Starting Airtable sync...")
                self.status_var.set("Syncing to Airtable...")

                # Set API key
                self.airtable_manager.set_api_key(api_key)

                # Test connection first
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Sync Failed", f"Connection failed: {message}")
                    return

                # Get sync mode
                sync_mode = self.airtable_sync_mode_var.get() if hasattr(self, 'airtable_sync_mode_var') else "append"

                # Clear existing data if in replace mode
                if sync_mode == "replace":
                    self.airtable_log("🗑️ Clearing existing Airtable data...")
                    existing_records = self.airtable_manager.get_existing_records(get_all=True)
                    if existing_records:
                        record_ids = [record['id'] for record in existing_records]
                        if self.airtable_manager.delete_records(record_ids):
                            self.airtable_log(f"✅ Cleared {len(record_ids)} existing records")
                        else:
                            self.airtable_log("⚠️ Failed to clear some existing records")

                # Upload data with selected mode
                if sync_mode == "incremental":
                    self.airtable_log(f"🔍 Checking for new records in {len(self.ad_spend_data)} extracted records...")
                    latest_date = self.airtable_manager.get_latest_date_in_airtable()
                    if latest_date:
                        self.airtable_log(f"📅 Latest date in Airtable: {latest_date}")
                else:
                    self.airtable_log(f"📤 Uploading {len(self.ad_spend_data)} records in {sync_mode} mode...")

                uploaded_count, errors, skipped_count = self.airtable_manager.upload_google_ads_data(
                    self.ad_spend_data,
                    mode=sync_mode
                )

                # Report results
                if sync_mode == "incremental" and skipped_count > 0:
                    self.airtable_log(f"⏭️ Skipped {skipped_count} existing records")

                if errors:
                    self.airtable_log(f"⚠️ Upload completed with errors:")
                    for error in errors:
                        self.airtable_log(f"   {error}")
                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    if sync_mode == "incremental":
                        result_msg += f" (skipped {skipped_count} existing)"
                else:
                    self.airtable_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    if sync_mode == "incremental" and skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"

                self.log(f"✅ Airtable sync completed: {result_msg}")
                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"Sync failed: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                self.log(f"❌ Airtable sync error: {str(e)}")
                messagebox.showerror("Sync Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=sync_thread, daemon=True).start()

    def view_airtable_data(self):
        """Open Airtable in browser to view data"""
        import webbrowser
        airtable_url = f"https://airtable.com/{AIRTABLE_BASE_ID}/{AIRTABLE_GOOGLE_ADS_TABLE_ID}"
        webbrowser.open(airtable_url)
        self.airtable_log("🌐 Opened Airtable in browser")

    def clear_airtable_data(self):
        """Clear all data from Airtable Google Ads table"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        # Confirm action
        if not messagebox.askyesno("Confirm Clear",
                                  "This will delete ALL Google Ads data from your Airtable table.\n\n"
                                  "This action cannot be undone. Are you sure?"):
            return

        def clear_thread():
            try:
                self.airtable_log("🗑️ Clearing all Airtable data...")
                self.status_var.set("Clearing Airtable data...")

                # Set API key and test connection
                self.airtable_manager.set_api_key(api_key)
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Clear Failed", f"Connection failed: {message}")
                    return

                # Get all records
                existing_records = self.airtable_manager.get_existing_records()
                if not existing_records:
                    self.airtable_log("ℹ️ No records found to clear")
                    messagebox.showinfo("Clear Complete", "No records found to clear")
                    return

                # Delete all records
                record_ids = [record['id'] for record in existing_records]
                if self.airtable_manager.delete_records(record_ids):
                    self.airtable_log(f"✅ Successfully cleared {len(record_ids)} records")
                    self.log(f"✅ Cleared {len(record_ids)} records from Airtable")
                    messagebox.showinfo("Clear Complete", f"Successfully cleared {len(record_ids)} records from Airtable")
                else:
                    self.airtable_log("❌ Failed to clear records")
                    messagebox.showerror("Clear Failed", "Failed to clear records from Airtable")

            except Exception as e:
                error_msg = f"Clear failed: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                self.log(f"❌ Airtable clear error: {str(e)}")
                messagebox.showerror("Clear Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=clear_thread, daemon=True).start()

    def show_airtable_stats(self):
        """Show Airtable statistics"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def stats_thread():
            try:
                self.airtable_log("📊 Fetching Airtable statistics...")
                self.status_var.set("Fetching statistics...")

                # Set API key and test connection
                self.airtable_manager.set_api_key(api_key)
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.airtable_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Stats Failed", f"Connection failed: {message}")
                    return

                # Get all records
                all_records = self.airtable_manager.get_existing_records(get_all=True)
                total_records = len(all_records)

                if total_records == 0:
                    self.airtable_log("ℹ️ No records found in Airtable")
                    messagebox.showinfo("Statistics", "No records found in Airtable")
                    return

                # Calculate statistics
                latest_date = self.airtable_manager.get_latest_date_in_airtable()

                # Count unique campaigns
                campaigns = set()
                total_cost = 0
                total_clicks = 0
                total_impressions = 0

                for record in all_records:
                    fields = record.get('fields', {})
                    campaign_name = fields.get('Campaign Name')
                    if campaign_name:
                        campaigns.add(campaign_name)

                    cost = fields.get('Cost', 0)
                    clicks = fields.get('Clicks', 0)
                    impressions = fields.get('Impressions', 0)

                    if isinstance(cost, (int, float)):
                        total_cost += cost
                    if isinstance(clicks, (int, float)):
                        total_clicks += clicks
                    if isinstance(impressions, (int, float)):
                        total_impressions += impressions

                # Format statistics
                stats_message = f"""📊 Airtable Statistics:

📈 Records: {total_records:,}
🎯 Unique Campaigns: {len(campaigns):,}
📅 Latest Date: {latest_date or 'N/A'}

💰 Total Cost: ${total_cost:,.2f}
👆 Total Clicks: {total_clicks:,}
👁️ Total Impressions: {total_impressions:,}

📊 Average CTR: {(total_clicks/total_impressions*100):.2f}% (if impressions > 0)
💵 Average CPC: ${(total_cost/total_clicks):.2f} (if clicks > 0)"""

                self.airtable_log("✅ Statistics retrieved successfully")
                messagebox.showinfo("Airtable Statistics", stats_message)

            except Exception as e:
                error_msg = f"Failed to get statistics: {str(e)}"
                self.airtable_log(f"❌ {error_msg}")
                messagebox.showerror("Statistics Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=stats_thread, daemon=True).start()

    def airtable_log(self, message):
        """Add message to Airtable status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'google_ads_status_text'):
            self.google_ads_status_text.insert(tk.END, formatted_message + "\n")
            self.google_ads_status_text.see(tk.END)

    def sync_google_ads_to_airtable(self):
        """Sync Google Ads data to Airtable"""
        if not self.has_valid_google_ads_data():
            messagebox.showerror("No Data", "Please extract Google Ads data first before syncing to Airtable")
            return

        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def sync_thread():
            try:
                # Get data info and client context for enhanced logging
                data_info = self.get_google_ads_data_info()
                current_client_name = self.client_var.get() if hasattr(self, 'client_var') else "No client"

                self.google_ads_log("🔄 Starting Google Ads sync to Airtable...")
                self.google_ads_log(f"👤 Client context: {current_client_name}")
                self.google_ads_log(f"📊 Data: {data_info['record_count']:,} records from {data_info['source']}")
                self.status_var.set("Syncing to Airtable...")

                # Create manager with EXACT settings from UI (no client-specific logic)
                temp_manager = AirtableManager(api_key)
                base_id = self.google_ads_base_id_var.get().strip()
                table_id = self.google_ads_table_id_var.get().strip()

                # Use EXACTLY what the user entered in the UI
                temp_manager.base_id = base_id
                temp_manager.google_ads_table_id = table_id

                self.google_ads_log(f"🔧 Using EXACT UI credentials:")
                self.google_ads_log(f"   - Base ID: {base_id}")
                self.google_ads_log(f"   - Table ID: {table_id}")
                self.google_ads_log(f"   - API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")

                # Enhanced connection debugging
                self.google_ads_log(f"🔗 Connection details:")
                self.google_ads_log(f"   - API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
                self.google_ads_log(f"   - Base ID: {base_id}")
                self.google_ads_log(f"   - Table ID: {table_id}")
                self.google_ads_log(f"   - URL: {AIRTABLE_API_URL}/{base_id}/{table_id}")

                # Test connection first
                success, message = temp_manager.test_connection()
                if not success:
                    self.google_ads_log(f"❌ Connection failed: {message}")
                    self.google_ads_log(f"❌ Please verify:")
                    self.google_ads_log(f"   1. API key is valid and has permissions")
                    self.google_ads_log(f"   2. Base ID exists and is accessible")
                    self.google_ads_log(f"   3. Table ID exists in the base")
                    messagebox.showerror("Sync Failed", f"Connection failed: {message}\n\nPlease check:\n1. API key permissions\n2. Base ID: {base_id}\n3. Table ID: {table_id}")
                    return

                # Get sync mode
                sync_mode = self.google_ads_sync_mode_var.get() if hasattr(self, 'google_ads_sync_mode_var') else "incremental"

                # Check if table is empty and switch to force upload mode
                existing_records = temp_manager.get_existing_records_for_table(temp_manager.google_ads_table_id, get_all=True)
                if not existing_records and sync_mode == "incremental":
                    self.google_ads_log("📋 Table is empty - switching to force upload mode")
                    sync_mode = "force_upload"

                # Clear existing data if in replace mode
                if sync_mode == "replace":
                    self.google_ads_log("🗑️ Clearing existing Airtable data...")
                    existing_records = temp_manager.get_existing_records_for_table(temp_manager.google_ads_table_id, get_all=True)
                    if existing_records:
                        record_ids = [record['id'] for record in existing_records]
                        if temp_manager.delete_records(record_ids):
                            self.google_ads_log(f"✅ Cleared {len(record_ids)} existing records")
                        else:
                            self.google_ads_log("⚠️ Failed to clear some existing records")

                # Get existing data summary for better analysis
                if sync_mode == "incremental":
                    self.google_ads_log(f"🔍 Analyzing data for incremental sync...")
                    existing_summary = temp_manager.get_existing_record_summary()

                    self.google_ads_log(f"📊 Airtable Status:")
                    self.google_ads_log(f"   - Existing records: {existing_summary['total_records']:,}")
                    if existing_summary['date_range']:
                        self.google_ads_log(f"   - Date range: {existing_summary['date_range'][0]} to {existing_summary['date_range'][1]}")
                    self.google_ads_log(f"   - Unique campaigns: {len(existing_summary['campaigns']):,}")

                    self.google_ads_log(f"📂 New data to process: {len(self.ad_spend_data):,} records")

                    # Analyze new data
                    new_dates = self.ad_spend_data['Date'].unique() if 'Date' in self.ad_spend_data.columns else []
                    new_campaigns = self.ad_spend_data['Campaign Name'].unique() if 'Campaign Name' in self.ad_spend_data.columns else []

                    if len(new_dates) > 0:
                        self.google_ads_log(f"   - Date range: {min(new_dates)} to {max(new_dates)}")
                    self.google_ads_log(f"   - Unique campaigns: {len(new_campaigns):,}")
                else:
                    self.google_ads_log(f"📤 Uploading {len(self.ad_spend_data)} records in {sync_mode} mode...")

                uploaded_count, errors, skipped_count = temp_manager.upload_google_ads_data(
                    self.ad_spend_data,
                    mode=sync_mode
                )

                # Report results
                if sync_mode == "incremental" and skipped_count > 0:
                    self.google_ads_log(f"⏭️ Skipped {skipped_count} existing records")

                if errors:
                    self.google_ads_log(f"⚠️ Upload completed with errors:")
                    for error in errors:
                        self.google_ads_log(f"   {error}")
                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    if sync_mode == "incremental":
                        result_msg += f" (skipped {skipped_count} existing)"
                else:
                    self.google_ads_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    if sync_mode == "incremental" and skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"

                self.log(f"✅ Google Ads sync completed: {result_msg}")
                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"Sync failed: {str(e)}"
                self.google_ads_log(f"❌ {error_msg}")
                self.log(f"❌ Google Ads sync error: {str(e)}")
                messagebox.showerror("Sync Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=sync_thread, daemon=True).start()

    def view_google_ads_airtable(self):
        """Open Google Ads table in Airtable"""
        import webbrowser
        base_id = self.google_ads_base_id_var.get().strip()
        table_id = self.google_ads_table_id_var.get().strip()
        airtable_url = f"https://airtable.com/{base_id}/{table_id}"
        webbrowser.open(airtable_url)
        self.google_ads_log("🌐 Opened Google Ads table in browser")

    def clear_google_ads_airtable(self):
        """Clear all data from Google Ads Airtable table"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        # Confirm action
        if not messagebox.askyesno("Confirm Clear",
                                  "This will delete ALL Google Ads data from your Airtable table.\n\n"
                                  "This action cannot be undone. Are you sure?"):
            return

        def clear_thread():
            try:
                self.google_ads_log("🗑️ Clearing all Google Ads Airtable data...")
                self.status_var.set("Clearing Google Ads Airtable data...")

                # Create manager with Google Ads settings
                temp_manager = AirtableManager(api_key)
                temp_manager.base_id = self.google_ads_base_id_var.get().strip()
                temp_manager.google_ads_table_id = self.google_ads_table_id_var.get().strip()

                # Test connection
                success, message = temp_manager.test_connection()
                if not success:
                    self.google_ads_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Clear Failed", f"Connection failed: {message}")
                    return

                # Get all records
                existing_records = temp_manager.get_existing_records_for_table(temp_manager.google_ads_table_id, get_all=True)
                if not existing_records:
                    self.google_ads_log("ℹ️ No records found to clear")
                    messagebox.showinfo("Clear Complete", "No records found to clear")
                    return

                # Delete all records
                record_ids = [record['id'] for record in existing_records]
                if temp_manager.delete_records(record_ids):
                    self.google_ads_log(f"✅ Successfully cleared {len(record_ids)} records")
                    self.log(f"✅ Cleared {len(record_ids)} records from Google Ads Airtable")
                    messagebox.showinfo("Clear Complete", f"Successfully cleared {len(record_ids)} records from Google Ads Airtable")
                else:
                    self.google_ads_log("❌ Failed to clear records")
                    messagebox.showerror("Clear Failed", "Failed to clear records from Google Ads Airtable")

            except Exception as e:
                error_msg = f"Clear failed: {str(e)}"
                self.google_ads_log(f"❌ {error_msg}")
                self.log(f"❌ Google Ads Airtable clear error: {str(e)}")
                messagebox.showerror("Clear Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=clear_thread, daemon=True).start()

    def show_google_ads_stats(self):
        """Show Google Ads Airtable statistics"""
        api_key = self.airtable_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def stats_thread():
            try:
                self.google_ads_log("📊 Fetching Google Ads Airtable statistics...")
                self.status_var.set("Fetching statistics...")

                # Create manager with Google Ads settings
                temp_manager = AirtableManager(api_key)
                temp_manager.base_id = self.google_ads_base_id_var.get().strip()
                temp_manager.google_ads_table_id = self.google_ads_table_id_var.get().strip()

                # Test connection
                success, message = temp_manager.test_connection()
                if not success:
                    self.google_ads_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Stats Failed", f"Connection failed: {message}")
                    return

                # Get all records
                all_records = temp_manager.get_existing_records_for_table(temp_manager.google_ads_table_id, get_all=True)
                total_records = len(all_records)

                if total_records == 0:
                    self.google_ads_log("ℹ️ No records found in Google Ads Airtable")
                    messagebox.showinfo("Statistics", "No records found in Google Ads Airtable")
                    return

                # Calculate statistics
                campaigns = set()
                total_cost = 0
                total_clicks = 0
                total_impressions = 0

                for record in all_records:
                    fields = record.get('fields', {})
                    campaign_name = fields.get('Campaign Name')
                    if campaign_name:
                        campaigns.add(campaign_name)

                    cost = fields.get('Cost', 0)
                    clicks = fields.get('Clicks', 0)
                    impressions = fields.get('Impressions', 0)

                    if isinstance(cost, (int, float)):
                        total_cost += cost
                    if isinstance(clicks, (int, float)):
                        total_clicks += clicks
                    if isinstance(impressions, (int, float)):
                        total_impressions += impressions

                # Format statistics
                stats_message = f"""📊 Google Ads Airtable Statistics:

📈 Records: {total_records:,}
🎯 Unique Campaigns: {len(campaigns):,}

💰 Total Cost: ${total_cost:,.2f}
👆 Total Clicks: {total_clicks:,}
👁️ Total Impressions: {total_impressions:,}

📊 Average CTR: {(total_clicks/total_impressions*100):.2f}% (if impressions > 0)
💵 Average CPC: ${(total_cost/total_clicks):.2f} (if clicks > 0)"""

                self.google_ads_log("✅ Statistics retrieved successfully")
                messagebox.showinfo("Google Ads Airtable Statistics", stats_message)

            except Exception as e:
                error_msg = f"Failed to get statistics: {str(e)}"
                self.google_ads_log(f"❌ {error_msg}")
                messagebox.showerror("Statistics Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=stats_thread, daemon=True).start()

    def google_ads_log(self, message):
        """Add message to Google Ads status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'google_ads_status_text'):
            self.google_ads_status_text.insert(tk.END, formatted_message + "\n")
            self.google_ads_status_text.see(tk.END)

    def test_ghl_connection(self):
        """Test GHL Airtable connection"""
        api_key = self.ghl_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        # Create temporary manager with GHL settings
        temp_manager = AirtableManager(api_key)
        temp_manager.base_id = self.ghl_base_id_var.get().strip()
        temp_manager.ghl_table_id = self.ghl_table_id_var.get().strip()

        success, message = temp_manager.test_connection()

        if success:
            self.log("✅ GHL Airtable connection successful!")
            self.ghl_log(f"✅ Connection test successful: {message}")
            messagebox.showinfo("Connection Success", "Successfully connected to Airtable!")
        else:
            self.log(f"❌ GHL Airtable connection failed: {message}")
            self.ghl_log(f"❌ Connection test failed: {message}")
            messagebox.showerror("Connection Failed", f"Failed to connect to Airtable:\n{message}")

    def browse_ghl_file(self):
        """Browse for GHL data file"""
        file_path = filedialog.askopenfilename(
            title="Select GHL Data File",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.ghl_file_var.set(file_path)
            self.ghl_log(f"📁 Selected file: {file_path}")



    def load_and_preview_ghl_data(self):
        """Load and preview GHL data from selected file"""
        file_path = self.ghl_file_var.get().strip()
        if not file_path:
            messagebox.showerror("No File Selected", "Please select a GHL data file first")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("File Not Found", f"File not found: {file_path}")
            return

        try:
            self.ghl_log(f"📖 Loading data from: {file_path}")

            # Determine file type and load accordingly
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension in ['.xlsx', '.xls']:
                self.ghl_data = pd.read_excel(file_path)
                self.ghl_log(f"✅ Successfully loaded Excel file")
            elif file_extension == '.csv':
                # Try different encodings for CSV files
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                loaded_data = None

                for encoding in encodings:
                    try:
                        self.ghl_data = pd.read_csv(file_path, encoding=encoding)
                        self.ghl_log(f"✅ Successfully loaded CSV with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.ghl_log(f"❌ Error with {encoding} encoding: {str(e)}")
                        continue

                if self.ghl_data is None:
                    raise Exception("Could not load CSV file with any supported encoding")
            else:
                # Try to load as CSV anyway
                self.ghl_data = pd.read_csv(file_path)
                self.ghl_log(f"✅ Successfully loaded file as CSV")

            # Validate data
            if self.ghl_data.empty:
                raise Exception("The loaded file is empty")

            # Check if this is actually GHL data or Google Ads data
            ads_columns = ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks']

            # Check which type of data this is
            if any(col in self.ghl_data.columns for col in ads_columns):
                self.ghl_log("⚠️ Warning: This appears to be Google Ads data, not GHL lead data")
                messagebox.showwarning("Data Type Mismatch",
                                     "This appears to be Google Ads data, not GHL lead data.\n"
                                     "Please select a proper GHL file with lead/contact data.")
                return

            self.ghl_log(f"✅ Loaded {len(self.ghl_data)} records for preview")
            self.log(f"GHL data loaded: {len(self.ghl_data)} records")

            # Use unified data state update
            self.update_ghl_data_state(data_source="file", show_success_message=True)

        except Exception as e:
            error_msg = f"Failed to load GHL data: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)

    def preview_ghl_data(self):
        """Preview GHL data from selected CSV file"""
        file_path = self.ghl_file_var.get().strip()
        if not file_path:
            messagebox.showerror("No File Selected", "Please select a GHL CSV file first")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("File Not Found", f"File not found: {file_path}")
            return

        try:
            self.ghl_log(f"📖 Loading data from: {file_path}")

            # Read CSV file
            self.ghl_data = pd.read_csv(file_path)

            # Check if this is actually GHL data or Google Ads data
            ghl_columns = ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created']
            ads_columns = ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks']

            # Check which type of data this is
            if any(col in self.ghl_data.columns for col in ads_columns):
                self.ghl_log("⚠️ Warning: This appears to be Google Ads data, not GHL lead data")
                messagebox.showwarning("Data Type Mismatch",
                                     "This appears to be Google Ads data, not GHL lead data.\n"
                                     "Please select a proper GHL CSV file with lead/contact data.")
                return

            # Clear existing data in treeview
            for item in self.ghl_tree.get_children():
                self.ghl_tree.delete(item)

            # Show first 50 rows for preview
            preview_data = self.ghl_data.head(50)

            for _, row in preview_data.iterrows():
                values = []
                for col in ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created']:
                    value = row.get(col, '')
                    if pd.isna(value):
                        value = ''
                    values.append(str(value))

                self.ghl_tree.insert('', 'end', values=values)

            self.ghl_log(f"✅ Loaded {len(self.ghl_data)} records for preview (showing first 50)")
            self.log(f"GHL data loaded: {len(self.ghl_data)} records")

        except Exception as e:
            error_msg = f"Failed to load GHL data: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)

    def sync_ghl_to_airtable(self):
        """Sync GHL data to Airtable"""
        if self.ghl_data is None or self.ghl_data.empty:
            messagebox.showerror("No Data", "Please load GHL data first")
            return

        api_key = self.ghl_api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("Missing API Key", "Please configure your Airtable API key")
            return

        def sync_thread():
            try:
                self.ghl_log("🔄 Starting GHL data sync to Airtable...")
                self.status_var.set("Syncing GHL data...")
                self.set_activity_indicator("working", "Syncing GHL data")
                self.add_activity("GHL sync started")

                # Create manager with GHL settings
                temp_manager = AirtableManager(api_key)
                temp_manager.base_id = self.ghl_base_id_var.get().strip()
                temp_manager.ghl_table_id = self.ghl_table_id_var.get().strip()

                # Test connection first
                success, message = temp_manager.test_connection()
                if not success:
                    self.ghl_log(f"❌ Connection failed: {message}")
                    messagebox.showerror("Sync Failed", f"Connection failed: {message}")
                    return

                # Get sync mode
                sync_mode = self.ghl_sync_mode_var.get() if hasattr(self, 'ghl_sync_mode_var') else "incremental"

                # Clear existing data if in replace mode
                if sync_mode == "replace":
                    self.ghl_log("🗑️ Clearing existing Airtable data...")
                    existing_records = temp_manager.get_existing_records_for_table(temp_manager.ghl_table_id, get_all=True)
                    if existing_records:
                        record_ids = [record['id'] for record in existing_records]
                        if temp_manager.delete_records(record_ids):
                            self.ghl_log(f"✅ Cleared {len(record_ids)} existing records")
                        else:
                            self.ghl_log("⚠️ Failed to clear some existing records")

                # Get existing data summary for better analysis
                if sync_mode == "incremental":
                    self.ghl_log(f"🔍 Analyzing GHL data for incremental sync...")
                    existing_summary = temp_manager.get_existing_ghl_summary()

                    self.ghl_log(f"📊 Airtable Status:")
                    self.ghl_log(f"   - Existing records: {existing_summary['total_records']:,}")
                    if existing_summary['date_range']:
                        self.ghl_log(f"   - Date range: {existing_summary['date_range'][0]} to {existing_summary['date_range'][1]}")
                    self.ghl_log(f"   - Locations: {len(existing_summary['locations']):,}")
                    self.ghl_log(f"   - Pipelines: {len(existing_summary['pipelines']):,}")

                    self.ghl_log(f"📂 New data to process: {len(self.ghl_data):,} records")

                    # Analyze new data
                    new_locations = self.ghl_data['Location'].unique() if 'Location' in self.ghl_data.columns else []
                    new_pipelines = self.ghl_data['pipeline'].unique() if 'pipeline' in self.ghl_data.columns else []

                    self.ghl_log(f"   - Locations: {len(new_locations):,}")
                    self.ghl_log(f"   - Pipelines: {len(new_pipelines):,}")
                else:
                    self.ghl_log(f"📤 Uploading {len(self.ghl_data)} records in {sync_mode} mode...")

                uploaded_count, errors, skipped_count = temp_manager.upload_ghl_data(
                    self.ghl_data,
                    mode=sync_mode
                )

                # Report results
                if sync_mode == "incremental" and skipped_count > 0:
                    self.ghl_log(f"⏭️ Skipped {skipped_count} existing records")

                if errors:
                    self.ghl_log(f"⚠️ Upload completed with errors:")
                    for error in errors:
                        self.ghl_log(f"   {error}")
                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    if sync_mode == "incremental":
                        result_msg += f" (skipped {skipped_count} existing)"
                else:
                    self.ghl_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    if sync_mode == "incremental" and skipped_count > 0:
                        result_msg += f" (skipped {skipped_count} existing)"

                self.log(f"✅ GHL sync completed: {result_msg}")
                self.set_activity_indicator("success", "GHL sync complete")
                self.add_activity(f"GHL sync: {uploaded_count} records uploaded")
                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"GHL sync failed: {str(e)}"
                self.ghl_log(f"❌ {error_msg}")
                self.log(f"❌ GHL sync error: {str(e)}")
                self.set_activity_indicator("error", "GHL sync failed")
                self.add_activity(f"GHL sync failed: {str(e)}")
                messagebox.showerror("Sync Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=sync_thread, daemon=True).start()

    def update_ghl_client_context(self):
        """Update the client context display in GHL tab"""
        current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None

        if hasattr(self, 'ghl_current_client_label'):
            if current_client_name:
                self.ghl_current_client_label.config(
                    text=current_client_name,
                    bootstyle="success"
                )

                # Update folder info
                if hasattr(self, 'client_manager'):
                    client_folder = self.client_manager.get_client_folder(current_client_name)
                    folder_info = f"📁 Client folder: {client_folder}"

                    # Check for existing GHL files
                    ghl_files = self.client_manager.list_client_files(current_client_name, "data")
                    if ghl_files:
                        folder_info += f" | 📄 {len(ghl_files)} data files"

                    if hasattr(self, 'ghl_client_folder_label'):
                        self.ghl_client_folder_label.config(text=folder_info)

                    # Update table ID to be client-specific
                    if hasattr(self, 'ghl_table_id_var'):
                        safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)
                        client_table_id = f"tbl{safe_client_name}GHL"[:17]  # Airtable table ID length limit
                        self.ghl_table_id_var.set(client_table_id)

            else:
                self.ghl_current_client_label.config(
                    text="No client selected",
                    bootstyle="warning"
                )
                if hasattr(self, 'ghl_client_folder_label'):
                    self.ghl_client_folder_label.config(text="Select a client to see folder information")

    def authenticate_ghl_sheets(self):
        """Authenticate Google Sheets for GHL data import"""
        try:
            if self.sheets_manager.authenticate():
                self.ghl_sheets_auth_status.config(
                    text="✅ Authenticated",
                    bootstyle="success"
                )
                self.ghl_log("✅ Google Sheets authentication successful")

                # Refresh spreadsheets list
                self.refresh_ghl_spreadsheets()
            else:
                self.ghl_sheets_auth_status.config(
                    text="❌ Authentication failed",
                    bootstyle="danger"
                )
                self.ghl_log("❌ Google Sheets authentication failed")

        except Exception as e:
            self.ghl_log(f"❌ Authentication error: {str(e)}")
            messagebox.showerror("Authentication Error", f"Failed to authenticate: {str(e)}")

    def refresh_ghl_spreadsheets(self):
        """Refresh the list of available spreadsheets for GHL import"""
        try:
            if not self.sheets_manager.service:
                self.ghl_log("⚠️ Please authenticate first")
                return

            self.ghl_log("🔄 Refreshing spreadsheets list...")

            # Get spreadsheets
            spreadsheets = self.sheets_manager.get_spreadsheets()

            # Update combobox
            spreadsheet_names = []
            self.ghl_spreadsheet_data = {}  # Store spreadsheet data

            if spreadsheets:
                for sheet in spreadsheets:
                    name = sheet.get('name', 'Unknown')
                    sheet_id = sheet.get('id', '')
                    spreadsheet_names.append(name)
                    self.ghl_spreadsheet_data[name] = sheet_id

                self.ghl_log(f"✅ Found {len(spreadsheets)} spreadsheets")
            else:
                # Check for saved spreadsheets
                saved_sheets = self.config_manager.config.get("saved_sheets", [])
                for saved_sheet in saved_sheets:
                    name = saved_sheet.get('name', 'Unknown')
                    sheet_id = saved_sheet.get('id', '')
                    spreadsheet_names.append(f"{name} (Saved)")
                    self.ghl_spreadsheet_data[f"{name} (Saved)"] = sheet_id

                if saved_sheets:
                    self.ghl_log(f"✅ Loaded {len(saved_sheets)} saved spreadsheets")
                else:
                    self.ghl_log("⚠️ No spreadsheets found. Use 'Manual' to add one.")

            if hasattr(self, 'ghl_spreadsheet_combo'):
                self.ghl_spreadsheet_combo['values'] = spreadsheet_names
                if spreadsheet_names:
                    self.ghl_spreadsheet_combo.set(spreadsheet_names[0])

        except Exception as e:
            self.ghl_log(f"❌ Error refreshing spreadsheets: {str(e)}")

    def add_manual_ghl_spreadsheet(self):
        """Add a spreadsheet manually for GHL import"""
        # Reuse the existing manual spreadsheet dialog
        self.add_manual_spreadsheet()
        # After adding, refresh the GHL spreadsheets list
        self.refresh_ghl_spreadsheets()

    def on_ghl_spreadsheet_select(self, event=None):
        """Handle spreadsheet selection for GHL import"""
        try:
            selected_name = self.ghl_spreadsheet_var.get()
            if not selected_name or not hasattr(self, 'ghl_spreadsheet_data'):
                return

            spreadsheet_id = self.ghl_spreadsheet_data.get(selected_name)
            if not spreadsheet_id:
                return

            self.ghl_log(f"📊 Loading worksheets for: {selected_name}")

            # Get worksheets
            worksheets = self.sheets_manager.get_worksheets(spreadsheet_id)

            if worksheets:
                # Use the correct data structure - worksheets now returns properly formatted data
                worksheet_names = [ws.get('title', 'Unknown') for ws in worksheets]
                if hasattr(self, 'ghl_worksheet_combo'):
                    self.ghl_worksheet_combo['values'] = worksheet_names
                    if worksheet_names:
                        self.ghl_worksheet_combo.set(worksheet_names[0])

                self.ghl_log(f"✅ Found {len(worksheets)} worksheets: {', '.join(worksheet_names)}")

                # Store worksheet data for preview functionality
                self.ghl_worksheet_data = {ws.get('title', 'Unknown'): ws for ws in worksheets}
            else:
                self.ghl_log("⚠️ No worksheets found")
                self.ghl_worksheet_data = {}

        except Exception as e:
            self.ghl_log(f"❌ Error loading worksheets: {str(e)}")

    def on_ghl_worksheet_select(self, event=None):
        """Handle worksheet selection for GHL import"""
        try:
            selected_worksheet = self.ghl_worksheet_var.get()
            if not selected_worksheet or not hasattr(self, 'ghl_worksheet_data'):
                return

            worksheet_data = self.ghl_worksheet_data.get(selected_worksheet)
            if not worksheet_data:
                return

            # Update worksheet info display
            row_count = worksheet_data.get('row_count', 0)
            column_count = worksheet_data.get('column_count', 0)

            info_text = f"📊 {selected_worksheet} | {row_count:,} rows × {column_count} columns"
            if hasattr(self, 'ghl_worksheet_info'):
                self.ghl_worksheet_info.config(text=info_text)

            self.ghl_log(f"📋 Selected worksheet: {selected_worksheet} ({row_count:,} rows)")

        except Exception as e:
            self.ghl_log(f"❌ Error selecting worksheet: {str(e)}")

    def preview_ghl_worksheet(self):
        """Preview data from selected worksheet in GHL Data Hub"""
        try:
            selected_spreadsheet = self.ghl_spreadsheet_var.get()
            selected_worksheet = self.ghl_worksheet_var.get()

            if not selected_spreadsheet or not selected_worksheet:
                messagebox.showerror("Selection Required", "Please select both a spreadsheet and worksheet.")
                return

            # Get spreadsheet ID
            spreadsheet_id = self.ghl_spreadsheet_data.get(selected_spreadsheet)
            if not spreadsheet_id:
                messagebox.showerror("Invalid Selection", "Could not find spreadsheet ID.")
                return

            self.ghl_log(f"👁️ Previewing worksheet: {selected_worksheet}")

            # Read first 10 rows for preview
            range_name = f"{selected_worksheet}!A1:Z10"
            data = self.sheets_manager.read_sheet_data(spreadsheet_id, range_name)

            if data:
                # Create preview dialog
                self.show_ghl_worksheet_preview(selected_worksheet, data)
            else:
                self.ghl_log("⚠️ No data found in worksheet")
                messagebox.showwarning("No Data", "No data found in the selected worksheet.")

        except Exception as e:
            error_msg = f"Preview failed: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Preview Error", error_msg)

    def show_ghl_worksheet_preview(self, worksheet_name, data):
        """Show worksheet preview dialog"""
        dialog = tk.Toplevel(self.master)
        dialog.title(f"Worksheet Preview: {worksheet_name}")
        dialog.geometry("800x600")
        dialog.resizable(True, True)
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"800x600+{x}+{y}")

        # Main frame
        main_frame = ttk_bootstrap.Frame(dialog, padding=15)
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk_bootstrap.Label(
            main_frame,
            text=f"📋 Preview: {worksheet_name}",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 15))

        # Info
        info_label = ttk_bootstrap.Label(
            main_frame,
            text=f"Showing first {len(data)} rows (preview only)",
            font=('Arial', 10),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 10))

        # Data display frame
        data_frame = ttk_bootstrap.Frame(main_frame)
        data_frame.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Create treeview for data display
        if data:
            headers = data[0] if data else []
            rows = data[1:] if len(data) > 1 else []

            # Create treeview
            tree = ttk.Treeview(data_frame, columns=headers, show='headings', height=15)

            # Configure columns
            for header in headers:
                tree.heading(header, text=header)
                tree.column(header, width=120, minwidth=80)

            # Add data rows
            for row in rows:
                # Pad row to match header count
                padded_row = row + [''] * (len(headers) - len(row))
                tree.insert('', 'end', values=padded_row[:len(headers)])

            # Scrollbars
            v_scrollbar = ttk.Scrollbar(data_frame, orient=VERTICAL, command=tree.yview)
            h_scrollbar = ttk.Scrollbar(data_frame, orient=HORIZONTAL, command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

            # Pack widgets
            tree.pack(side=LEFT, fill=BOTH, expand=True)
            v_scrollbar.pack(side=RIGHT, fill=Y)
            h_scrollbar.pack(side=BOTTOM, fill=X)

        # Button frame
        button_frame = ttk_bootstrap.Frame(main_frame)
        button_frame.pack(fill=X)

        # Close button
        ttk_bootstrap.Button(
            button_frame,
            text="✅ Close",
            command=dialog.destroy,
            bootstyle="primary",
            width=15
        ).pack(side=RIGHT)

    def view_ghl_airtable(self):
        """Open GHL table in Airtable"""
        import webbrowser
        base_id = self.ghl_base_id_var.get().strip()
        table_id = self.ghl_table_id_var.get().strip()
        airtable_url = f"https://airtable.com/{base_id}/{table_id}"
        webbrowser.open(airtable_url)
        self.ghl_log("🌐 Opened GHL table in browser")

    def import_ghl_from_sheets(self):
        """Import GHL data from Google Sheets"""
        try:
            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before importing GHL data.")
                return

            # Check authentication
            if not self.sheets_manager.service:
                messagebox.showerror("Not Authenticated", "Please authenticate with Google Sheets first.")
                return

            # Check spreadsheet selection
            selected_spreadsheet = self.ghl_spreadsheet_var.get()
            selected_worksheet = self.ghl_worksheet_var.get()

            if not selected_spreadsheet or not selected_worksheet:
                messagebox.showerror("Selection Required", "Please select both a spreadsheet and worksheet.")
                return

            # Get spreadsheet ID
            spreadsheet_id = self.ghl_spreadsheet_data.get(selected_spreadsheet)
            if not spreadsheet_id:
                messagebox.showerror("Invalid Selection", "Could not find spreadsheet ID.")
                return

            self.ghl_log(f"📊 Importing GHL data from: {selected_spreadsheet} -> {selected_worksheet}")
            self.ghl_log(f"👤 Client: {current_client_name}")

            # Import data from sheets
            data = self.sheets_manager.get_sheet_data(spreadsheet_id, selected_worksheet)

            if data and len(data) > 0:
                # Convert to DataFrame
                import pandas as pd

                # First row as headers
                headers = data[0] if data else []
                rows = data[1:] if len(data) > 1 else []

                if not headers:
                    messagebox.showerror("No Data", "No headers found in the worksheet.")
                    return

                # Create DataFrame
                self.ghl_sheets_data = pd.DataFrame(rows, columns=headers)

                # Clean up the data
                self.ghl_sheets_data = self.ghl_sheets_data.dropna(how='all')  # Remove empty rows

                self.ghl_log(f"✅ Imported {len(self.ghl_sheets_data)} records from Google Sheets")
                self.ghl_log(f"📋 Columns: {', '.join(self.ghl_sheets_data.columns.tolist())}")

                # Save to client folder
                if hasattr(self, 'client_manager'):
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)
                    filename = f"{safe_client_name}_ghl_sheets_import_{timestamp}.csv"

                    file_path = self.client_manager.get_client_file_path(current_client_name, filename, "data")
                    self.ghl_sheets_data.to_csv(file_path, index=False)

                    self.ghl_log(f"💾 Saved to client folder: {file_path}")

                # Set as current GHL data for processing
                self.ghl_data = self.ghl_sheets_data.copy()

                # Use unified data state update
                self.update_ghl_data_state(data_source="sheets", show_success_message=True)

            else:
                self.ghl_log("⚠️ No data found in the selected worksheet")
                messagebox.showwarning("No Data", "No data found in the selected worksheet.")

        except Exception as e:
            error_msg = f"Failed to import from Google Sheets: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Import Error", error_msg)

    def validate_ghl_data(self):
        """Validate GHL data against Airtable to ensure consistency"""
        try:
            # Check if we have data to validate using unified method
            if not self.has_valid_ghl_data():
                messagebox.showerror("No Data", "Please load GHL data first (from file or Google Sheets).")
                return

            # Get data info for logging
            data_info = self.get_ghl_data_info()
            self.ghl_log(f"🔍 Starting validation for {data_info['source']} data...")

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before validating data.")
                return

            # Check API configuration
            api_key = self.ghl_api_key_var.get().strip()
            if not api_key:
                messagebox.showerror("Missing API Key", "Please configure your Airtable API key.")
                return

            self.ghl_log("🔍 Starting data validation...")
            self.ghl_log(f"👤 Client: {current_client_name}")
            self.ghl_log(f"📊 Local data: {len(self.ghl_data)} records")

            # Create temporary manager for validation
            temp_manager = AirtableManager(api_key)
            temp_manager.base_id = self.ghl_base_id_var.get().strip()
            temp_manager.ghl_table_id = self.ghl_table_id_var.get().strip()

            # Test connection first
            success, message = temp_manager.test_connection()
            if not success:
                self.ghl_log(f"❌ Connection failed: {message}")
                messagebox.showerror("Validation Failed", f"Cannot connect to Airtable: {message}")
                return

            # Get existing records from Airtable
            self.ghl_log("📥 Fetching existing Airtable data...")
            existing_records = temp_manager.get_existing_records_for_table(temp_manager.ghl_table_id, get_all=True)

            if existing_records:
                self.ghl_log(f"📊 Airtable data: {len(existing_records)} records")

                # Convert Airtable data to DataFrame for comparison
                airtable_data = []
                for record in existing_records:
                    fields = record.get('fields', {})
                    airtable_data.append(fields)

                if airtable_data:
                    import pandas as pd
                    airtable_df = pd.DataFrame(airtable_data)

                    # Perform validation checks
                    validation_results = {
                        'local_count': len(self.ghl_data),
                        'airtable_count': len(airtable_df),
                        'count_match': len(self.ghl_data) == len(airtable_df),
                        'first_record_match': False,
                        'last_record_match': False,
                        'column_comparison': {},
                        'validation_passed': False
                    }

                    # Check first and last record matching (simple approach)
                    if len(self.ghl_data) > 0 and len(airtable_df) > 0:
                        # Compare common columns
                        common_columns = set(self.ghl_data.columns) & set(airtable_df.columns)

                        if common_columns:
                            # Check first record
                            local_first = self.ghl_data.iloc[0][list(common_columns)].to_dict()
                            airtable_first = airtable_df.iloc[0][list(common_columns)].to_dict()

                            # Simple string comparison (convert to strings for comparison)
                            first_match_count = 0
                            for col in common_columns:
                                if str(local_first.get(col, '')).strip() == str(airtable_first.get(col, '')).strip():
                                    first_match_count += 1

                            validation_results['first_record_match'] = first_match_count >= len(common_columns) * 0.8  # 80% match

                            # Check last record
                            local_last = self.ghl_data.iloc[-1][list(common_columns)].to_dict()
                            airtable_last = airtable_df.iloc[-1][list(common_columns)].to_dict()

                            last_match_count = 0
                            for col in common_columns:
                                if str(local_last.get(col, '')).strip() == str(airtable_last.get(col, '')).strip():
                                    last_match_count += 1

                            validation_results['last_record_match'] = last_match_count >= len(common_columns) * 0.8  # 80% match

                            validation_results['column_comparison'] = {
                                'common_columns': list(common_columns),
                                'local_only': list(set(self.ghl_data.columns) - set(airtable_df.columns)),
                                'airtable_only': list(set(airtable_df.columns) - set(self.ghl_data.columns))
                            }

                    # Overall validation result
                    validation_results['validation_passed'] = (
                        validation_results['count_match'] and
                        validation_results['first_record_match'] and
                        validation_results['last_record_match']
                    )

                    # Display validation results
                    self.display_validation_results(validation_results)

                else:
                    self.ghl_log("⚠️ No data found in Airtable records")
                    self.ghl_validation_status.config(
                        text="Airtable has no data to compare",
                        bootstyle="warning"
                    )
            else:
                self.ghl_log("⚠️ No existing records found in Airtable")
                self.ghl_validation_status.config(
                    text="Airtable table is empty - ready for initial sync",
                    bootstyle="info"
                )
                messagebox.showinfo(
                    "Validation Complete",
                    "Airtable table is empty. Your data is ready for initial synchronization."
                )

        except Exception as e:
            error_msg = f"Validation failed: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            self.ghl_validation_status.config(
                text="Validation failed",
                bootstyle="danger"
            )
            messagebox.showerror("Validation Error", error_msg)

    def display_validation_results(self, results):
        """Display validation results to user"""
        self.ghl_log("📋 VALIDATION RESULTS:")
        self.ghl_log("=" * 50)
        self.ghl_log(f"📊 Record Count Comparison:")
        self.ghl_log(f"   Local data: {results['local_count']:,} records")
        self.ghl_log(f"   Airtable data: {results['airtable_count']:,} records")
        self.ghl_log(f"   Count match: {'✅ Yes' if results['count_match'] else '❌ No'}")

        self.ghl_log(f"🔍 Record Content Validation:")
        self.ghl_log(f"   First record match: {'✅ Yes' if results['first_record_match'] else '❌ No'}")
        self.ghl_log(f"   Last record match: {'✅ Yes' if results['last_record_match'] else '❌ No'}")

        if 'column_comparison' in results:
            comp = results['column_comparison']
            self.ghl_log(f"📋 Column Analysis:")
            self.ghl_log(f"   Common columns: {len(comp.get('common_columns', []))}")
            if comp.get('local_only'):
                self.ghl_log(f"   Local-only columns: {', '.join(comp['local_only'])}")
            if comp.get('airtable_only'):
                self.ghl_log(f"   Airtable-only columns: {', '.join(comp['airtable_only'])}")

        overall_status = "✅ PASSED" if results['validation_passed'] else "❌ FAILED"
        self.ghl_log(f"🎯 Overall Validation: {overall_status}")

        # Update UI status
        if results['validation_passed']:
            self.ghl_validation_status.config(
                text="✅ Validation passed - data is synchronized",
                bootstyle="success"
            )
            messagebox.showinfo(
                "Validation Passed",
                f"✅ Data validation successful!\n\n"
                f"Local records: {results['local_count']:,}\n"
                f"Airtable records: {results['airtable_count']:,}\n"
                f"First/Last records match: ✅\n\n"
                f"Your data is properly synchronized."
            )
        else:
            self.ghl_validation_status.config(
                text="❌ Validation failed - data mismatch detected",
                bootstyle="danger"
            )

            issues = []
            if not results['count_match']:
                issues.append(f"Record count mismatch ({results['local_count']} vs {results['airtable_count']})")
            if not results['first_record_match']:
                issues.append("First record doesn't match")
            if not results['last_record_match']:
                issues.append("Last record doesn't match")

            messagebox.showwarning(
                "Validation Failed",
                f"❌ Data validation detected issues:\n\n" + "\n".join(f"• {issue}" for issue in issues) +
                f"\n\nRecommendation: Review your data and consider re-syncing."
            )

    def sync_ghl_to_client_airtable(self):
        """Sync GHL data to client-specific Airtable table"""
        try:
            # Check if we have valid data
            if not self.has_valid_ghl_data():
                messagebox.showerror("No Data", "Please load GHL data first (from file or Google Sheets).")
                return

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before syncing data.")
                return

            # Get data info for logging
            data_info = self.get_ghl_data_info()
            self.ghl_log(f"🔄 Starting client-specific sync for: {current_client_name}")
            self.ghl_log(f"📊 Data source: {data_info['source']} ({data_info['record_count']:,} records)")

            # Update table ID to be client-specific
            self.update_ghl_client_context()

            # Use the existing sync method but with client context
            self.sync_ghl_to_airtable()

        except Exception as e:
            error_msg = f"Client sync failed: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Sync Error", error_msg)

    def save_ghl_to_client_folder(self):
        """Save GHL data to client-specific folder"""
        try:
            # Check if we have valid data
            if not self.has_valid_ghl_data():
                messagebox.showerror("No Data", "Please load GHL data first (from file or Google Sheets).")
                return

            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before saving data.")
                return

            if not hasattr(self, 'client_manager'):
                messagebox.showerror("Error", "Client manager not available.")
                return

            # Generate filename
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)

            # Determine data source for filename
            data_source = "sheets" if hasattr(self, 'ghl_sheets_data') and self.ghl_sheets_data is not None else "file"
            filename = f"{safe_client_name}_ghl_{data_source}_processed_{timestamp}.csv"

            # Save to client folder
            file_path = self.client_manager.get_client_file_path(current_client_name, filename, "data")
            self.ghl_data.to_csv(file_path, index=False)

            self.ghl_log(f"💾 Saved GHL data to client folder:")
            self.ghl_log(f"   📁 Path: {file_path}")
            self.ghl_log(f"   📊 Records: {len(self.ghl_data):,}")
            self.ghl_log(f"   👤 Client: {current_client_name}")

            # Also save Excel version
            excel_filename = filename.replace('.csv', '.xlsx')
            excel_path = self.client_manager.get_client_file_path(current_client_name, excel_filename, "exports")
            self.ghl_data.to_excel(excel_path, index=False, sheet_name="GHL Data")

            self.ghl_log(f"📊 Also saved Excel version: {excel_path}")

            messagebox.showinfo(
                "Save Successful",
                f"GHL data saved successfully!\n\n"
                f"Client: {current_client_name}\n"
                f"Records: {len(self.ghl_data):,}\n"
                f"CSV: {file_path}\n"
                f"Excel: {excel_path}"
            )

        except Exception as e:
            error_msg = f"Save failed: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Save Error", error_msg)

    def view_client_ghl_airtable(self):
        """Open client-specific GHL table in Airtable"""
        try:
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showwarning("No Client Selected", "Please select a client to view their Airtable data.")
                return

            # Update to client-specific table ID
            self.update_ghl_client_context()

            import webbrowser
            base_id = self.ghl_base_id_var.get().strip()
            table_id = self.ghl_table_id_var.get().strip()
            airtable_url = f"https://airtable.com/{base_id}/{table_id}"
            webbrowser.open(airtable_url)

            self.ghl_log(f"🌐 Opened client-specific GHL table for: {current_client_name}")
            self.ghl_log(f"   📊 Table ID: {table_id}")

        except Exception as e:
            self.ghl_log(f"❌ Error opening Airtable: {str(e)}")
            messagebox.showerror("Error", f"Failed to open Airtable: {str(e)}")



    def find_ghl_duplicates(self):
        """Find duplicates in GHL data using precise criteria"""
        if self.ghl_data is None or self.ghl_data.empty:
            messagebox.showerror("No Data", "Please load GHL data first")
            return

        def find_duplicates_thread():
            try:
                self.ghl_log("🔍 Starting precise duplicate analysis...")
                self.set_activity_indicator("working", "Finding duplicates")
                self.add_activity("GHL duplicate analysis started")

                data = self.ghl_data.copy()
                total_records = len(data)

                self.ghl_log(f"📊 Analyzing {total_records:,} records for duplicates...")
                self.ghl_log(f"🎯 Using precise criteria: Contact ID + Opportunity ID + (Name OR Phone OR Email)")

                # Check if required columns exist
                required_columns = ['Contact ID', 'Opportunity ID', 'contact name', 'phone', 'email']
                missing_columns = [col for col in required_columns if col not in data.columns]
                if missing_columns:
                    self.ghl_log(f"❌ Missing required columns: {missing_columns}")
                    return

                # Normalize data for comparison
                self.ghl_log("🔧 Normalizing data for comparison...")
                data['contact_id_clean'] = data['Contact ID'].astype(str).str.strip()
                data['opportunity_id_clean'] = data['Opportunity ID'].astype(str).str.strip()
                data['contact_name_clean'] = data['contact name'].astype(str).str.lower().str.strip()
                data['phone_clean'] = data['phone'].astype(str).str.replace(r'[^\d]', '', regex=True)
                data['email_clean'] = data['email'].astype(str).str.lower().str.strip()

                # Find duplicates using precise criteria
                self.ghl_log("🔍 Searching for exact duplicates...")
                duplicate_pairs = []
                duplicate_records = set()

                # Compare each record with all subsequent records
                for i in range(len(data)):
                    if i in duplicate_records:
                        continue  # Skip if already identified as duplicate

                    current_record = data.iloc[i]
                    current_contact_id = current_record['contact_id_clean']
                    current_opp_id = current_record['opportunity_id_clean']
                    current_name = current_record['contact_name_clean']
                    current_phone = current_record['phone_clean']
                    current_email = current_record['email_clean']

                    # Skip if missing required fields
                    if pd.isna(current_contact_id) or pd.isna(current_opp_id) or current_contact_id == 'nan' or current_opp_id == 'nan':
                        continue

                    matches = []

                    for j in range(i + 1, len(data)):
                        if j in duplicate_records:
                            continue

                        compare_record = data.iloc[j]
                        compare_contact_id = compare_record['contact_id_clean']
                        compare_opp_id = compare_record['opportunity_id_clean']
                        compare_name = compare_record['contact_name_clean']
                        compare_phone = compare_record['phone_clean']
                        compare_email = compare_record['email_clean']

                        # Check if Contact ID and Opportunity ID match exactly
                        if (current_contact_id == compare_contact_id and
                            current_opp_id == compare_opp_id):

                            # Check if at least one contact detail matches
                            contact_detail_match = False
                            matching_detail = ""

                            if current_name == compare_name and current_name != 'nan' and current_name != '':
                                contact_detail_match = True
                                matching_detail = f"Name: '{current_name}'"
                            elif current_phone == compare_phone and current_phone != 'nan' and current_phone != '' and len(current_phone) > 3:
                                contact_detail_match = True
                                matching_detail = f"Phone: '{current_phone}'"
                            elif current_email == compare_email and current_email != 'nan' and current_email != '' and '@' in current_email:
                                contact_detail_match = True
                                matching_detail = f"Email: '{current_email}'"

                            if contact_detail_match:
                                matches.append({
                                    'index': j,
                                    'line': j + 1,  # 1-based line number
                                    'matching_detail': matching_detail
                                })
                                duplicate_records.add(j)

                    if matches:
                        # Add the original record and all its matches as a duplicate group
                        duplicate_pairs.append({
                            'original_index': i,
                            'original_line': i + 1,  # 1-based line number
                            'contact_id': current_contact_id,
                            'opportunity_id': current_opp_id,
                            'matches': matches,
                            'total_duplicates': len(matches) + 1
                        })
                        duplicate_records.add(i)

                # Process results
                self.ghl_log(f"✅ Analysis complete!")
                self.ghl_log(f"📊 Found {len(duplicate_pairs)} duplicate groups with {len(duplicate_records)} total duplicate records")

                # Compile results for display
                all_duplicate_records = list(duplicate_records)
                duplicate_summary = []

                for pair in duplicate_pairs:
                    # Create summary for each duplicate group
                    duplicate_summary.append({
                        'type': 'Exact Match',
                        'criteria': 'Contact ID + Opportunity ID + Contact Detail',
                        'contact_id': pair['contact_id'],
                        'opportunity_id': pair['opportunity_id'],
                        'original_line': pair['original_line'],
                        'duplicate_lines': [match['line'] for match in pair['matches']],
                        'matching_details': [match['matching_detail'] for match in pair['matches']],
                        'count': pair['total_duplicates'],
                        'rows': [pair['original_line']] + [match['line'] for match in pair['matches']],
                        'records': [pair['original_index']] + [match['index'] for match in pair['matches']]
                    })

                # Store results for other methods
                self.duplicate_analysis = {
                    'summary': duplicate_summary,
                    'all_duplicate_records': all_duplicate_records,
                    'total_records': total_records,
                    'clean_records': total_records - len(all_duplicate_records)
                }

                # Generate detailed report
                self.ghl_log(f"\n📊 PRECISE DUPLICATE ANALYSIS RESULTS:")
                self.ghl_log(f"   Total records analyzed: {total_records:,}")
                self.ghl_log(f"   Duplicate records found: {len(all_duplicate_records):,}")
                self.ghl_log(f"   Clean records: {total_records - len(all_duplicate_records):,}")
                self.ghl_log(f"   Duplicate groups: {len(duplicate_summary):,}")

                # Show detailed duplicate information
                if duplicate_summary:
                    self.ghl_log(f"\n🔍 DETAILED DUPLICATE MATCHES:")
                    for i, dup in enumerate(duplicate_summary, 1):
                        self.ghl_log(f"\n   Group {i}:")
                        self.ghl_log(f"      Contact ID: {dup['contact_id']}")
                        self.ghl_log(f"      Opportunity ID: {dup['opportunity_id']}")
                        self.ghl_log(f"      Original record: Line {dup['original_line']}")
                        self.ghl_log(f"      Duplicate records: Lines {', '.join(map(str, dup['duplicate_lines']))}")
                        self.ghl_log(f"      Matching details: {', '.join(dup['matching_details'])}")
                        self.ghl_log(f"      Total duplicates: {dup['count']} records")
                else:
                    self.ghl_log(f"\n✅ NO DUPLICATES FOUND using precise criteria!")
                    self.ghl_log(f"   All records have unique Contact ID + Opportunity ID combinations")
                    self.ghl_log(f"   or different contact details (name, phone, email)")

                # Show sample duplicates
                if duplicate_summary:
                    self.ghl_log(f"\n📋 Sample duplicate groups:")
                    for i, dup in enumerate(duplicate_summary[:3]):  # Show first 3 groups
                        self.ghl_log(f"   Group {i+1} ({dup['type']}):")
                        self.ghl_log(f"      Criteria: {dup['criteria']}")
                        self.ghl_log(f"      Records: {dup['count']} duplicates")
                        self.ghl_log(f"      Rows: {dup['rows']}")
                        if dup['contact_id'] != 'Various':
                            self.ghl_log(f"      Contact ID: {dup['contact_id']}")

                self.ghl_log(f"\n✅ Precise duplicate analysis complete!")
                self.set_activity_indicator("success", "Duplicates found")
                self.add_activity(f"Found {len(all_duplicate_records)} duplicate records")

                # Show summary dialog
                if duplicate_summary:
                    summary_msg = f"""🔍 Precise GHL Duplicate Analysis Results:

📊 Total Records: {total_records:,}
🔍 Duplicate Records: {len(all_duplicate_records):,}
✅ Clean Records: {total_records - len(all_duplicate_records):,}

📋 Exact Matches Found: {len(duplicate_summary)} groups
• Using strict criteria: Contact ID + Opportunity ID + Contact Detail

Use 'Duplicate Report' for detailed view with line numbers or 'Clean Duplicates' to remove them."""
                else:
                    summary_msg = f"""✅ No Duplicates Found!

📊 Total Records: {total_records:,}
🔍 Duplicate Records: 0
✅ Clean Records: {total_records:,}

All records have unique Contact ID + Opportunity ID combinations or different contact details."""

                messagebox.showinfo("Precise Duplicate Analysis Complete", summary_msg)

            except Exception as e:
                error_msg = f"Duplicate analysis failed: {str(e)}"
                self.ghl_log(f"❌ {error_msg}")
                self.set_activity_indicator("error", "Duplicate analysis failed")
                self.add_activity(f"Duplicate analysis error: {str(e)}")
                messagebox.showerror("Analysis Error", error_msg)

        threading.Thread(target=find_duplicates_thread, daemon=True).start()

    def show_duplicate_report(self):
        """Show detailed duplicate report in a new window"""
        if not hasattr(self, 'duplicate_analysis') or not self.duplicate_analysis:
            messagebox.showwarning("No Analysis", "Please run 'Find Duplicates' first")
            return

        # Create new window for duplicate report
        report_window = tk.Toplevel(self.master)
        report_window.title("🔍 GHL Duplicate Analysis Report")
        report_window.geometry("1000x700")
        report_window.transient(self.master)

        # Main frame with scrollbar
        main_frame = ttk_bootstrap.Frame(report_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # Summary section
        summary_frame = ttk_bootstrap.LabelFrame(main_frame, text="📊 Summary", padding=10)
        summary_frame.pack(fill=X, pady=(0, 10))

        analysis = self.duplicate_analysis
        summary_text = f"""Total Records: {analysis['total_records']:,}
Duplicate Records: {len(analysis['all_duplicate_records']):,}
Clean Records: {analysis['clean_records']:,}
Duplicate Groups: {len(analysis['summary']):,}"""

        ttk_bootstrap.Label(summary_frame, text=summary_text, font=('Arial', 10)).pack(anchor='w')

        # Detailed report section
        detail_frame = ttk_bootstrap.LabelFrame(main_frame, text="📋 Detailed Duplicate Groups", padding=10)
        detail_frame.pack(fill=BOTH, expand=True)

        # Create treeview for detailed report
        columns = ('Type', 'Criteria', 'Count', 'Rows', 'Contact ID', 'Opportunity ID', 'Date')
        tree = ttk.Treeview(detail_frame, columns=columns, show='headings', height=15)

        # Configure columns
        tree.heading('Type', text='Confidence Level')
        tree.heading('Criteria', text='Matching Criteria')
        tree.heading('Count', text='Records')
        tree.heading('Rows', text='Row Numbers')
        tree.heading('Contact ID', text='Contact ID')
        tree.heading('Opportunity ID', text='Opportunity ID')
        tree.heading('Date', text='Date Created')

        tree.column('Type', width=150)
        tree.column('Criteria', width=200)
        tree.column('Count', width=80)
        tree.column('Rows', width=150)
        tree.column('Contact ID', width=150)
        tree.column('Opportunity ID', width=150)
        tree.column('Date', width=120)

        # Populate treeview with new precise format
        for i, dup in enumerate(analysis['summary']):
            # Format line numbers for display
            if 'original_line' in dup and 'duplicate_lines' in dup:
                # New precise format - show original line and duplicate lines
                all_lines = [dup['original_line']] + dup['duplicate_lines']
                rows_str = ', '.join(map(str, all_lines[:5]))  # Show first 5 line numbers
                if len(all_lines) > 5:
                    rows_str += f" (+{len(all_lines)-5} more)"

                # Format matching details for the Date column
                matching_details = ', '.join(dup.get('matching_details', ['N/A']))
                date_display = matching_details[:30] + '...' if len(matching_details) > 30 else matching_details
            else:
                # Fallback for old format (shouldn't happen with new implementation)
                rows_str = ', '.join(map(str, dup.get('rows', [])[:5]))
                if len(dup.get('rows', [])) > 5:
                    rows_str += f" (+{len(dup.get('rows', []))-5} more)"
                date_display = dup.get('date_created', 'N/A')[:10] if dup.get('date_created', 'N/A') != 'Various' else 'Various'

            tree.insert('', 'end', values=(
                dup['type'],
                dup['criteria'],
                dup['count'],
                rows_str,
                dup['contact_id'][:20] + '...' if len(str(dup['contact_id'])) > 20 else dup['contact_id'],
                dup['opportunity_id'][:20] + '...' if len(str(dup['opportunity_id'])) > 20 else dup['opportunity_id'],
                date_display
            ))

        # Scrollbars for treeview
        v_scrollbar = ttk.Scrollbar(detail_frame, orient=VERTICAL, command=tree.yview)
        h_scrollbar = ttk.Scrollbar(detail_frame, orient=HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        tree.pack(side=LEFT, fill=BOTH, expand=True)
        v_scrollbar.pack(side=RIGHT, fill=Y)
        h_scrollbar.pack(side=BOTTOM, fill=X)

        # Action buttons
        button_frame = ttk_bootstrap.Frame(main_frame)
        button_frame.pack(fill=X, pady=(10, 0))

        ttk_bootstrap.Button(
            button_frame,
            text="📄 Export Report",
            command=lambda: self.export_duplicate_report(),
            bootstyle="info-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            button_frame,
            text="🧹 Clean Duplicates",
            command=lambda: [report_window.destroy(), self.clean_ghl_duplicates()],
            bootstyle="danger-outline"
        ).pack(side=LEFT, padx=5)

        ttk_bootstrap.Button(
            button_frame,
            text="❌ Close",
            command=report_window.destroy,
            bootstyle="secondary-outline"
        ).pack(side=RIGHT, padx=5)

    def clean_ghl_duplicates(self):
        """Clean duplicates from GHL data with user confirmation"""
        if not hasattr(self, 'duplicate_analysis') or not self.duplicate_analysis:
            messagebox.showwarning("No Analysis", "Please run 'Find Duplicates' first")
            return

        analysis = self.duplicate_analysis
        duplicate_count = len(analysis['all_duplicate_records'])

        if duplicate_count == 0:
            messagebox.showinfo("No Duplicates", "No duplicates found to clean")
            return

        # Confirm cleaning action
        confirm_msg = f"""🧹 Clean Duplicates Confirmation:

Found {duplicate_count:,} duplicate records in {len(analysis['summary']):,} groups.

Cleaning Strategy:
• Keep the FIRST occurrence of each duplicate group
• Remove subsequent duplicates
• Preserve data integrity

This will remove {duplicate_count - len(analysis['summary']):,} records.

Do you want to proceed?"""

        if not messagebox.askyesno("Confirm Duplicate Cleaning", confirm_msg):
            return

        def clean_thread():
            try:
                self.ghl_log("🧹 Starting duplicate cleaning process...")
                self.set_activity_indicator("working", "Cleaning duplicates")
                self.add_activity("GHL duplicate cleaning started")

                original_count = len(self.ghl_data)

                # Create a list of indices to keep (first occurrence of each group)
                indices_to_keep = set(range(len(self.ghl_data)))

                for dup_group in analysis['summary']:
                    group_indices = [idx - 1 for idx in dup_group['rows']]  # Convert to 0-based
                    # Keep the first occurrence, remove the rest
                    if group_indices:
                        indices_to_keep.discard(group_indices[0])  # Keep first
                        for idx in group_indices[1:]:  # Remove rest
                            indices_to_keep.discard(idx)
                        # Actually, we want to remove duplicates, so:
                        indices_to_keep.add(group_indices[0])  # Keep first
                        for idx in group_indices[1:]:  # Remove rest
                            indices_to_keep.discard(idx)

                # Create cleaned dataset
                indices_to_keep = sorted(list(indices_to_keep))
                cleaned_data = self.ghl_data.iloc[indices_to_keep].copy()

                # Update the main data
                self.ghl_data = cleaned_data

                removed_count = original_count - len(cleaned_data)

                self.ghl_log(f"✅ Duplicate cleaning complete!")
                self.ghl_log(f"   Original records: {original_count:,}")
                self.ghl_log(f"   Cleaned records: {len(cleaned_data):,}")
                self.ghl_log(f"   Removed duplicates: {removed_count:,}")

                # Clear previous analysis since data has changed
                if hasattr(self, 'duplicate_analysis'):
                    delattr(self, 'duplicate_analysis')

                self.set_activity_indicator("success", "Duplicates cleaned")
                self.add_activity(f"Removed {removed_count} duplicate records")

                result_msg = f"""🧹 Duplicate Cleaning Complete!

Original Records: {original_count:,}
Cleaned Records: {len(cleaned_data):,}
Removed Duplicates: {removed_count:,}

Your GHL data is now clean and ready for sync!"""

                messagebox.showinfo("Cleaning Complete", result_msg)

            except Exception as e:
                error_msg = f"Duplicate cleaning failed: {str(e)}"
                self.ghl_log(f"❌ {error_msg}")
                self.set_activity_indicator("error", "Cleaning failed")
                self.add_activity(f"Duplicate cleaning error: {str(e)}")
                messagebox.showerror("Cleaning Error", error_msg)

        threading.Thread(target=clean_thread, daemon=True).start()

    def export_duplicate_report(self):
        """Export duplicate analysis report to CSV"""
        if not hasattr(self, 'duplicate_analysis') or not self.duplicate_analysis:
            messagebox.showwarning("No Analysis", "Please run 'Find Duplicates' first")
            return

        try:
            # Ask user for save location
            filename = filedialog.asksaveasfilename(
                title="Save Duplicate Report",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if not filename:
                return

            # Prepare report data
            report_data = []
            for dup in self.duplicate_analysis['summary']:
                report_data.append({
                    'Confidence_Level': dup['type'],
                    'Matching_Criteria': dup['criteria'],
                    'Duplicate_Count': dup['count'],
                    'Row_Numbers': ', '.join(map(str, dup['rows'])),
                    'Contact_ID': dup['contact_id'],
                    'Opportunity_ID': dup['opportunity_id'],
                    'Date_Created': dup['date_created']
                })

            # Create DataFrame and save
            report_df = pd.DataFrame(report_data)
            report_df.to_csv(filename, index=False)

            self.ghl_log(f"📄 Duplicate report exported to: {filename}")
            self.add_activity("Duplicate report exported")
            messagebox.showinfo("Export Complete", f"Duplicate report saved to:\n{filename}")

        except Exception as e:
            error_msg = f"Export failed: {str(e)}"
            self.ghl_log(f"❌ {error_msg}")
            messagebox.showerror("Export Error", error_msg)

    # Google Sheets Integration Methods
    def authenticate_google_sheets(self):
        """Authenticate with Google Sheets API"""
        def auth_thread():
            try:
                self.sheets_log("🔑 Starting Google Sheets authentication...")
                self.set_activity_indicator("working", "Authenticating with Google")

                success = self.sheets_manager.authenticate()

                if success:
                    self.sheets_auth_status.config(text="✅ Authenticated", foreground="green")
                    self.sheets_log("✅ Successfully authenticated with Google Sheets!")
                    self.set_activity_indicator("success", "Google Sheets authenticated")
                    self.add_activity("Google Sheets authentication successful")

                    # Automatically refresh spreadsheets list
                    self.refresh_spreadsheets_list()
                else:
                    self.sheets_auth_status.config(text="❌ Authentication failed", foreground="red")
                    self.sheets_log("❌ Authentication failed")
                    self.set_activity_indicator("error", "Authentication failed")

            except Exception as e:
                error_msg = f"Authentication error: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                self.sheets_auth_status.config(text="❌ Authentication failed", foreground="red")
                self.set_activity_indicator("error", "Authentication failed")
                messagebox.showerror("Authentication Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=auth_thread, daemon=True).start()

    def refresh_sheets_connection(self):
        """Refresh Google Sheets connection"""
        try:
            if hasattr(self.sheets_manager, 'credentials') and self.sheets_manager.credentials:
                self.sheets_log("🔄 Refreshing Google Sheets connection...")
                success = self.sheets_manager.authenticate()
                if success:
                    self.sheets_log("✅ Connection refreshed successfully")
                    self.sheets_auth_status.config(text="✅ Authenticated", foreground="green")
                else:
                    self.sheets_log("❌ Failed to refresh connection")
                    self.sheets_auth_status.config(text="❌ Connection failed", foreground="red")
            else:
                messagebox.showinfo("Info", "Please authenticate first")
        except Exception as e:
            error_msg = f"Refresh failed: {str(e)}"
            self.sheets_log(f"❌ {error_msg}")
            messagebox.showerror("Refresh Error", error_msg)

    def test_sheets_connection(self):
        """Test Google Sheets connection"""
        def test_thread():
            try:
                if not hasattr(self.sheets_manager, 'service') or not self.sheets_manager.service:
                    messagebox.showerror("Error", "Please authenticate first")
                    return

                self.sheets_log("🧪 Testing Google Sheets connection...")
                self.set_activity_indicator("working", "Testing connection")

                # Try to get a simple list of spreadsheets
                spreadsheets = self.sheets_manager.get_spreadsheets()

                self.sheets_log(f"✅ Connection test successful! Found {len(spreadsheets)} accessible spreadsheets")
                self.set_activity_indicator("success", "Connection test passed")
                messagebox.showinfo("Connection Test", f"✅ Connection successful!\nFound {len(spreadsheets)} accessible spreadsheets")

            except Exception as e:
                error_msg = f"Connection test failed: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                self.set_activity_indicator("error", "Connection test failed")
                messagebox.showerror("Connection Test Failed", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=test_thread, daemon=True).start()

    def refresh_spreadsheets_list(self):
        """Refresh the list of available spreadsheets"""
        def refresh_thread():
            try:
                if not hasattr(self.sheets_manager, 'service') or not self.sheets_manager.service:
                    messagebox.showerror("Error", "Please authenticate first")
                    return

                self.sheets_log("🔄 Refreshing spreadsheets list...")
                self.set_activity_indicator("working", "Loading spreadsheets")

                # Clear existing items
                for item in self.sheets_tree.get_children():
                    self.sheets_tree.delete(item)

                # Get spreadsheets
                spreadsheets = self.sheets_manager.get_spreadsheets()

                # If no spreadsheets found (Drive API not available), show manual entry option
                if not spreadsheets:
                    self.sheets_log("⚠️ No spreadsheets found automatically")
                    self.sheets_log("💡 You can manually add spreadsheets using 'Add Manual Entry' button")

                    # Add saved spreadsheets from config
                    saved_sheets = self.config_manager.config.get("saved_sheets", [])
                    if saved_sheets:
                        self.sheets_log(f"📋 Loading {len(saved_sheets)} saved spreadsheets...")
                        for saved_sheet in saved_sheets:
                            self.sheets_tree.insert('', 'end', values=(
                                saved_sheet.get('name', 'Unknown'),
                                'Manual Entry',
                                'Saved',
                                saved_sheet.get('added_date', '')[:10] if saved_sheet.get('added_date') else '',
                                saved_sheet.get('id', '')
                            ))

                    self.set_activity_indicator("warning", "Manual entry required")
                    return

                # Populate treeview with auto-detected spreadsheets
                for sheet in spreadsheets:
                    # Format modified time
                    modified_time = sheet.get('modifiedTime', '')
                    if modified_time:
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(modified_time.replace('Z', '+00:00'))
                            modified_time = dt.strftime('%Y-%m-%d %H:%M')
                        except:
                            modified_time = modified_time[:10]  # Just date part

                    # Determine type
                    sheet_type = "Owned" if sheet.get('is_owned', False) else "Shared"

                    self.sheets_tree.insert('', 'end', values=(
                        sheet.get('name', 'Unknown'),
                        sheet.get('owner_name', 'Unknown'),
                        sheet_type,
                        modified_time,
                        sheet.get('id', '')
                    ))

                self.sheets_log(f"✅ Found {len(spreadsheets)} spreadsheets")
                self.set_activity_indicator("success", f"Loaded {len(spreadsheets)} spreadsheets")

            except Exception as e:
                error_msg = f"Failed to refresh spreadsheets: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                self.set_activity_indicator("error", "Failed to load spreadsheets")
                messagebox.showerror("Refresh Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def on_spreadsheet_select(self, event):
        """Handle spreadsheet selection"""
        selection = self.sheets_tree.selection()
        if selection:
            item = self.sheets_tree.item(selection[0])
            values = item['values']

            self.selected_spreadsheet_name = values[0]
            self.selected_spreadsheet_id = values[4]

            self.selected_sheet_label.config(
                text=f"Selected: {self.selected_spreadsheet_name} (ID: {self.selected_spreadsheet_id[:20]}...)"
            )

            # Load worksheets for selected spreadsheet
            self.load_worksheets()

    def load_worksheets(self):
        """Load worksheets for selected spreadsheet"""
        def load_thread():
            try:
                if not self.selected_spreadsheet_id:
                    return

                self.sheets_log(f"📋 Loading worksheets for {self.selected_spreadsheet_name}...")

                worksheets = self.sheets_manager.get_worksheets(self.selected_spreadsheet_id)

                # Update worksheet combobox
                worksheet_names = [ws['title'] for ws in worksheets]
                self.worksheet_combo['values'] = worksheet_names

                if worksheet_names:
                    self.worksheet_var.set(worksheet_names[0])  # Select first worksheet

                self.sheets_log(f"✅ Loaded {len(worksheets)} worksheets")

            except Exception as e:
                error_msg = f"Failed to load worksheets: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                messagebox.showerror("Worksheet Error", error_msg)

        threading.Thread(target=load_thread, daemon=True).start()

    def load_selected_spreadsheet(self):
        """Load data from selected spreadsheet"""
        if not self.selected_spreadsheet_id:
            messagebox.showerror("Error", "Please select a spreadsheet first")
            return

        worksheet = self.worksheet_var.get()
        if not worksheet:
            messagebox.showerror("Error", "Please select a worksheet first")
            return

        def load_thread():
            try:
                self.sheets_log(f"📊 Loading data from {worksheet} in {self.selected_spreadsheet_name}...")
                self.set_activity_indicator("working", "Loading spreadsheet data")

                # Read data from the worksheet
                range_name = f"{worksheet}!A:Z"  # Read all data
                data = self.sheets_manager.read_sheet_data(self.selected_spreadsheet_id, range_name)

                if data:
                    # Convert to DataFrame
                    import pandas as pd
                    df = pd.DataFrame(data[1:], columns=data[0])  # First row as headers

                    # Store the data
                    self.sheets_data = df

                    self.sheets_log(f"✅ Loaded {len(df)} rows and {len(df.columns)} columns")
                    self.set_activity_indicator("success", f"Loaded {len(df)} rows")
                    messagebox.showinfo("Success", f"Loaded {len(df)} rows from {worksheet}")
                else:
                    self.sheets_log("⚠️ No data found in the selected range")
                    messagebox.showwarning("No Data", "No data found in the selected worksheet")

            except Exception as e:
                error_msg = f"Failed to load spreadsheet: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                self.set_activity_indicator("error", "Failed to load data")
                messagebox.showerror("Load Error", error_msg)
            finally:
                self.status_var.set("Ready")

        threading.Thread(target=load_thread, daemon=True).start()

    def open_spreadsheet_in_browser(self):
        """Open selected spreadsheet in browser"""
        if not self.selected_spreadsheet_id:
            messagebox.showerror("Error", "Please select a spreadsheet first")
            return

        import webbrowser
        url = f"https://docs.google.com/spreadsheets/d/{self.selected_spreadsheet_id}/edit"
        webbrowser.open(url)
        self.sheets_log(f"🌐 Opened {self.selected_spreadsheet_name} in browser")

    def preview_worksheet_data(self):
        """Preview data from selected worksheet"""
        if not self.selected_spreadsheet_id:
            messagebox.showerror("Error", "Please select a spreadsheet first")
            return

        worksheet = self.worksheet_var.get()
        if not worksheet:
            messagebox.showerror("Error", "Please select a worksheet first")
            return

        def preview_thread():
            try:
                self.sheets_log(f"👁️ Previewing data from {worksheet}...")

                # Read first 10 rows for preview
                range_name = f"{worksheet}!A1:Z10"
                data = self.sheets_manager.read_sheet_data(self.selected_spreadsheet_id, range_name)

                if data:
                    # Create preview window
                    preview_window = tk.Toplevel(self.master)
                    preview_window.title(f"Preview: {worksheet}")
                    preview_window.geometry("800x400")

                    # Create treeview for preview
                    if len(data) > 0:
                        columns = data[0] if data[0] else [f"Col{i+1}" for i in range(len(data[0]) if data else 0)]
                        preview_tree = ttk.Treeview(preview_window, columns=columns, show='headings')

                        # Configure columns
                        for col in columns:
                            preview_tree.heading(col, text=str(col))
                            preview_tree.column(col, width=100)

                        # Add data
                        for row in data[1:]:  # Skip header row
                            preview_tree.insert('', 'end', values=row)

                        preview_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

                    self.sheets_log(f"✅ Preview loaded: {len(data)} rows")
                else:
                    messagebox.showwarning("No Data", "No data found in the selected range")

            except Exception as e:
                error_msg = f"Preview failed: {str(e)}"
                self.sheets_log(f"❌ {error_msg}")
                messagebox.showerror("Preview Error", error_msg)

        threading.Thread(target=preview_thread, daemon=True).start()

    def add_manual_spreadsheet(self):
        """Add a spreadsheet manually by ID"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Add Spreadsheet Manually")
        dialog.geometry("500x350")
        dialog.resizable(False, False)
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (350 // 2)
        dialog.geometry(f"500x350+{x}+{y}")

        # Main frame
        main_frame = ttk_bootstrap.Frame(dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk_bootstrap.Label(
            main_frame,
            text="Add Google Spreadsheet Manually",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # Instructions
        instructions = ttk_bootstrap.Label(
            main_frame,
            text="Enter the Google Spreadsheet ID or URL.\nYou can find this in the spreadsheet URL:\nhttps://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit",
            font=('Arial', 9),
            bootstyle="info",
            justify=CENTER
        )
        instructions.pack(pady=(0, 15))

        # Input frame
        input_frame = ttk_bootstrap.LabelFrame(main_frame, text="Spreadsheet Information", padding=15)
        input_frame.pack(fill=X, pady=(0, 15))

        # Spreadsheet ID/URL
        ttk_bootstrap.Label(input_frame, text="Spreadsheet ID or URL:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        id_var = tk.StringVar()
        id_entry = ttk_bootstrap.Entry(input_frame, textvariable=id_var, width=50, font=('Arial', 10))
        id_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)
        id_entry.focus()

        # Name
        ttk_bootstrap.Label(input_frame, text="Display Name:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        name_var = tk.StringVar()
        name_entry = ttk_bootstrap.Entry(input_frame, textvariable=name_var, width=50, font=('Arial', 10))
        name_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        # Description
        ttk_bootstrap.Label(input_frame, text="Description:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='nw', pady=5)
        desc_text = tk.Text(input_frame, width=50, height=3, font=('Arial', 10))
        desc_text.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)

        input_frame.columnconfigure(1, weight=1)

        # Buttons frame
        button_frame = ttk_bootstrap.Frame(main_frame)
        button_frame.pack(fill=X, pady=(15, 0))

        def extract_spreadsheet_id(url_or_id):
            """Extract spreadsheet ID from URL or return as-is if already an ID"""
            if 'docs.google.com/spreadsheets' in url_or_id:
                # Extract ID from URL
                import re
                match = re.search(r'/spreadsheets/d/([a-zA-Z0-9-_]+)', url_or_id)
                if match:
                    return match.group(1)
            return url_or_id.strip()

        def test_and_add():
            """Test the spreadsheet and add it if valid"""
            spreadsheet_input = id_var.get().strip()
            display_name = name_var.get().strip()
            description = desc_text.get(1.0, tk.END).strip()

            if not spreadsheet_input:
                messagebox.showerror("Error", "Please enter a spreadsheet ID or URL!")
                return

            # Extract ID from URL if needed
            spreadsheet_id = extract_spreadsheet_id(spreadsheet_input)

            if not display_name:
                display_name = f"Spreadsheet {spreadsheet_id[:8]}..."

            try:
                # Test if we can access the spreadsheet
                if not self.sheets_manager.service:
                    messagebox.showerror("Error", "Please authenticate with Google Sheets first!")
                    return

                # Try to get spreadsheet metadata
                spreadsheet = self.sheets_manager.service.spreadsheets().get(
                    spreadsheetId=spreadsheet_id
                ).execute()

                actual_name = spreadsheet.get('properties', {}).get('title', display_name)

                # Add to saved sheets in config
                saved_sheet = {
                    "id": spreadsheet_id,
                    "name": display_name,
                    "actual_name": actual_name,
                    "description": description,
                    "added_date": datetime.datetime.now().isoformat(),
                    "manual_entry": True
                }

                saved_sheets = self.config_manager.config.get("saved_sheets", [])

                # Check if already exists
                for existing in saved_sheets:
                    if existing.get("id") == spreadsheet_id:
                        messagebox.showerror("Error", f"Spreadsheet '{actual_name}' is already added!")
                        return

                saved_sheets.append(saved_sheet)
                self.config_manager.config["saved_sheets"] = saved_sheets
                self.config_manager.save_config()

                # Add to treeview
                self.sheets_tree.insert('', 'end', values=(
                    display_name,
                    'Manual Entry',
                    'Saved',
                    datetime.datetime.now().strftime('%Y-%m-%d'),
                    spreadsheet_id
                ))

                self.sheets_log(f"✅ Added spreadsheet: {display_name}")
                messagebox.showinfo("Success", f"Spreadsheet '{display_name}' added successfully!\n\nActual name: {actual_name}")
                dialog.destroy()

            except Exception as e:
                error_msg = f"Failed to access spreadsheet: {str(e)}"
                if "not found" in str(e).lower():
                    error_msg = "Spreadsheet not found. Please check the ID and make sure you have access to it."
                elif "permission" in str(e).lower():
                    error_msg = "Permission denied. Please make sure the spreadsheet is shared with you."

                messagebox.showerror("Error", error_msg)

        # Test & Add button
        test_btn = ttk_bootstrap.Button(
            button_frame,
            text="🧪 Test & Add",
            command=test_and_add,
            bootstyle="success",
            width=15
        )
        test_btn.pack(side=LEFT, padx=5)

        # Cancel button
        cancel_btn = ttk_bootstrap.Button(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT, padx=5)

    def import_from_sheets(self):
        """Import data from Google Sheets to local data"""
        messagebox.showinfo("Coming Soon", "Import from Google Sheets feature will be implemented next!")

    def export_to_sheets(self):
        """Export local data to Google Sheets"""
        messagebox.showinfo("Coming Soon", "Export to Google Sheets feature will be implemented next!")

    def sync_bidirectional(self):
        """Sync data both ways between local and Google Sheets"""
        messagebox.showinfo("Coming Soon", "Bidirectional sync feature will be implemented next!")

    def save_sheets_to_client_folder(self):
        """Save currently loaded Google Sheets data to client folder"""
        try:
            # Check if client is selected
            current_client_name = self.client_var.get() if hasattr(self, 'client_var') else None
            if not current_client_name:
                messagebox.showerror("No Client Selected", "Please select a client before saving data.")
                return

            # Check if we have loaded spreadsheet data
            selection = self.sheets_tree.selection()
            if not selection:
                messagebox.showerror("No Selection", "Please select a spreadsheet first.")
                return

            # Get selected spreadsheet info
            item = self.sheets_tree.item(selection[0])
            spreadsheet_name = item['values'][0]
            spreadsheet_id = item['values'][4]  # ID is in the 5th column

            # Check if worksheet is selected
            selected_worksheet = self.worksheet_var.get()
            if not selected_worksheet:
                messagebox.showerror("No Worksheet Selected", "Please select a worksheet to save.")
                return

            self.sheets_log(f"💾 Saving data to client folder...")
            self.sheets_log(f"👤 Client: {current_client_name}")
            self.sheets_log(f"📊 Spreadsheet: {spreadsheet_name}")
            self.sheets_log(f"📋 Worksheet: {selected_worksheet}")

            # Read data from the selected worksheet
            data = self.sheets_manager.get_sheet_data(spreadsheet_id, selected_worksheet)

            if not data or len(data) == 0:
                messagebox.showwarning("No Data", "No data found in the selected worksheet.")
                return

            # Convert to DataFrame
            import pandas as pd

            # First row as headers
            headers = data[0] if data else []
            rows = data[1:] if len(data) > 1 else []

            if not headers:
                messagebox.showerror("No Headers", "No headers found in the worksheet.")
                return

            # Create DataFrame
            df = pd.DataFrame(rows, columns=headers)

            # Clean up the data
            df = df.dropna(how='all')  # Remove empty rows

            if df.empty:
                messagebox.showwarning("No Data", "No valid data found after cleaning.")
                return

            # Generate client-specific filename
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_client_name = self.client_manager.sanitize_folder_name(current_client_name)
            safe_sheet_name = self.client_manager.sanitize_folder_name(spreadsheet_name)
            safe_worksheet_name = self.client_manager.sanitize_folder_name(selected_worksheet)

            # Create filename with client-specific naming convention
            base_filename = f"{safe_client_name}_sheets_{safe_sheet_name}_{safe_worksheet_name}_{timestamp}"
            csv_filename = f"{base_filename}.csv"
            excel_filename = f"{base_filename}.xlsx"

            # Save to client's data folder
            csv_path = self.client_manager.get_client_file_path(current_client_name, csv_filename, "data")
            excel_path = self.client_manager.get_client_file_path(current_client_name, excel_filename, "exports")

            # Save CSV format
            df.to_csv(csv_path, index=False)
            self.sheets_log(f"💾 Saved CSV: {csv_path}")

            # Save Excel format
            df.to_excel(excel_path, index=False, sheet_name=selected_worksheet)
            self.sheets_log(f"📊 Saved Excel: {excel_path}")

            # Log summary
            self.sheets_log(f"✅ Save completed:")
            self.sheets_log(f"   📊 Records: {len(df):,}")
            self.sheets_log(f"   📋 Columns: {len(df.columns)}")
            self.sheets_log(f"   📁 Client: {current_client_name}")

            # Show success message
            messagebox.showinfo(
                "Save Successful",
                f"Google Sheets data saved successfully!\n\n"
                f"Client: {current_client_name}\n"
                f"Source: {spreadsheet_name} → {selected_worksheet}\n"
                f"Records: {len(df):,}\n"
                f"Columns: {len(df.columns)}\n\n"
                f"Files saved:\n"
                f"• CSV: {csv_filename}\n"
                f"• Excel: {excel_filename}"
            )

        except Exception as e:
            error_msg = f"Save failed: {str(e)}"
            self.sheets_log(f"❌ {error_msg}")
            messagebox.showerror("Save Error", error_msg)

    def sheets_log(self, message):
        """Add message to Google Sheets status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'sheets_status_text'):
            self.sheets_status_text.insert(tk.END, formatted_message + "\n")
            self.sheets_status_text.see(tk.END)

    def ghl_log(self, message):
        """Add message to GHL status log"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        if hasattr(self, 'ghl_status_text'):
            self.ghl_status_text.insert(tk.END, formatted_message + "\n")
            self.ghl_status_text.see(tk.END)

    def quick_extract(self):
        """Quick extract (placeholder)"""
        self.log("Quick extract started...")
        messagebox.showinfo("Info", "Quick extract - Feature coming soon!")

    def generate_report(self):
        """Generate report (placeholder)"""
        self.log("Generating report...")
        messagebox.showinfo("Info", "Report generation - Feature coming soon!")

    def quick_export(self):
        """Quick export (placeholder)"""
        self.log("Quick export started...")
        messagebox.showinfo("Info", "Quick export - Feature coming soon!")

    def refresh_data(self):
        """Refresh data (placeholder)"""
        self.log("Refreshing data...")
        messagebox.showinfo("Info", "Data refresh - Feature coming soon!")

    def update_chart(self, event=None):
        """Update chart (placeholder)"""
        self.log("Updating chart...")
        # Chart update logic will be implemented

    def add_client_dialog(self):
        """Add client dialog"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Add New Client")
        dialog.geometry("600x550")
        dialog.resizable(True, True)  # Make it resizable
        dialog.minsize(500, 450)  # Set minimum size
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (550 // 2)
        dialog.geometry(f"600x550+{x}+{y}")

        # Main frame with scrollable content
        main_frame = ttk_bootstrap.Frame(dialog, padding=15)
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk_bootstrap.Label(
            main_frame,
            text="Add New Client",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # Client information frame - enhanced
        info_frame = ttk_bootstrap.LabelFrame(main_frame, text="📝 Client Information", padding=20)
        info_frame.pack(fill=X, pady=(0, 15))

        # Client Name
        ttk_bootstrap.Label(info_frame, text="Client Name:", font=('Arial', 11, 'bold')).grid(row=0, column=0, sticky='w', pady=8)
        client_name_var = tk.StringVar()
        client_name_entry = ttk_bootstrap.Entry(info_frame, textvariable=client_name_var, width=40, font=('Arial', 11))
        client_name_entry.grid(row=0, column=1, sticky='ew', padx=(15, 0), pady=8)
        client_name_entry.focus()

        # Customer ID
        ttk_bootstrap.Label(info_frame, text="Customer ID:", font=('Arial', 11, 'bold')).grid(row=1, column=0, sticky='w', pady=8)
        customer_id_var = tk.StringVar()
        customer_id_entry = ttk_bootstrap.Entry(info_frame, textvariable=customer_id_var, width=40, font=('Arial', 11))
        customer_id_entry.grid(row=1, column=1, sticky='ew', padx=(15, 0), pady=8)

        # Notes
        ttk_bootstrap.Label(info_frame, text="Notes:", font=('Arial', 11, 'bold')).grid(row=2, column=0, sticky='nw', pady=8)
        notes_text = tk.Text(
            info_frame,
            width=40,
            height=4,
            font=('Arial', 10),
            wrap=tk.WORD,
            relief='sunken',
            borderwidth=1
        )
        notes_text.grid(row=2, column=1, sticky='ew', padx=(15, 0), pady=8)

        info_frame.columnconfigure(1, weight=1)

        # Folder preview section
        preview_frame = ttk_bootstrap.LabelFrame(main_frame, text="📁 Folder Structure Preview", padding=15)
        preview_frame.pack(fill=BOTH, expand=True, pady=(10, 15))

        # Create a frame for the text widget with scrollbar
        text_frame = ttk_bootstrap.Frame(preview_frame)
        text_frame.pack(fill=BOTH, expand=True)

        # Text widget with scrollbar
        preview_text = tk.Text(
            text_frame,
            height=8,
            width=50,
            font=('Consolas', 10),
            state='disabled',
            wrap=tk.WORD,
            bg='#f8f9fa',
            relief='sunken',
            borderwidth=2
        )

        # Scrollbar for text widget
        scrollbar = ttk.Scrollbar(text_frame, orient=VERTICAL, command=preview_text.yview)
        preview_text.configure(yscrollcommand=scrollbar.set)

        # Pack text widget and scrollbar
        preview_text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        def update_preview():
            """Update folder structure preview"""
            client_name = client_name_var.get().strip()
            if client_name:
                safe_name = self.client_manager.sanitize_folder_name(client_name)
                preview_content = f"""📁 clients/
└── 📁 {safe_name}/
    ├── 📁 exports/     (CSV, Excel files)
    ├── 📁 data/        (Raw data files)
    ├── 📁 reports/     (Generated reports)
    └── 📁 temp/        (Temporary files)"""
            else:
                preview_content = "Enter a client name to see folder structure..."

            preview_text.config(state='normal')
            preview_text.delete(1.0, tk.END)
            preview_text.insert(1.0, preview_content)
            preview_text.config(state='disabled')

        # Bind preview update to name change
        client_name_var.trace('w', lambda *args: update_preview())
        update_preview()

        # Buttons frame - fixed positioning
        button_frame = ttk_bootstrap.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 10), side=BOTTOM)

        def save_client():
            """Save the new client"""
            client_name = client_name_var.get().strip()
            customer_id = customer_id_var.get().strip()
            notes = notes_text.get(1.0, tk.END).strip()

            if not client_name:
                messagebox.showerror("Error", "Client name is required!")
                return

            # Check if client already exists
            existing_clients = self.config_manager.config.get("clients", [])
            for client in existing_clients:
                if client.get("client_name", "").lower() == client_name.lower():
                    messagebox.showerror("Error", f"Client '{client_name}' already exists!")
                    return

            # Format customer ID
            customer_id_formatted = ""
            if customer_id:
                # Remove any existing formatting
                clean_id = customer_id.replace("-", "").replace(" ", "")
                if len(clean_id) == 10 and clean_id.isdigit():
                    customer_id_formatted = f"{clean_id[:3]}-{clean_id[3:6]}-{clean_id[6:]}"
                else:
                    customer_id_formatted = customer_id

            # Create client entry
            new_client = {
                "client_name": client_name,
                "customer_id": customer_id.replace("-", "") if customer_id else "",
                "customer_id_formatted": customer_id_formatted,
                "notes": notes,
                "added_date": datetime.datetime.now().isoformat(),
                "auto_detected": False
            }

            # Add to config
            self.config_manager.config.setdefault("clients", []).append(new_client)

            # Create client folder
            self.client_manager.create_client_folder(client_name)

            # Save config
            self.config_manager.save_config()

            # Update client list
            self.update_client_list()

            # Select the new client
            self.client_var.set(client_name)
            self.on_client_select()

            self.log(f"✅ Added new client: {client_name}")
            messagebox.showinfo("Success", f"Client '{client_name}' added successfully!")
            dialog.destroy()

        # Button styling and layout
        button_container = ttk_bootstrap.Frame(button_frame)
        button_container.pack(expand=True)

        # Save button - enhanced styling
        save_btn = ttk_bootstrap.Button(
            button_container,
            text="💾 Add Client",
            command=save_client,
            bootstyle="success",
            width=18
        )
        save_btn.pack(side=LEFT, padx=10, pady=5)

        # Cancel button - enhanced styling
        cancel_btn = ttk_bootstrap.Button(
            button_container,
            text="❌ Cancel",
            command=dialog.destroy,
            bootstyle="secondary",
            width=18
        )
        cancel_btn.pack(side=LEFT, padx=10, pady=5)

        # Add some spacing at the bottom
        spacer_frame = ttk_bootstrap.Frame(main_frame, height=10)
        spacer_frame.pack(side=BOTTOM)

    def edit_client_dialog(self):
        """Edit client dialog"""
        current_client_name = self.client_var.get()
        if not current_client_name:
            messagebox.showwarning("No Selection", "Please select a client to edit.")
            return

        # Find current client data
        current_client = None
        for client in self.config_manager.config.get("clients", []):
            if client.get("client_name") == current_client_name:
                current_client = client
                break

        if not current_client:
            messagebox.showerror("Error", f"Client '{current_client_name}' not found in configuration.")
            return

        dialog = tk.Toplevel(self.master)
        dialog.title(f"Edit Client: {current_client_name}")
        dialog.geometry("600x500")
        dialog.resizable(True, True)  # Make it resizable
        dialog.minsize(500, 400)  # Set minimum size
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        # Main frame
        main_frame = ttk_bootstrap.Frame(dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)

        # Title
        title_label = ttk_bootstrap.Label(
            main_frame,
            text=f"Edit Client: {current_client_name}",
            font=('Arial', 16, 'bold'),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # Client information frame
        info_frame = ttk_bootstrap.LabelFrame(main_frame, text="Client Information", padding=15)
        info_frame.pack(fill=X, pady=(0, 15))

        # Client Name
        ttk_bootstrap.Label(info_frame, text="Client Name:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        client_name_var = tk.StringVar(value=current_client.get("client_name", ""))
        client_name_entry = ttk_bootstrap.Entry(info_frame, textvariable=client_name_var, width=30, font=('Arial', 10))
        client_name_entry.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=5)

        # Customer ID
        ttk_bootstrap.Label(info_frame, text="Customer ID:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        customer_id_var = tk.StringVar(value=current_client.get("customer_id_formatted", ""))
        customer_id_entry = ttk_bootstrap.Entry(info_frame, textvariable=customer_id_var, width=30, font=('Arial', 10))
        customer_id_entry.grid(row=1, column=1, sticky='ew', padx=(10, 0), pady=5)

        # Notes
        ttk_bootstrap.Label(info_frame, text="Notes:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='nw', pady=5)
        notes_text = tk.Text(info_frame, width=30, height=4, font=('Arial', 10))
        notes_text.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=5)
        notes_text.insert(1.0, current_client.get("notes", ""))

        info_frame.columnconfigure(1, weight=1)

        # Client files info
        files_frame = ttk_bootstrap.LabelFrame(main_frame, text="Client Files", padding=15)
        files_frame.pack(fill=BOTH, expand=True, pady=(0, 15))

        # Show client files summary
        client_files = self.client_manager.list_client_files(current_client_name)
        files_info = f"📁 Client folder: {self.client_manager.get_client_folder(current_client_name)}\n"
        files_info += f"📄 Total files: {len(client_files)}\n"
        if client_files:
            files_info += f"📅 Last modified: {datetime.datetime.fromtimestamp(max(f['modified'] for f in client_files)).strftime('%Y-%m-%d %H:%M')}"

        files_label = ttk_bootstrap.Label(files_frame, text=files_info, font=('Arial', 9))
        files_label.pack(anchor='w')

        # Buttons frame
        button_frame = ttk_bootstrap.Frame(main_frame)
        button_frame.pack(fill=X, pady=(15, 0))

        def save_changes():
            """Save client changes"""
            new_name = client_name_var.get().strip()
            customer_id = customer_id_var.get().strip()
            notes = notes_text.get(1.0, tk.END).strip()

            if not new_name:
                messagebox.showerror("Error", "Client name is required!")
                return

            # Check if new name conflicts with existing clients (except current)
            existing_clients = self.config_manager.config.get("clients", [])
            for client in existing_clients:
                if (client.get("client_name", "").lower() == new_name.lower() and
                    client.get("client_name") != current_client_name):
                    messagebox.showerror("Error", f"Client '{new_name}' already exists!")
                    return

            # Format customer ID
            customer_id_formatted = ""
            if customer_id:
                clean_id = customer_id.replace("-", "").replace(" ", "")
                if len(clean_id) == 10 and clean_id.isdigit():
                    customer_id_formatted = f"{clean_id[:3]}-{clean_id[3:6]}-{clean_id[6:]}"
                else:
                    customer_id_formatted = customer_id

            # Update client data
            current_client["client_name"] = new_name
            current_client["customer_id"] = customer_id.replace("-", "") if customer_id else ""
            current_client["customer_id_formatted"] = customer_id_formatted
            current_client["notes"] = notes
            current_client["modified_date"] = datetime.datetime.now().isoformat()

            # If name changed, rename folder
            if new_name != current_client_name:
                old_folder = self.client_manager.get_client_folder(current_client_name)
                new_folder = self.client_manager.get_client_folder(new_name)

                try:
                    if os.path.exists(old_folder) and not os.path.exists(new_folder):
                        os.rename(old_folder, new_folder)
                        self.log(f"📁 Renamed client folder: {current_client_name} → {new_name}")
                except Exception as e:
                    self.log(f"⚠️ Could not rename folder: {str(e)}")

            # Save config
            self.config_manager.save_config()

            # Update client list
            self.update_client_list()

            # Select the updated client
            self.client_var.set(new_name)
            self.on_client_select()

            self.log(f"✅ Updated client: {new_name}")
            messagebox.showinfo("Success", f"Client '{new_name}' updated successfully!")
            dialog.destroy()

        # Save button
        save_btn = ttk_bootstrap.Button(
            button_frame,
            text="💾 Save Changes",
            command=save_changes,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=LEFT, padx=5)

        # Cancel button
        cancel_btn = ttk_bootstrap.Button(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT, padx=5)

    def remove_client_dialog(self):
        """Remove client dialog"""
        current_client_name = self.client_var.get()
        if not current_client_name:
            messagebox.showwarning("No Selection", "Please select a client to remove.")
            return

        # Get client files count
        client_files = self.client_manager.list_client_files(current_client_name)
        client_folder = self.client_manager.get_client_folder(current_client_name)

        # Confirmation dialog
        message = f"Are you sure you want to remove client '{current_client_name}'?\n\n"
        message += f"📁 Client folder: {client_folder}\n"
        message += f"📄 Files found: {len(client_files)}\n\n"
        message += "⚠️ This will:\n"
        message += "• Remove client from configuration\n"
        message += "• Keep the client folder and files intact\n"
        message += "• You can re-add the client later if needed"

        result = messagebox.askyesno(
            "Confirm Removal",
            message,
            icon='warning'
        )

        if result:
            # Remove from config
            clients = self.config_manager.config.get("clients", [])
            self.config_manager.config["clients"] = [
                client for client in clients
                if client.get("client_name") != current_client_name
            ]

            # Save config
            self.config_manager.save_config()

            # Update client list
            self.update_client_list()

            # Clear current selection
            self.client_var.set("")
            self.current_client = None
            self.status_var.set("Ready")

            self.log(f"🗑️ Removed client: {current_client_name}")
            messagebox.showinfo("Success", f"Client '{current_client_name}' removed from configuration.\n\nFolder and files are preserved.")
        else:
            self.log(f"❌ Client removal cancelled: {current_client_name}")

    def add_schedule_dialog(self):
        """Add schedule dialog (placeholder)"""
        self.log("Add schedule dialog...")
        messagebox.showinfo("Info", "Add schedule - Feature coming soon!")

    def edit_schedule_dialog(self):
        """Edit schedule dialog (placeholder)"""
        self.log("Edit schedule dialog...")
        messagebox.showinfo("Info", "Edit schedule - Feature coming soon!")

    def remove_schedule_dialog(self):
        """Remove schedule dialog (placeholder)"""
        self.log("Remove schedule dialog...")
        messagebox.showinfo("Info", "Remove schedule - Feature coming soon!")

    def run_schedule_now(self):
        """Run schedule now (placeholder)"""
        self.log("Running schedule now...")
        messagebox.showinfo("Info", "Run schedule - Feature coming soon!")

    def start_scheduler(self):
        """Start scheduler (placeholder)"""
        self.log("Scheduler started")

    def new_config(self):
        """New configuration"""
        if messagebox.askyesno("New Configuration", "This will reset all settings. Continue?"):
            self.config_manager.config = self.config_manager.default_config()
            self.load_settings()
            self.log("Configuration reset to defaults")

    def export_config(self):
        """Export configuration"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.config_manager.config, f, indent=4)
                self.log(f"Configuration exported to {filename}")
                messagebox.showinfo("Success", f"Configuration exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export configuration: {str(e)}")

    def import_config(self):
        """Import configuration"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    imported_config = json.load(f)
                self.config_manager.config = self.config_manager.validate_config(imported_config)
                self.load_settings()
                self.log(f"Configuration imported from {filename}")
                messagebox.showinfo("Success", f"Configuration imported from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import configuration: {str(e)}")

    def reset_config(self):
        """Reset configuration to defaults"""
        if messagebox.askyesno("Reset Configuration", "This will reset all settings to defaults. Continue?"):
            self.config_manager.config = self.config_manager.default_config()
            self.config_manager.save_config()
            self.load_settings()
            self.log("Configuration reset to defaults")

    def open_schedule_manager(self):
        """Open schedule manager (placeholder)"""
        self.log("Opening schedule manager...")
        messagebox.showinfo("Info", "Schedule manager - Feature coming soon!")

    def show_help(self):
        """Show help dialog"""
        help_text = """
Google Ads Data Extractor - Professional Edition

This modern application helps you extract and manage Google Ads data with:

🔐 Secure credential management
📊 Advanced data visualization
📤 Multiple export formats
🗃️ Airtable integration & auto-sync
⏰ Automated scheduling
🎨 Modern themes and UI

Airtable Integration:
• Sync data directly to your Airtable workspace
• Auto-sync after data extraction
• Replace or append data options
• Real-time sync status monitoring

For support, visit: https://github.com/your-repo
        """
        messagebox.showinfo("Help", help_text)

    def open_api_docs(self):
        """Open API documentation"""
        import webbrowser
        webbrowser.open("https://developers.google.com/google-ads/api/docs")

    def show_about(self):
        """Show about dialog"""
        about_text = """
Google Ads Data Extractor
Professional Edition v2.0

Built with:
• Python 3.x
• ttkbootstrap for modern UI
• Google Ads API
• Matplotlib for charts

© 2024 - Professional Google Ads Management Tool
        """
        messagebox.showinfo("About", about_text)

    def on_closing(self):
        """Handle application closing"""
        if self.auto_save_var.get():
            self.save_settings()

        self.log("Application closing...")
        self.master.destroy()

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ModernGoogleAdsExtractor(root)

    # Center the window
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()