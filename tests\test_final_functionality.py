#!/usr/bin/env python3
"""
Final test of the enhanced client database functionality
"""

import re

def parse_notes_for_client_info(notes_text):
    """
    Parse notes text to extract business name and customer ID

    Expected format:
    Business Name
    Repair Lift
    XXX-XXX-XXXX
    """
    if not notes_text or not notes_text.strip():
        return None, None
        
    try:
        # Split by various line break patterns
        lines = []
        for line in notes_text.replace('\r\n', '\n').replace('\r', '\n').split('\n'):
            line = line.strip()
            if line:  # Only add non-empty lines
                lines.append(line)
        
        if len(lines) < 2:
            return None, None
            
        # Extract business name (first line)
        business_name = lines[0].strip()
        
        # Capitalize first letter of each word if not already capitalized
        if business_name:
            # Split by spaces and capitalize each word
            words = business_name.split()
            capitalized_words = []
            for word in words:
                if word and not word[0].isupper():
                    capitalized_words.append(word.capitalize())
                else:
                    capitalized_words.append(word)
            business_name = ' '.join(capitalized_words)
        
        # Extract customer ID (look for phone number pattern)
        customer_id = None
        phone_pattern = r'\b\d{3}-\d{3}-\d{4}\b'  # XXX-XXX-XXXX format

        for line in lines:
            match = re.search(phone_pattern, line)
            if match:
                customer_id = match.group()
                break

        return business_name, customer_id
        
    except Exception as e:
        print(f"❌ Error parsing notes: {str(e)}")
        return None, None

def test_user_examples():
    """Test with the exact examples provided by the user"""
    print("🧪 Testing User-Provided Examples")
    print("=" * 50)
    
    examples = [
        "we fix wireless\nRepair Lift\n************",
        "Cell Phone Fix\nRepair Lift\n************", 
        "Colorado Gadget Fix\nRepair Lift\n************"
    ]
    
    expected_results = [
        ("We Fix Wireless", "************"),
        ("Cell Phone Fix", "************"),
        ("Colorado Gadget Fix", "************")
    ]
    
    all_passed = True
    
    for i, (example, expected) in enumerate(zip(examples, expected_results), 1):
        print(f"\nTest {i}:")
        print(f"Input: {repr(example)}")
        
        business_name, customer_id = parse_notes_for_client_info(example)
        expected_business, expected_customer = expected

        print(f"Expected: Business='{expected_business}', Customer='{expected_customer}'")
        print(f"Got:      Business='{business_name}', Customer='{customer_id}'")

        if business_name == expected_business and customer_id == expected_customer:
            print("✅ PASS")
        else:
            print("❌ FAIL")
            all_passed = False
        
        print("-" * 30)
    
    print(f"\n{'✅ ALL TESTS PASSED!' if all_passed else '❌ SOME TESTS FAILED!'}")
    return all_passed

if __name__ == "__main__":
    test_user_examples()
