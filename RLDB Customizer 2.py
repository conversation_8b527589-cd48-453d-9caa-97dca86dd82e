#!/usr/bin/env python3
"""
RL Dashboard Customizer v3.0 - Complete Version with GUI
Enhanced version with comprehensive verification and test8/clzone template base

Features:
- Full GUI interface for easy client configuration
- Uses test8/clzone as the base template (most advanced version)
- Dynamic tab management and store title customization
- Comprehensive verification system
- Detailed logging and error checking
- Supports all data source configurations
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import shutil
import re
from pathlib import Path
import requests
from typing import Dict, List, Optional
import threading
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import webbrowser
import logging
from datetime import datetime
import sqlite3

# === CONFIGURATION MANAGER ===
class ConfigManager:
    """Manages configuration persistence for Google Ads credentials and settings"""

    def __init__(self, config_file="customizer_config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Validate and merge with defaults
                    return self.validate_config(config)
            else:
                return self.default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.default_config()

    def save_config(self):
        """Save configuration to file with backup"""
        try:
            # Create backup if file exists
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup"
                shutil.copy2(self.config_file, backup_file)

            # Save new config
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False

    def validate_config(self, config):
        """Validate configuration structure and merge with defaults"""
        default = self.default_config()

        # Ensure all required keys exist
        for key in default:
            if key not in config:
                config[key] = default[key]

        # Validate Google Ads credentials structure
        if "google_ads_credentials" not in config or not isinstance(config["google_ads_credentials"], dict):
            config["google_ads_credentials"] = default["google_ads_credentials"]

        for cred_key in default["google_ads_credentials"]:
            if cred_key not in config["google_ads_credentials"]:
                config["google_ads_credentials"][cred_key] = default["google_ads_credentials"][cred_key]

        return config

    def default_config(self):
        """Return default configuration"""
        return {
            "google_ads_credentials": {
                "customer_id": "",
                "developer_token": "bGBR3u83ozZjmx1WXckSkQ",
                "refresh_token": "1//01NsefTx-HCBvCgYIARAAGAESNwF-L9IrG7rF3r0GzraiGQPO-cBWzppPxA0pggmi8KtWhZqpSZBDUAG3J_qbKx1xWo06hY9GaSc",
                "manager_account_id": "**********"
            },
            "google_ads_airtable": {
                "api_key": "",
                "base_id": "",
                "table_id": "",
                "auto_sync": False,
                "sync_mode": "incremental"
            },
            "google_ads_settings": {
                "last_date_preset": "Last 30 days",
                "auto_load_campaigns": False,
                "default_campaigns": []
            },
            "window_settings": {
                "geometry": "1400x900",
                "last_tab": "Client Configuration"
            },
            "app_settings": {
                "auto_save": True,
                "show_tooltips": True,
                "log_level": "INFO"
            }
        }

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.FileHandler('dashboard_customizer_v3_complete.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class EnhancedAirtableManager:
    """Enhanced Airtable API integration manager adapted from RLEx"""

    def __init__(self, api_key=None):
        self.api_key = api_key
        self.base_id = None
        self.table_id = None
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        } if api_key else None
        self.api_url = "https://api.airtable.com/v0"

    def set_credentials(self, api_key, base_id, table_id):
        """Set Airtable credentials"""
        self.api_key = api_key
        self.base_id = base_id
        self.table_id = table_id
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    def test_connection(self):
        """Test Airtable API connection"""
        if not self.api_key or not self.base_id or not self.table_id:
            return False, "Missing credentials (API key, base ID, or table ID)"

        try:
            url = f"{self.api_url}/{self.base_id}/{self.table_id}"
            params = {'maxRecords': 1}
            response = requests.get(url, headers=self.headers, params=params, timeout=10)

            if response.status_code == 200:
                return True, "Connection successful"
            elif response.status_code == 401:
                return False, "Invalid API key"
            elif response.status_code == 404:
                return False, "Base or table not found"
            else:
                return False, f"API Error: {response.status_code} - {response.text}"
        except Exception as e:
            return False, f"Connection error: {str(e)}"

    def get_records(self, max_records=100, date_range=None):
        """Get records from Airtable with optional filtering"""
        if not self.headers:
            return []

        try:
            url = f"{self.api_url}/{self.base_id}/{self.table_id}"
            all_records = []
            offset = None

            while True:
                params = {'maxRecords': min(max_records, 100)}

                if date_range:
                    start_date, end_date = date_range
                    filter_formula = f"AND(IS_AFTER({{Date}}, '{start_date}'), IS_BEFORE({{Date}}, '{end_date}'))"
                    params['filterByFormula'] = filter_formula

                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=self.headers, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    all_records.extend(records)

                    offset = data.get('offset')
                    if not offset or len(all_records) >= max_records:
                        break
                else:
                    break

            return all_records[:max_records]
        except Exception:
            return []

    def upload_data(self, dataframe, batch_size=10, mode="append"):
        """Upload data to Airtable with different sync modes"""
        print(f"🔍 DEBUG: upload_data called with {len(dataframe)} rows, mode={mode}")
        print(f"🔍 DEBUG: Headers present: {bool(self.headers)}")
        print(f"🔍 DEBUG: DataFrame empty: {dataframe.empty}")

        if not self.headers or dataframe.empty:
            print("❌ DEBUG: Returning early - no headers or empty dataframe")
            return 0, ["No data to upload"], 0

        url = f"{self.api_url}/{self.base_id}/{self.table_id}"
        print(f"🔍 DEBUG: Upload URL: {url}")

        uploaded_count = 0
        skipped_count = 0
        errors = []

        # Handle different sync modes
        filtered_data = dataframe.copy()

        if mode == "incremental":
            # Get latest date from Airtable to filter new records
            print("🔍 DEBUG: Running incremental mode...")
            latest_date = self.get_latest_date()
            print(f"🔍 DEBUG: Latest date from Airtable: {latest_date}")

            if latest_date and 'Date' in filtered_data.columns:
                print(f"🔍 DEBUG: Original data dates: {filtered_data['Date'].head().tolist()}")
                filtered_data['Date'] = pd.to_datetime(filtered_data['Date'])
                latest_date = pd.to_datetime(latest_date)
                print(f"🔍 DEBUG: Parsed latest date: {latest_date}")
                print(f"🔍 DEBUG: Parsed data dates: {filtered_data['Date'].head().tolist()}")

                new_data = filtered_data[filtered_data['Date'] > latest_date]
                skipped_count = len(filtered_data) - len(new_data)
                print(f"🔍 DEBUG: Original records: {len(filtered_data)}, New records: {len(new_data)}, Skipped: {skipped_count}")
                filtered_data = new_data
            else:
                print(f"🔍 DEBUG: No latest date or Date column not found. Latest date: {latest_date}, Date column exists: {'Date' in filtered_data.columns}")

        # Convert DataFrame to Airtable records format
        for i in range(0, len(filtered_data), batch_size):
            batch = filtered_data.iloc[i:i+batch_size]
            records = []

            for row_idx, row in batch.iterrows():
                fields = {}
                print(f"🔍 DEBUG: Processing row {row_idx}")
                print(f"🔍 DEBUG: Raw row data: {dict(row)}")

                for col, value in row.items():
                    print(f"🔍 DEBUG: Processing field '{col}' = '{value}' (type: {type(value)})")

                    if pd.notna(value):
                        try:
                            if isinstance(value, (pd.Timestamp, datetime)):
                                formatted_value = value.strftime('%Y-%m-%d')
                                fields[col] = formatted_value
                                print(f"✅ DEBUG: Date field '{col}' = '{formatted_value}'")
                            elif isinstance(value, (int, float)):
                                # Ensure numeric values are properly formatted
                                if pd.isna(value) or np.isinf(value):
                                    print(f"⚠️ DEBUG: Skipping invalid numeric value '{col}' = '{value}'")
                                    continue  # Skip invalid numeric values
                                numeric_value = float(value) if isinstance(value, float) else int(value)
                                fields[col] = numeric_value
                                print(f"✅ DEBUG: Numeric field '{col}' = {numeric_value}")
                            else:
                                # Convert to string and clean
                                str_value = str(value).strip()
                                if str_value and str_value.lower() not in ['nan', 'none', 'null']:
                                    fields[col] = str_value
                                    print(f"✅ DEBUG: String field '{col}' = '{str_value}'")
                                else:
                                    print(f"⚠️ DEBUG: Skipping empty/null string '{col}' = '{str_value}'")
                        except Exception as e:
                            print(f"❌ DEBUG: Error processing field '{col}' with value '{value}': {e}")
                            continue
                    else:
                        print(f"⚠️ DEBUG: Skipping null/NaN field '{col}'")

                print(f"🔍 DEBUG: Final fields for row {row_idx}: {fields}")

                if fields:  # Only add record if it has valid fields
                    records.append({"fields": fields})
                    print(f"✅ DEBUG: Added record for row {row_idx} with {len(fields)} fields")
                else:
                    print(f"❌ DEBUG: Skipping empty record at row {row_idx} - no valid fields")

            print(f"🔍 DEBUG: Batch {i//batch_size + 1} summary: {len(records)} records created from {len(batch)} rows")

            if not records:
                print(f"⚠️ DEBUG: No valid records in batch {i//batch_size + 1}, skipping")
                continue

            # Send batch to Airtable
            try:
                payload = {"records": records}
                print(f"🔍 DEBUG: Sending batch {i//batch_size + 1} with {len(records)} records")
                print(f"🔍 DEBUG: Sample record fields: {list(records[0]['fields'].keys()) if records else 'No records'}")

                response = requests.post(url, headers=self.headers, json=payload, timeout=30)

                print(f"🔍 DEBUG: Response status: {response.status_code}")

                if response.status_code == 200:
                    uploaded_count += len(records)
                    print(f"✅ DEBUG: Batch {i//batch_size + 1} uploaded successfully")
                else:
                    # Get detailed error from Airtable API
                    try:
                        error_details = response.json()
                        error_msg = f"Batch {i//batch_size + 1} failed: {response.status_code} - {error_details}"
                        print(f"❌ DEBUG: Airtable API Error: {error_details}")
                    except:
                        error_msg = f"Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}"
                        print(f"❌ DEBUG: Raw error response: {response.text}")

                    errors.append(error_msg)
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} error: {str(e)}"
                print(f"❌ DEBUG: Exception during upload: {str(e)}")
                errors.append(error_msg)

        return uploaded_count, errors, skipped_count

    def get_latest_date(self):
        """Get the most recent date in Airtable"""
        try:
            url = f"{self.api_url}/{self.base_id}/{self.table_id}"
            params = {
                'maxRecords': 1,
                'sort[0][field]': 'Date',
                'sort[0][direction]': 'desc'
            }

            response = requests.get(url, headers=self.headers, params=params, timeout=10)

            if response.status_code == 200:
                records = response.json().get('records', [])
                if records:
                    return records[0]['fields'].get('Date')
            return None
        except Exception:
            return None

    def get_table_schema(self):
        """Get the table schema to check field compatibility"""
        try:
            # Get a sample record to see the field structure
            url = f"{self.api_url}/{self.base_id}/{self.table_id}"
            params = {'maxRecords': 1}

            response = requests.get(url, headers=self.headers, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                records = data.get('records', [])
                if records:
                    fields = records[0].get('fields', {})
                    print(f"🔍 DEBUG: Airtable table fields: {list(fields.keys())}")
                    return list(fields.keys())
                else:
                    print("🔍 DEBUG: No records in Airtable table to check schema")
                    return []
            else:
                print(f"🔍 DEBUG: Failed to get table schema: {response.status_code}")
                return []
        except Exception as e:
            print(f"🔍 DEBUG: Error getting table schema: {e}")
            return []

    def clear_all_records(self):
        """Clear all records from the table"""
        try:
            # Get all record IDs
            all_records = self.get_records(max_records=10000)
            if not all_records:
                return True, "No records to clear"

            record_ids = [record['id'] for record in all_records]

            # Delete in batches of 10 (Airtable limit)
            url = f"{self.api_url}/{self.base_id}/{self.table_id}"
            deleted_count = 0

            for i in range(0, len(record_ids), 10):
                batch_ids = record_ids[i:i+10]
                params = {'records[]': batch_ids}
                response = requests.delete(url, headers=self.headers, params=params, timeout=15)

                if response.status_code == 200:
                    deleted_count += len(batch_ids)
                else:
                    return False, f"Failed to delete batch: {response.status_code}"

            return True, f"Successfully deleted {deleted_count} records"
        except Exception as e:
            return False, f"Error clearing records: {str(e)}"


class RLDashboardCustomizerV3Complete:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("RL Dashboard Customizer v3.0 - Complete Edition")
        self.root.geometry("1000x900")

        # Initialize configuration manager
        self.config_manager = ConfigManager()

        # Initialize client database
        self.init_client_database()

        # Configuration variables
        self.template_path = tk.StringVar(value="./base")  # Use base folder as default
        self.output_path = tk.StringVar()
        self.client_name = tk.StringVar()
        self.business_name = tk.StringVar()
        self.store_name = tk.StringVar()  # New: Store name for header
        self.base_id = tk.StringVar()

        # Hardcoded API key for convenience
        self.airtable_api_key = "**********************************************************************************"

        # Available bases and tables
        self.available_bases = {}
        self.available_tables = {}
        
        # Table ID variables
        self.ghl_table_id = tk.StringVar()
        self.google_ads_table_id = tk.StringVar()
        self.pos_table_id = tk.StringVar()
        self.meta_ads_table_id = tk.StringVar()
        
        # Data source checkboxes
        self.enable_ghl = tk.BooleanVar(value=True)
        self.enable_google_ads = tk.BooleanVar(value=True)
        self.enable_pos = tk.BooleanVar(value=False)
        self.enable_meta_ads = tk.BooleanVar(value=False)

        # Google Ads location configuration
        self.google_ads_locations = tk.StringVar(value="Location 1, Location 2, Location 3")
        self.location_icons = tk.StringVar(value="🔧, 🛠️, ⚙️")  # Repair shop icons
        self.location_colors = tk.StringVar(value="#4CAF50, #2196F3, #FF9800")

        # Location detection specific Airtable configuration
        self.location_base_id = tk.StringVar()
        self.location_available_bases = {}
        self.location_available_tables = {}
        self.location_ghl_table_id = tk.StringVar()
        self.location_google_ads_table_id = tk.StringVar()
        
        # Verification results
        self.verification_results = {}

        # Load saved settings
        self.load_settings()

        # Set up auto-save on window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.setup_ui()
        logger.info("🚀 RL Dashboard Customizer v3.0 Complete initialized")
        
    def setup_ui(self):
        """Setup the complete user interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Client Configuration
        self.setup_client_config_tab(notebook)
        
        # Tab 2: Data Sources
        self.setup_data_sources_tab(notebook)
        
        # Tab 3: Airtable Configuration
        self.setup_airtable_tab(notebook)

        # Tab 4: Google Ads Data Hub
        self.setup_google_ads_hub_tab(notebook)

        # Tab 5: Generation & Verification
        self.setup_generation_tab(notebook)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select template and configure client settings")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def setup_client_config_tab(self, notebook):
        """Setup client configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📋 Client Configuration")
        
        # Title
        title_label = ttk.Label(frame, text="Client Configuration", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Template selection
        template_frame = ttk.LabelFrame(frame, text="Template Selection", padding=10)
        template_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(template_frame, text="Base Template:").grid(row=0, column=0, sticky=tk.W, pady=2)
        template_entry = ttk.Entry(template_frame, textvariable=self.template_path, width=50)
        template_entry.grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Button(template_frame, text="Browse", 
                  command=self.browse_template).grid(row=0, column=2, padx=5, pady=2)
        
        ttk.Button(template_frame, text="Verify Template", 
                  command=self.verify_template).grid(row=0, column=3, padx=5, pady=2)
        
        # Client information
        client_frame = ttk.LabelFrame(frame, text="Client Information", padding=10)
        client_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(client_frame, text="Client Name:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(client_frame, textvariable=self.client_name, width=30).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(client_frame, text="Business Name:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(client_frame, textvariable=self.business_name, width=30).grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(client_frame, text="Store Name (Header):").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(client_frame, textvariable=self.store_name, width=30).grid(row=2, column=1, padx=5, pady=2)
        
        # Output directory
        output_frame = ttk.LabelFrame(frame, text="Output Configuration", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, pady=2)
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path, width=50)
        output_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Button(output_frame, text="Browse",
                  command=self.browse_output).grid(row=0, column=2, padx=5, pady=2)

        # Airtable Connection for Location Detection
        airtable_connection_frame = ttk.LabelFrame(frame, text="Airtable Connection", padding=10)
        airtable_connection_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(airtable_connection_frame, text="Airtable API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(airtable_connection_frame, text="✅ Hardcoded (patOAJfPfi9To83yR...)",
                 foreground="green").grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Button(airtable_connection_frame, text="🔍 Load Available Bases",
                  command=self.load_available_bases_for_locations,
                  style="Accent.TButton").grid(row=0, column=2, padx=5, pady=2)

        ttk.Label(airtable_connection_frame, text="Select Base:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.base_combobox_locations = ttk.Combobox(airtable_connection_frame, width=60, state="readonly")
        self.base_combobox_locations.grid(row=1, column=1, padx=5, pady=2)
        self.base_combobox_locations.bind('<<ComboboxSelected>>', self.on_base_selected_for_locations)

        ttk.Button(airtable_connection_frame, text="🔍 Load Tables",
                  command=self.load_base_tables_for_locations).grid(row=1, column=2, padx=5, pady=2)

        # Connection status
        self.location_connection_status = tk.StringVar(value="❌ Not Connected")
        ttk.Label(airtable_connection_frame, textvariable=self.location_connection_status,
                 foreground="red").grid(row=2, column=0, columnspan=3, pady=5)

        # Google Ads Location Configuration
        location_frame = ttk.LabelFrame(frame, text="Google Ads Location Configuration", padding=10)
        location_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(location_frame, text="Location Names:").grid(row=0, column=0, sticky=tk.W, pady=2)
        location_entry = ttk.Entry(location_frame, textvariable=self.google_ads_locations, width=50)
        location_entry.grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(location_frame, text="(comma-separated)", foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)

        ttk.Label(location_frame, text="Location Icons:").grid(row=1, column=0, sticky=tk.W, pady=2)
        icons_entry = ttk.Entry(location_frame, textvariable=self.location_icons, width=50)
        icons_entry.grid(row=1, column=1, padx=5, pady=2)
        ttk.Label(location_frame, text="(emoji, comma-separated)", foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)

        ttk.Label(location_frame, text="Location Colors:").grid(row=2, column=0, sticky=tk.W, pady=2)
        colors_entry = ttk.Entry(location_frame, textvariable=self.location_colors, width=50)
        colors_entry.grid(row=2, column=1, padx=5, pady=2)
        ttk.Label(location_frame, text="(hex codes, comma-separated)", foreground="gray").grid(row=2, column=2, sticky=tk.W, padx=5)

        # Location setup tools
        setup_frame = ttk.Frame(location_frame)
        setup_frame.grid(row=3, column=0, columnspan=3, pady=10)

        # Number of locations setup
        ttk.Label(setup_frame, text="Quick Setup:").pack(side=tk.LEFT, padx=5)
        for i in range(1, 11):  # 1 to 10 locations
            ttk.Button(setup_frame, text=f"{i} Location{'s' if i > 1 else ''}",
                      command=lambda n=i: self.setup_n_locations(n), width=10).pack(side=tk.LEFT, padx=2)

        # Instructions
        instructions_frame = ttk.Frame(location_frame)
        instructions_frame.grid(row=4, column=0, columnspan=3, pady=5)
        ttk.Label(instructions_frame, text="💡 First connect to Airtable above, then use auto-detection or manual setup below",
                 foreground="blue", font=("Arial", 9, "italic")).pack()

        # Airtable auto-detection
        airtable_frame = ttk.Frame(location_frame)
        airtable_frame.grid(row=5, column=0, columnspan=3, pady=10)

        ttk.Button(airtable_frame, text="🔍 Auto-Detect from GHL Location Column",
                  command=self.auto_detect_from_ghl, style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(airtable_frame, text="🔍 Auto-Detect from Google Ads Campaign Names",
                  command=self.auto_detect_from_google_ads, style="Accent.TButton").pack(side=tk.LEFT, padx=5)

        # Auto-populate store name when client name changes
        self.client_name.trace('w', self.auto_populate_names)
        
    def setup_data_sources_tab(self, notebook):
        """Setup data sources configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📊 Data Sources")
        
        # Title
        title_label = ttk.Label(frame, text="Data Sources Configuration", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Data sources frame
        sources_frame = ttk.LabelFrame(frame, text="Enable/Disable Data Sources", padding=10)
        sources_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # GHL (always enabled for most clients)
        ghl_frame = ttk.Frame(sources_frame)
        ghl_frame.pack(fill=tk.X, pady=2)
        ttk.Checkbutton(ghl_frame, text="GHL (GoHighLevel) - Lead Data", 
                       variable=self.enable_ghl).pack(side=tk.LEFT)
        ttk.Label(ghl_frame, text="✅ Recommended for all clients", 
                 foreground="green").pack(side=tk.LEFT, padx=10)
        
        # Google Ads
        gads_frame = ttk.Frame(sources_frame)
        gads_frame.pack(fill=tk.X, pady=2)
        ttk.Checkbutton(gads_frame, text="Google Ads - Campaign Data", 
                       variable=self.enable_google_ads).pack(side=tk.LEFT)
        ttk.Label(gads_frame, text="✅ Recommended for most clients", 
                 foreground="green").pack(side=tk.LEFT, padx=10)
        
        # POS
        pos_frame = ttk.Frame(sources_frame)
        pos_frame.pack(fill=tk.X, pady=2)
        ttk.Checkbutton(pos_frame, text="POS (Point of Sale) - Sales Data", 
                       variable=self.enable_pos).pack(side=tk.LEFT)
        ttk.Label(pos_frame, text="⚠️ Rarely used", 
                 foreground="orange").pack(side=tk.LEFT, padx=10)
        
        # Meta Ads
        meta_frame = ttk.Frame(sources_frame)
        meta_frame.pack(fill=tk.X, pady=2)
        ttk.Checkbutton(meta_frame, text="Meta Ads (Facebook/Instagram) - Ad Data", 
                       variable=self.enable_meta_ads).pack(side=tk.LEFT)
        ttk.Label(meta_frame, text="⚠️ Rarely used", 
                 foreground="orange").pack(side=tk.LEFT, padx=10)
        
        # Information panel
        info_frame = ttk.LabelFrame(frame, text="Data Source Information", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        info_text = scrolledtext.ScrolledText(info_frame, height=10, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """
📊 DATA SOURCE RECOMMENDATIONS:

✅ GHL (GoHighLevel):
   • Primary lead tracking system
   • Essential for all clients
   • Provides lead volume, sources, and conversion data

✅ Google Ads:
   • Most common advertising platform
   • Recommended for 90% of clients
   • Provides campaign performance and cost data

⚠️ POS (Point of Sale):
   • Only for clients with integrated POS systems
   • Provides sales transaction data
   • Rarely used - most clients don't have POS integration

⚠️ Meta Ads (Facebook/Instagram):
   • Only for clients actively using Facebook/Instagram ads
   • Provides social media advertising data
   • Less common than Google Ads

💡 TYPICAL CLIENT CONFIGURATION:
   • Enable: GHL + Google Ads
   • Disable: POS + Meta Ads

This provides a clean, focused dashboard with the most relevant data sources.

🗺️ NEW: GOOGLE ADS LOCATION ANALYTICS:
   • Automatically extracts location data from Google Ads campaign names
   • Creates beautiful location-based performance cards for repair shops
   • Auto-detects locations from GHL data or Google Ads campaigns
   • Responsive design with repair shop appropriate icons
   • Example: "RSA Repair | Daphne #15" → Shows Daphne performance card
        """
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
    def auto_populate_names(self, *args):
        """Auto-populate business and store names when client name changes"""
        client_name = self.client_name.get()
        if client_name and not self.business_name.get():
            self.business_name.set(client_name)
        if client_name and not self.store_name.get():
            self.store_name.set(client_name)

    def parse_notes_for_client_info(self, notes_text):
        """
        Parse notes text to extract business name and customer ID

        Expected format:
        Business Name
        Repair Lift
        XXX-XXX-XXXX

        Examples:
        - "we fix wireless\nRepair Lift\n950-622-0400"
        - "Cell Phone Fix\nRepair Lift\n703-049-0350"
        - "Colorado Gadget Fix\nRepair Lift\n847-763-5663"

        Returns:
        - business_name: First line, properly capitalized
        - customer_id: Phone number (XXX-XXX-XXXX format)
        """
        if not notes_text or not notes_text.strip():
            return None, None

        try:
            # Split by various line break patterns
            lines = []
            for line in notes_text.replace('\r\n', '\n').replace('\r', '\n').split('\n'):
                line = line.strip()
                if line:  # Only add non-empty lines
                    lines.append(line)

            if len(lines) < 2:
                return None, None

            # Extract business name (first line)
            business_name = lines[0].strip()

            # Capitalize first letter of each word if not already capitalized
            if business_name:
                # Split by spaces and capitalize each word
                words = business_name.split()
                capitalized_words = []
                for word in words:
                    if word and not word[0].isupper():
                        capitalized_words.append(word.capitalize())
                    else:
                        capitalized_words.append(word)
                business_name = ' '.join(capitalized_words)

            # Extract customer ID (look for phone number pattern)
            customer_id = None
            phone_pattern = r'\b\d{3}-\d{3}-\d{4}\b'  # XXX-XXX-XXXX format

            for line in lines:
                import re
                match = re.search(phone_pattern, line)
                if match:
                    customer_id = match.group()
                    break

            return business_name, customer_id

        except Exception as e:
            self.log_message(f"❌ Error parsing notes: {str(e)}")
            return None, None

    def on_notes_changed(self, *args):
        """Handle notes field changes and auto-populate business name and customer ID"""
        if not hasattr(self, 'notes_var'):
            return

        notes_text = self.notes_var.get()
        if not notes_text:
            return

        business_name, customer_id = self.parse_notes_for_client_info(notes_text)

        # Auto-populate business name if empty and we found one
        if business_name and hasattr(self, 'business_name') and not self.business_name.get():
            self.business_name.set(business_name)
            self.log_message(f"✅ Auto-filled business name: {business_name}")

        # Auto-populate customer ID if empty and we found one
        if customer_id and hasattr(self, 'customer_id') and not self.customer_id.get():
            self.customer_id.set(customer_id)
            self.log_message(f"✅ Auto-filled customer ID: {customer_id}")

        # Also auto-populate store name if empty
        if business_name and hasattr(self, 'store_name') and not self.store_name.get():
            self.store_name.set(business_name)
            self.log_message(f"✅ Auto-filled store name: {business_name}")

    def setup_n_locations(self, n):
        """Setup configuration for N number of locations"""
        # Generate generic location names
        location_names = [f"Location {i+1}" for i in range(n)]

        # Use a variety of repair shop appropriate icons
        repair_icons = ["🔧", "🛠️", "⚙️", "🔩", "🪛", "🧰", "⚡", "📱", "💻", "🖥️"]
        icons = [repair_icons[i % len(repair_icons)] for i in range(n)]

        # Use a variety of colors
        colors = ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#F44336",
                 "#00BCD4", "#795548", "#607D8B", "#E91E63", "#3F51B5"]
        location_colors = [colors[i % len(colors)] for i in range(n)]

        # Set the configuration
        self.google_ads_locations.set(", ".join(location_names))
        self.location_icons.set(", ".join(icons))
        self.location_colors.set(", ".join(location_colors))

        messagebox.showinfo("Quick Setup", f"✅ Configuration set up for {n} location{'s' if n > 1 else ''}!\n\nPlease update the location names manually.")

    def auto_detect_from_ghl(self):
        """Auto-detect locations from GHL table location column"""
        if not self.location_base_id.get():
            messagebox.showerror("Error", "Please connect to Airtable and select a base first")
            return

        if not self.location_ghl_table_id.get():
            messagebox.showerror("Error", "No GHL table detected. Please load tables first.")
            return

        self.log_message("🔍 Auto-detecting locations from GHL table...")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Get records from GHL table
            url = f'https://api.airtable.com/v0/{self.location_base_id.get()}/{self.location_ghl_table_id.get()}'
            response = requests.get(url, headers=headers, params={'maxRecords': 100}, timeout=15)

            if response.status_code == 200:
                records = response.json().get('records', [])

                # Extract unique locations
                locations = set()
                location_fields = ['Location', 'location', 'LOCATION', 'store', 'Store', 'branch', 'Branch']

                for record in records:
                    fields = record.get('fields', {})
                    for field_name in location_fields:
                        if field_name in fields and fields[field_name]:
                            location_value = str(fields[field_name]).strip()
                            if location_value and len(location_value) > 1:
                                locations.add(location_value)

                if locations:
                    location_list = sorted(list(locations))
                    self._apply_detected_locations(location_list, "GHL table")
                else:
                    messagebox.showwarning("No Locations Found",
                                         "No locations found in GHL table. Check if the Location column exists and has data.")
                    self.log_message("⚠️ No locations found in GHL table")
            else:
                error_msg = f"Failed to fetch GHL data: {response.status_code}"
                messagebox.showerror("Error", error_msg)
                self.log_message(f"❌ {error_msg}")

        except Exception as e:
            error_msg = f"Error detecting locations from GHL: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.log_message(f"❌ {error_msg}")

    def auto_detect_from_google_ads(self):
        """Auto-detect locations from Google Ads campaign names"""
        if not self.location_base_id.get():
            messagebox.showerror("Error", "Please connect to Airtable and select a base first")
            return

        if not self.location_google_ads_table_id.get():
            messagebox.showerror("Error", "No Google Ads table detected. Please load tables first.")
            return

        self.log_message("🔍 Auto-detecting locations from Google Ads campaign names...")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Get records from Google Ads table
            url = f'https://api.airtable.com/v0/{self.location_base_id.get()}/{self.location_google_ads_table_id.get()}'
            response = requests.get(url, headers=headers, params={'maxRecords': 200}, timeout=15)

            if response.status_code == 200:
                records = response.json().get('records', [])

                # Extract campaign names and parse locations
                campaign_names = set()
                campaign_fields = ['Campaign Name', 'campaign_name', 'Campaign', 'campaign', 'CAMPAIGN']

                for record in records:
                    fields = record.get('fields', {})
                    for field_name in campaign_fields:
                        if field_name in fields and fields[field_name]:
                            campaign_name = str(fields[field_name]).strip()
                            if campaign_name:
                                campaign_names.add(campaign_name)

                if campaign_names:
                    # Parse locations from campaign names
                    detected_locations = self._parse_locations_from_campaigns(list(campaign_names))

                    if detected_locations:
                        self._apply_detected_locations(detected_locations, "Google Ads campaign names")
                    else:
                        messagebox.showwarning("No Locations Found",
                                             "No clear location patterns found in Google Ads campaign names.\n\nTry manual configuration or check campaign naming patterns.")
                        self.log_message("⚠️ No location patterns found in campaign names")
                else:
                    messagebox.showwarning("No Campaign Data",
                                         "No campaign names found in Google Ads table.")
                    self.log_message("⚠️ No campaign names found")
            else:
                error_msg = f"Failed to fetch Google Ads data: {response.status_code}"
                messagebox.showerror("Error", error_msg)
                self.log_message(f"❌ {error_msg}")

        except Exception as e:
            error_msg = f"Error detecting locations from Google Ads: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.log_message(f"❌ {error_msg}")

    def _parse_locations_from_campaigns(self, campaign_names):
        """Parse potential locations from campaign names"""
        # Common patterns in campaign names
        potential_locations = set()

        for campaign in campaign_names:
            # Split by common delimiters
            parts = re.split(r'[|#\-_\s]+', campaign)

            for part in parts:
                part = part.strip()
                # Look for location-like patterns
                if (len(part) >= 3 and
                    part.isalpha() and
                    part[0].isupper() and
                    part.lower() not in ['rsa', 'pmax', 'buybacks', 'device', 'sale', 'repair', 'workaround', 'campaign']):
                    potential_locations.add(part)

        # Filter out obvious non-locations
        filtered_locations = []
        for location in potential_locations:
            if (len(location) >= 3 and
                location.lower() not in ['the', 'and', 'for', 'with', 'from', 'this', 'that']):
                filtered_locations.append(location)

        return sorted(filtered_locations)

    def _apply_detected_locations(self, locations, source):
        """Apply detected locations to the configuration"""
        n = len(locations)

        # Use repair shop appropriate icons
        repair_icons = ["🔧", "🛠️", "⚙️", "🔩", "🪛", "🧰", "⚡", "📱", "💻", "🖥️"]
        icons = [repair_icons[i % len(repair_icons)] for i in range(n)]

        # Use a variety of colors
        colors = ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#F44336",
                 "#00BCD4", "#795548", "#607D8B", "#E91E63", "#3F51B5"]
        location_colors = [colors[i % len(colors)] for i in range(n)]

        # Set the configuration
        self.google_ads_locations.set(", ".join(locations))
        self.location_icons.set(", ".join(icons))
        self.location_colors.set(", ".join(location_colors))

        self.log_message(f"✅ Auto-detected {n} locations from {source}: {', '.join(locations)}")
        messagebox.showinfo("Auto-Detection Success",
                          f"✅ Found {n} location{'s' if n > 1 else ''} from {source}:\n\n{', '.join(locations)}\n\nPlease review and adjust if needed.")

    def load_available_bases_for_locations(self):
        """Load available Airtable bases for location detection with pagination support"""
        self.log_message("🔍 Loading available Airtable bases for location detection...")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            all_bases = []
            offset = None
            max_bases = 100  # Fetch up to 100 bases

            while len(all_bases) < max_bases:
                # Build URL with pagination
                url = 'https://api.airtable.com/v0/meta/bases'
                params = {}
                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=headers, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    bases = data.get('bases', [])

                    if not bases:
                        break

                    all_bases.extend(bases)

                    # Check if there are more bases to fetch
                    offset = data.get('offset')
                    if not offset:
                        break

                    # Limit to max_bases
                    if len(all_bases) >= max_bases:
                        all_bases = all_bases[:max_bases]
                        break

                else:
                    error_msg = f"❌ Failed to load bases: {response.status_code} - {response.text}"
                    self.log_message(error_msg)
                    messagebox.showerror("Error", error_msg)
                    return

            self.location_available_bases = {f"{base['name']} ({base['id']})": base['id'] for base in all_bases}

            # Update combobox
            base_options = list(self.location_available_bases.keys())
            self.base_combobox_locations['values'] = base_options

            self.log_message(f"✅ Loaded {len(all_bases)} available bases for location detection")
            messagebox.showinfo("Success", f"✅ Loaded {len(all_bases)} available bases")

        except Exception as e:
            error_msg = f"❌ Error loading bases: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def on_base_selected_for_locations(self, event=None):
        """Handle base selection for location detection"""
        selected_base = self.base_combobox_locations.get()
        if selected_base and selected_base in self.location_available_bases:
            base_id = self.location_available_bases[selected_base]
            self.location_base_id.set(base_id)
            self.log_message(f"📋 Selected base for location detection: {selected_base}")

            # Auto-load tables for selected base
            self.load_base_tables_for_locations()

    def load_base_tables_for_locations(self):
        """Load tables for the selected base for location detection"""
        base_id = self.location_base_id.get()
        if not base_id:
            messagebox.showerror("Error", "Please select a base first")
            return

        self.log_message(f"🔍 Loading tables for location detection from base: {base_id}")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Get base schema
            url = f'https://api.airtable.com/v0/meta/bases/{base_id}/tables'
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                tables = response.json().get('tables', [])
                self.location_available_tables = {f"{table['name']} ({table['id']})": table['id'] for table in tables}

                self.log_message(f"✅ Loaded {len(tables)} tables for location detection")

                # Show table info
                table_info = "\\n".join([f"• {table['name']} (ID: {table['id']})" for table in tables])
                self.log_message(f"📋 Available tables for location detection:\\n{table_info}")

                # Auto-detect table IDs for location detection
                self.auto_detect_table_ids_for_locations(tables)

            else:
                error_msg = f"❌ Failed to load tables: {response.status_code} - {response.text}"
                self.log_message(error_msg)
                messagebox.showerror("Error", error_msg)

        except Exception as e:
            error_msg = f"❌ Error loading tables: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def auto_detect_table_ids_for_locations(self, tables):
        """Auto-detect table IDs for location detection"""
        self.log_message("🤖 Auto-detecting table IDs for location detection...")

        # Define detection patterns
        detection_patterns = {
            'ghl': ['ghl', 'gohighlevel', 'leads', 'lead'],
            'googleAds': ['google ads', 'google_ads', 'googleads', 'ads', 'adwords']
        }

        detected = {}

        for table in tables:
            table_name = table['name'].lower()
            table_id = table['id']

            for source, patterns in detection_patterns.items():
                for pattern in patterns:
                    if pattern in table_name:
                        if source not in detected:  # Take first match
                            detected[source] = table_id
                            break

        # Store detected table IDs
        if 'ghl' in detected:
            self.location_ghl_table_id.set(detected['ghl'])
            self.log_message(f"🎯 Auto-detected GHL table for location detection: {detected['ghl']}")

        if 'googleAds' in detected:
            self.location_google_ads_table_id.set(detected['googleAds'])
            self.log_message(f"🎯 Auto-detected Google Ads table for location detection: {detected['googleAds']}")

        if detected:
            self.log_message(f"✅ Auto-detection for location detection complete! Found {len(detected)} matches")
            self.location_connection_status.set("✅ Connected - Ready for auto-detection")
            # Update status label color to green
            for widget in self.root.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'winfo_children'):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Label) and grandchild.cget('textvariable') == str(self.location_connection_status):
                                    grandchild.configure(foreground="green")
            messagebox.showinfo("Auto-Detection Complete",
                              f"✅ Auto-detected {len(detected)} table IDs for location detection!\\n\\nReady to auto-detect locations.")
        else:
            self.log_message("⚠️ No tables auto-detected for location detection.")
            self.location_connection_status.set("⚠️ Connected but no tables detected")
            messagebox.showinfo("Auto-Detection",
                              "⚠️ No tables were auto-detected.\\n\\nPlease use the main Airtable Setup tab to configure tables first.")

    def _parse_location_configuration(self):
        """Parse the Google Ads location configuration from GUI inputs"""
        try:
            # Check if StringVar objects exist
            if not hasattr(self, 'google_ads_locations') or not hasattr(self, 'location_icons') or not hasattr(self, 'location_colors'):
                self.log_message("⚠️ Location configuration variables not initialized, using defaults")
                return {
                    'names': ['Location 1', 'Location 2', 'Location 3'],
                    'icons': {'Location 1': '🔧', 'Location 2': '🛠️', 'Location 3': '⚙️'},
                    'colors': {'Location 1': '#4CAF50', 'Location 2': '#2196F3', 'Location 3': '#FF9800'}
                }

            # Parse location names
            location_names = [name.strip() for name in self.google_ads_locations.get().split(',') if name.strip()]

            # Parse icons
            icons_list = [icon.strip() for icon in self.location_icons.get().split(',') if icon.strip()]

            # Parse colors
            colors_list = [color.strip() for color in self.location_colors.get().split(',') if color.strip()]

            # Log what we're parsing
            self.log_message(f"📋 Parsing location config - Names: {location_names}")
            self.log_message(f"📋 Parsing location config - Icons: {icons_list}")
            self.log_message(f"📋 Parsing location config - Colors: {colors_list}")
        except Exception as e:
            self.log_message(f"❌ Error in location configuration parsing: {str(e)}")
            raise

        # Create icons dictionary
        icons_dict = {}
        for i, location in enumerate(location_names):
            if i < len(icons_list):
                icons_dict[location] = icons_list[i]
            else:
                icons_dict[location] = '📍'  # Default icon

        # Create colors dictionary
        colors_dict = {}
        for i, location in enumerate(location_names):
            if i < len(colors_list):
                colors_dict[location] = colors_list[i]
            else:
                colors_dict[location] = '#6c757d'  # Default color

        return {
            'names': location_names,
            'icons': icons_dict,
            'colors': colors_dict
        }
            
    def browse_template(self):
        """Browse for template directory"""
        directory = filedialog.askdirectory(title="Select Template Directory")
        if directory:
            self.template_path.set(directory)
            
    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_path.set(directory)
            
    def verify_template(self):
        """Verify the selected template"""
        template_path = Path(self.template_path.get())
        
        if not template_path.exists():
            messagebox.showerror("Error", f"Template directory not found: {template_path}")
            return
            
        required_files = [
            'client-config.js',
            'index.html', 
            'script.js',
            'server.py',
            'styles.css'
        ]
        
        missing_files = []
        for file in required_files:
            if not (template_path / file).exists():
                missing_files.append(file)
                
        if missing_files:
            messagebox.showerror("Error", f"Missing required files: {missing_files}")
        else:
            messagebox.showinfo("Success", "✅ Template verification passed!")
            self.status_var.set("✅ Template verified successfully")

    def setup_airtable_tab(self, notebook):
        """Setup Airtable configuration tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🗄️ Airtable Setup")

        # Title
        title_label = ttk.Label(frame, text="Airtable Configuration", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # API Configuration
        api_frame = ttk.LabelFrame(frame, text="API Configuration", padding=10)
        api_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(api_frame, text="Airtable API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(api_frame, text="✅ Hardcoded (patOAJfPfi9To83yR...)",
                 foreground="green").grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Button(api_frame, text="🔍 Load Available Bases",
                  command=self.load_available_bases,
                  style="Accent.TButton").grid(row=0, column=2, padx=5, pady=2)

        # Base Selection
        base_frame = ttk.LabelFrame(frame, text="Base Selection", padding=10)
        base_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(base_frame, text="Select Base:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.base_combobox = ttk.Combobox(base_frame, width=60, state="readonly")
        self.base_combobox.grid(row=0, column=1, padx=5, pady=2)
        self.base_combobox.bind('<<ComboboxSelected>>', self.on_base_selected)

        ttk.Button(base_frame, text="🔍 Load Tables",
                  command=self.load_base_tables).grid(row=0, column=2, padx=5, pady=2)

        # Table IDs with Auto-Detection
        tables_frame = ttk.LabelFrame(frame, text="Table IDs (Auto-Detected)", padding=10)
        tables_frame.pack(fill=tk.X, padx=10, pady=5)

        # GHL Table
        ttk.Label(tables_frame, text="GHL Table:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.ghl_combobox = ttk.Combobox(tables_frame, textvariable=self.ghl_table_id, width=40, state="readonly")
        self.ghl_combobox.grid(row=0, column=1, padx=5, pady=2)

        # Google Ads Table
        ttk.Label(tables_frame, text="Google Ads Table:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.google_ads_combobox = ttk.Combobox(tables_frame, textvariable=self.google_ads_table_id, width=40, state="readonly")
        self.google_ads_combobox.grid(row=1, column=1, padx=5, pady=2)

        # POS Table
        ttk.Label(tables_frame, text="POS Table:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.pos_combobox = ttk.Combobox(tables_frame, textvariable=self.pos_table_id, width=40, state="readonly")
        self.pos_combobox.grid(row=2, column=1, padx=5, pady=2)

        # Meta Ads Table
        ttk.Label(tables_frame, text="Meta Ads Table:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.meta_ads_combobox = ttk.Combobox(tables_frame, textvariable=self.meta_ads_table_id, width=40, state="readonly")
        self.meta_ads_combobox.grid(row=3, column=1, padx=5, pady=2)

        # Auto-detect button
        ttk.Button(tables_frame, text="🤖 Auto-Detect Table IDs",
                  command=self.auto_detect_table_ids,
                  style="Accent.TButton").grid(row=4, column=0, columnspan=2, pady=10)

        # Validation info
        validation_frame = ttk.LabelFrame(frame, text="Validation Information", padding=10)
        validation_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        validation_text = scrolledtext.ScrolledText(validation_frame, height=8, wrap=tk.WORD)
        validation_text.pack(fill=tk.BOTH, expand=True)

        validation_content = """
🔍 ENHANCED AIRTABLE CONFIGURATION:

🚀 NEW FEATURES:
   • ✅ API Key is hardcoded for convenience
   • ✅ Auto-load available bases from your account
   • ✅ Auto-detect table IDs based on naming patterns
   • ✅ Dropdown selection for easy configuration

📋 WORKFLOW:
   1. Click "Load Available Bases" to see all your bases
   2. Select the base you want to use
   3. Tables will auto-load and auto-detect
   4. Adjust table selections if needed
   5. Generate dashboard!

🤖 AUTO-DETECTION PATTERNS:
   • GHL: Looks for "ghl", "leads", "gohighlevel"
   • Google Ads: Looks for "google ads", "ads", "adwords"
   • POS: Looks for "pos", "sales", "transactions"
   • Meta Ads: Looks for "meta ads", "facebook ads", "fb ads"

💡 TIPS:
   • Auto-detection works best with descriptive table names
   • You can manually select from dropdowns if auto-detection misses
   • Only select tables for data sources you want to enable
        """

        validation_text.insert(tk.END, validation_content)
        validation_text.config(state=tk.DISABLED)

    def setup_google_ads_hub_tab(self, notebook):
        """Setup Google Ads Data Hub tab with enhanced functionality and better UX"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🎯 Google Ads Hub")

        # Create main horizontal layout: Left panel (controls) + Right panel (activity log)
        main_paned = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # === LEFT PANEL: Controls and Configuration ===
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=3)  # Give more weight to left panel

        # Create main scrollable container like other tabs
        main_container = ttk.Frame(left_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(main_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        left_scrollable = ttk.Frame(canvas)

        # Configure scrolling
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # Also update the canvas window width to match the canvas width
            canvas_width = canvas.winfo_width()
            if canvas_width > 1:  # Only update if canvas has been drawn
                canvas.itemconfig(canvas_window, width=canvas_width)

        left_scrollable.bind("<Configure>", configure_scroll_region)

        canvas_window = canvas.create_window((0, 0), window=left_scrollable, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Bind canvas resize to update the scroll region
        canvas.bind("<Configure>", configure_scroll_region)

        # Make canvas focusable and ensure it gets focus for mouse wheel events
        canvas.configure(highlightthickness=0)
        canvas.focus_set()

        # Simple and reliable mouse wheel scrolling support
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mouse wheel to the main container and canvas
        main_container.bind("<MouseWheel>", on_mousewheel)
        canvas.bind("<MouseWheel>", on_mousewheel)
        left_scrollable.bind("<MouseWheel>", on_mousewheel)

        # Function to bind mouse wheel to all widgets recursively
        def bind_all_widgets(widget):
            widget.bind("<MouseWheel>", on_mousewheel)
            for child in widget.winfo_children():
                bind_all_widgets(child)

        # Bind to all widgets after they're created
        def setup_mousewheel():
            bind_all_widgets(left_scrollable)
            # Also ensure the canvas gets focus when mouse enters
            canvas.bind("<Enter>", lambda e: canvas.focus_set())

        # Store for later use
        self.setup_gads_mousewheel = setup_mousewheel

        # Pack canvas and scrollbar properly
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Title and description in left panel with better styling
        title_frame = ttk.Frame(left_scrollable)
        title_frame.pack(fill=tk.X, padx=12, pady=(12, 15))

        # Add a subtle separator line
        separator = ttk.Separator(title_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(title_frame, text="🎯 Google Ads Data Hub",
                               font=("Segoe UI", 13, "bold"), foreground="#2c3e50")
        title_label.pack(anchor=tk.W)

        desc_label = ttk.Label(title_frame,
                              text="Extract, validate, and sync Google Ads data to Airtable",
                              font=("Segoe UI", 9), foreground="#7f8c8d")
        desc_label.pack(anchor=tk.W, pady=(3, 8))

        # Add another separator
        separator2 = ttk.Separator(title_frame, orient='horizontal')
        separator2.pack(fill=tk.X, pady=(5, 0))

        # === STEP 1: GOOGLE ADS CREDENTIALS ===
        step1_frame = ttk.LabelFrame(left_scrollable, text="🔐 Step 1: Google Ads API Credentials",
                                    padding=15)
        step1_frame.pack(fill=tk.X, padx=12, pady=(8, 10))

        # Info label
        info_label = ttk.Label(step1_frame,
                              text="Configure Google Ads API authentication credentials",
                              font=("Segoe UI", 9), foreground="#7f8c8d")
        info_label.pack(anchor=tk.W, pady=(0, 10))

        # API credentials in a grid layout
        creds_frame = ttk.Frame(step1_frame)
        creds_frame.pack(fill=tk.X, pady=(0, 10))

        # Customer ID
        customer_label_frame = ttk.Frame(creds_frame)
        customer_label_frame.grid(row=0, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 8))

        ttk.Label(customer_label_frame, text="Customer ID:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        ttk.Label(customer_label_frame, text="(Load from database or View Accounts)",
                 font=("Segoe UI", 8), foreground="#666666").pack(anchor=tk.W)

        # Customer ID entry and load button frame
        customer_input_frame = ttk.Frame(creds_frame)
        customer_input_frame.grid(row=0, column=1, sticky=tk.EW, padx=(0, 0), pady=(0, 8))

        self.gads_customer_id = tk.StringVar()
        customer_entry = ttk.Entry(customer_input_frame, textvariable=self.gads_customer_id,
                                  width=28, font=("Segoe UI", 9))
        customer_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Load Customer button
        ttk.Button(customer_input_frame, text="👥 Load Customer", width=15,
                  command=self.load_customer_from_database).pack(side=tk.LEFT, padx=(5, 0))

        # Developer Token
        ttk.Label(creds_frame, text="Developer Token:", font=("Segoe UI", 9, "bold")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 8))
        self.gads_developer_token = tk.StringVar(value="bGBR3u83ozZjmx1WXckSkQ")
        dev_token_entry = ttk.Entry(creds_frame, textvariable=self.gads_developer_token,
                                   width=35, show="*", font=("Segoe UI", 9))
        dev_token_entry.grid(row=1, column=1, sticky=tk.EW, padx=(0, 0), pady=(0, 8))

        # Refresh Token (populated by OAuth)
        ttk.Label(creds_frame, text="Refresh Token:", font=("Segoe UI", 9, "bold")).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 8))
        self.gads_refresh_token = tk.StringVar(value="1//01NsefTx-HCBvCgYIARAAGAESNwF-L9IrG7rF3r0GzraiGQPO-cBWzppPxA0pggmi8KtWhZqpSZBDUAG3J_qbKx1xWo06hY9GaSc")
        refresh_token_entry = ttk.Entry(creds_frame, textvariable=self.gads_refresh_token,
                                       width=35, show="*", font=("Segoe UI", 9))
        refresh_token_entry.grid(row=2, column=1, sticky=tk.EW, padx=(0, 0), pady=(0, 8))

        # Manager Account ID
        ttk.Label(creds_frame, text="Manager Account:", font=("Segoe UI", 9, "bold")).grid(
            row=3, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 8))
        self.gads_manager_account = tk.StringVar(value="**********")
        manager_entry = ttk.Entry(creds_frame, textvariable=self.gads_manager_account,
                                 width=35, font=("Segoe UI", 9))
        manager_entry.grid(row=3, column=1, sticky=tk.EW, padx=(0, 0), pady=(0, 8))

        creds_frame.columnconfigure(1, weight=1)

        # OAuth and validation buttons (first row)
        buttons_frame1 = ttk.Frame(step1_frame)
        buttons_frame1.pack(fill=tk.X, pady=(10, 5))

        oauth_btn = ttk.Button(buttons_frame1, text="🔑 Get OAuth Token",
                              command=self.get_google_ads_oauth_tokens, width=18)
        oauth_btn.pack(side=tk.LEFT, padx=(0, 8))

        validate_btn = ttk.Button(buttons_frame1, text="✓ Validate Credentials",
                                 command=self.validate_google_ads_credentials, width=20)
        validate_btn.pack(side=tk.LEFT, padx=(0, 8))

        save_btn = ttk.Button(buttons_frame1, text="💾 Save Settings",
                             command=self.save_google_ads_settings, width=16)
        save_btn.pack(side=tk.LEFT, padx=(0, 8))

        reset_btn = ttk.Button(buttons_frame1, text="🔄 Load Defaults",
                              command=self.load_default_google_ads_settings, width=16)
        reset_btn.pack(side=tk.LEFT)

        # Connection and account buttons (second row)
        buttons_frame2 = ttk.Frame(step1_frame)
        buttons_frame2.pack(fill=tk.X, pady=(5, 5))

        test_btn = ttk.Button(buttons_frame2, text="Test Connection",
                             command=self.test_google_ads_connection, width=16)
        test_btn.pack(side=tk.LEFT, padx=(0, 8))

        search_btn = ttk.Button(buttons_frame2, text="🔍 Search Accounts",
                               command=self.search_google_ads_accounts, width=16)
        search_btn.pack(side=tk.LEFT, padx=(0, 8))

        # Client management button
        clients_btn = ttk.Button(buttons_frame2, text="👥 Manage Clients",
                                command=self.manage_clients, width=16)
        clients_btn.pack(side=tk.LEFT)

        # Connection status
        self.gads_connection_status = tk.StringVar(value="⚪ Not connected")
        status_label = ttk.Label(step1_frame, textvariable=self.gads_connection_status,
                                font=("Segoe UI", 9), foreground="#666666")
        status_label.pack(anchor=tk.W, pady=(5, 0))

        # === STEP 2: DATA EXTRACTION ===
        step2_frame = ttk.LabelFrame(left_scrollable, text="📊 Step 2: Data Extraction",
                                    padding=15)
        step2_frame.pack(fill=tk.X, padx=12, pady=(5, 10))

        # Date range selection
        date_frame = ttk.Frame(step2_frame)
        date_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(date_frame, text="Date Range:", font=("Segoe UI", 9, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))

        # Date range presets
        self.gads_date_preset = tk.StringVar(value="Last 30 days")
        date_combo = ttk.Combobox(date_frame, textvariable=self.gads_date_preset,
                                 values=["Last 7 days", "Last 30 days", "Last 90 days",
                                        "This month", "Last month", "Custom range"],
                                 state="readonly", width=20)
        date_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 5))
        date_combo.bind("<<ComboboxSelected>>", self.on_date_preset_change)

        # Custom date inputs (initially hidden)
        self.custom_date_frame = ttk.Frame(date_frame)
        self.custom_date_frame.grid(row=1, column=0, columnspan=3, sticky=tk.EW, pady=(5, 0))

        # From date with calendar picker
        from_frame = ttk.Frame(self.custom_date_frame)
        from_frame.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        ttk.Label(from_frame, text="From:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        from_input_frame = ttk.Frame(from_frame)
        from_input_frame.pack(fill=tk.X, pady=(2, 0))

        self.gads_start_date = tk.StringVar()
        start_date_entry = ttk.Entry(from_input_frame, textvariable=self.gads_start_date,
                                   width=12, font=("Segoe UI", 9))
        start_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(from_input_frame, text="📅", width=3,
                  command=self.open_start_date_picker).pack(side=tk.LEFT)

        # To date with calendar picker
        to_frame = ttk.Frame(self.custom_date_frame)
        to_frame.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(to_frame, text="To:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        to_input_frame = ttk.Frame(to_frame)
        to_input_frame.pack(fill=tk.X, pady=(2, 0))

        self.gads_end_date = tk.StringVar()
        end_date_entry = ttk.Entry(to_input_frame, textvariable=self.gads_end_date,
                                 width=12, font=("Segoe UI", 9))
        end_date_entry.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(to_input_frame, text="📅", width=3,
                  command=self.open_end_date_picker).pack(side=tk.LEFT)

        # Quick date range buttons
        quick_frame = ttk.Frame(self.custom_date_frame)
        quick_frame.grid(row=0, column=2, sticky=tk.W)

        ttk.Label(quick_frame, text="Quick Select:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        quick_buttons_frame = ttk.Frame(quick_frame)
        quick_buttons_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Button(quick_buttons_frame, text="Last 7d", width=8,
                  command=lambda: self.set_quick_range(7)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(quick_buttons_frame, text="Last 30d", width=8,
                  command=lambda: self.set_quick_range(30)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(quick_buttons_frame, text="Last 90d", width=8,
                  command=lambda: self.set_quick_range(90)).pack(side=tk.LEFT)

        # Initially hide custom date frame
        self.custom_date_frame.grid_remove()

        date_frame.columnconfigure(2, weight=1)

        # Campaign selection
        campaign_frame = ttk.Frame(step2_frame)
        campaign_frame.pack(fill=tk.X, pady=(5, 10))

        ttk.Label(campaign_frame, text="Campaigns:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)

        # Campaign listbox with scrollbar
        campaign_list_frame = ttk.Frame(campaign_frame)
        campaign_list_frame.pack(fill=tk.X, pady=(5, 0))

        self.gads_campaign_listbox = tk.Listbox(campaign_list_frame, height=4, selectmode=tk.MULTIPLE)
        campaign_scrollbar = ttk.Scrollbar(campaign_list_frame, orient=tk.VERTICAL,
                                          command=self.gads_campaign_listbox.yview)
        self.gads_campaign_listbox.configure(yscrollcommand=campaign_scrollbar.set)

        self.gads_campaign_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        campaign_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Load campaigns button
        load_campaigns_btn = ttk.Button(step2_frame, text="🔄 Load Campaigns",
                                       command=self.load_google_ads_campaigns, width=18)
        load_campaigns_btn.pack(pady=(5, 0))

        # Extraction status
        extraction_status_frame = ttk.Frame(step2_frame)
        extraction_status_frame.pack(fill=tk.X, pady=(10, 0))

        self.gads_extraction_status = tk.StringVar(value="⚪ Ready to extract")
        extraction_status_label = ttk.Label(extraction_status_frame, textvariable=self.gads_extraction_status,
                                           font=("Segoe UI", 9), foreground="#666666")
        extraction_status_label.pack(side=tk.LEFT)

        extract_btn = ttk.Button(extraction_status_frame, text="Extract Data",
                                command=self.extract_google_ads_data, width=14)
        extract_btn.pack(side=tk.RIGHT, padx=(0, 8))

        preview_btn = ttk.Button(extraction_status_frame, text="Preview",
                                command=self.preview_google_ads_data, width=12)
        preview_btn.pack(side=tk.RIGHT)

        # === STEP 3: AIRTABLE CONNECTION ===
        step3_frame = ttk.LabelFrame(left_scrollable, text="🗄️ Step 3: Airtable Connection",
                                    padding=15)
        step3_frame.pack(fill=tk.X, padx=12, pady=(5, 10))

        # Airtable credentials
        airtable_creds_frame = ttk.Frame(step3_frame)
        airtable_creds_frame.pack(fill=tk.X, pady=(0, 10))

        # API Key
        ttk.Label(airtable_creds_frame, text="API Key:", font=("Segoe UI", 9, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 8))
        self.gads_airtable_api_key = tk.StringVar()
        airtable_key_entry = ttk.Entry(airtable_creds_frame, textvariable=self.gads_airtable_api_key,
                                      width=50, show="*", font=("Segoe UI", 9))
        airtable_key_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 0), pady=(0, 8))

        airtable_creds_frame.columnconfigure(1, weight=1)

        # Base and table selection
        base_selection_frame = ttk.Frame(step3_frame)
        base_selection_frame.pack(fill=tk.X, pady=(5, 10))

        # Base selection
        ttk.Label(base_selection_frame, text="Base:", font=("Segoe UI", 9, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 5))
        self.gads_airtable_base = tk.StringVar()
        self.gads_base_combo = ttk.Combobox(base_selection_frame, textvariable=self.gads_airtable_base,
                                           width=30, state="readonly")
        self.gads_base_combo.grid(row=0, column=1, sticky=tk.EW, padx=(0, 8), pady=(0, 8))

        fetch_bases_btn = ttk.Button(base_selection_frame, text="Fetch Bases",
                                    command=self.fetch_airtable_bases_gads, width=14)
        fetch_bases_btn.grid(row=0, column=2, sticky=tk.W, pady=(0, 8))

        # Table selection
        ttk.Label(base_selection_frame, text="Table:", font=("Segoe UI", 9, "bold")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 5))
        self.gads_airtable_table = tk.StringVar()
        self.gads_table_combo = ttk.Combobox(base_selection_frame, textvariable=self.gads_airtable_table,
                                            width=30, state="readonly")
        self.gads_table_combo.grid(row=1, column=1, sticky=tk.EW, padx=(0, 8), pady=(0, 8))

        fetch_tables_btn = ttk.Button(base_selection_frame, text="Fetch Tables",
                                     command=self.fetch_google_ads_tables, width=14)
        fetch_tables_btn.grid(row=1, column=2, sticky=tk.W, pady=(0, 8))

        base_selection_frame.columnconfigure(1, weight=1)
        base_selection_frame.columnconfigure(0, minsize=60)  # Minimum width for labels

        # Customer configuration status
        customer_status_frame = ttk.Frame(step3_frame)
        customer_status_frame.pack(fill=tk.X, pady=(5, 10))

        ttk.Label(customer_status_frame, text="Customer Config:", font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
        self.customer_config_status = tk.StringVar(value="❌ No customer loaded")
        ttk.Label(customer_status_frame, textvariable=self.customer_config_status,
                 font=("Segoe UI", 9), foreground="#666666").pack(side=tk.LEFT, padx=(5, 0))

        # Auto-populate button (now shows status)
        self.auto_populate_btn = ttk.Button(customer_status_frame, text="🔄 Auto-Populate from Customer",
                                           command=self.auto_populate_airtable_from_customer)
        self.auto_populate_btn.pack(side=tk.RIGHT)

        # Airtable connection status
        airtable_status_frame = ttk.Frame(step3_frame)
        airtable_status_frame.pack(fill=tk.X, pady=(5, 0))

        self.gads_airtable_status = tk.StringVar(value="⚪ Not connected")
        airtable_status_label = ttk.Label(airtable_status_frame, textvariable=self.gads_airtable_status,
                                         font=("Segoe UI", 9), foreground="#666666")
        airtable_status_label.pack(side=tk.LEFT)

        ttk.Button(airtable_status_frame, text="Test Connection",
                  command=self.test_airtable_connection_gads, width=16).pack(side=tk.RIGHT)

        # === STEP 4: AIRTABLE SYNC ===
        step4_frame = ttk.LabelFrame(left_scrollable, text="🔄 Step 4: Airtable Sync",
                                    padding=15)
        step4_frame.pack(fill=tk.X, padx=12, pady=(5, 15))

        # Sync configuration
        sync_config_frame = ttk.Frame(step4_frame)
        sync_config_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(sync_config_frame, text="Sync Mode:", font=("Segoe UI", 9, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.gads_sync_mode = tk.StringVar(value="incremental")
        sync_combo = ttk.Combobox(sync_config_frame, textvariable=self.gads_sync_mode,
                                 values=["incremental", "replace", "append"],
                                 state="readonly", width=15)
        sync_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 5))

        # Auto-sync option
        self.gads_auto_sync = tk.BooleanVar(value=False)
        auto_sync_check = ttk.Checkbutton(sync_config_frame, text="Auto-sync after extraction",
                                         variable=self.gads_auto_sync)
        auto_sync_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Sync status
        self.gads_sync_status = tk.StringVar(value="⚪ Ready to sync")
        sync_status_label = ttk.Label(step4_frame, textvariable=self.gads_sync_status,
                                     font=("Segoe UI", 9), foreground="#666666")
        sync_status_label.pack(anchor=tk.W, pady=(5, 10))

        # Sync action buttons
        sync_buttons_frame = ttk.Frame(step4_frame)
        sync_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(sync_buttons_frame, text="Sync to Airtable",
                  command=self.sync_google_ads_to_airtable, width=16).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(sync_buttons_frame, text="View Stats",
                  command=self.view_google_ads_stats, width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(sync_buttons_frame, text="Clear Data",
                  command=self.clear_google_ads_data, width=12).pack(side=tk.LEFT)

        # Add spacer frame to fill remaining space and prevent white space
        spacer_frame = ttk.Frame(left_scrollable)
        spacer_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Final mouse wheel binding for all widgets (after all widgets are created)
        if hasattr(self, 'setup_gads_mousewheel'):
            left_scrollable.after(50, self.setup_gads_mousewheel)

        # === RIGHT PANEL: Activity Log ===
        right_frame = ttk.Frame(main_paned)
        right_frame.configure(width=350)  # Set fixed width for activity panel
        main_paned.add(right_frame, weight=1)  # Remove minsize for compatibility

        # Activity log header with better styling
        log_header_frame = ttk.Frame(right_frame)
        log_header_frame.pack(fill=tk.X, padx=8, pady=(8, 5))

        # Header with icon and title
        header_left = ttk.Frame(log_header_frame)
        header_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(header_left, text="📋 Activity Log",
                 font=("Segoe UI", 11, "bold")).pack(side=tk.LEFT)

        # Control buttons frame
        header_right = ttk.Frame(log_header_frame)
        header_right.pack(side=tk.RIGHT)

        ttk.Button(header_right, text="Clear Log",
                  command=self.clear_gads_log, width=10).pack(side=tk.RIGHT)

        # Activity log text area with better styling
        log_frame = ttk.Frame(right_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=(0, 8))

        self.gads_log_text = scrolledtext.ScrolledText(
            log_frame,
            height=28,
            width=45,  # Fixed width for consistency
            font=("Consolas", 8),
            bg="#f8f9fa",
            fg="#333333",
            wrap=tk.WORD,
            relief=tk.SUNKEN,
            borderwidth=1
        )
        self.gads_log_text.pack(fill=tk.BOTH, expand=True)

        # Add initial welcome message
        welcome_msg = """🎯 Google Ads Data Hub - Activity Log
{'=' * 45}

📋 Workflow Steps:
1. 📡 Connect to Google Ads API
2. 🗄️ Connect to Airtable
3. 📊 Extract campaign data
4. 🔄 Sync to Airtable

💡 Tips:
• Use incremental sync for regular updates
• Test connections before data operations
• Preview data to verify accuracy
• Export CSV for backup purposes

Ready to start! 🚀
{'=' * 45}

"""
        self.gads_log_text.insert(tk.END, welcome_msg)

        # Initialize variables
        self.google_ads_data = None
        self.airtable_manager = None
        self.gads_available_bases = {}
        self.gads_available_tables = {}

    def setup_generation_tab(self, notebook):
        """Setup dashboard generation and verification tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🚀 Generate Dashboard")

        # Title
        title_label = ttk.Label(frame, text="Dashboard Generation & Verification", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # Generation controls
        controls_frame = ttk.LabelFrame(frame, text="Generation Controls", padding=10)
        controls_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(controls_frame, text="🔍 Validate Configuration",
                  command=self.validate_configuration,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="🚀 Generate Dashboard",
                  command=self.generate_dashboard,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="📄 View Report",
                  command=self.view_generation_report).pack(side=tk.LEFT, padx=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(controls_frame, variable=self.progress_var,
                                          maximum=100, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=5)

        # Output log
        log_frame = ttk.LabelFrame(frame, text="Generation Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Clear log button
        ttk.Button(log_frame, text="Clear Log",
                  command=self.clear_log).pack(anchor=tk.E, pady=5)

    def load_available_bases(self):
        """Load available Airtable bases with pagination support"""
        self.log_message("🔍 Loading available Airtable bases...")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            all_bases = []
            offset = None
            max_bases = 100  # Fetch up to 100 bases

            while len(all_bases) < max_bases:
                # Build URL with pagination
                url = 'https://api.airtable.com/v0/meta/bases'
                params = {}
                if offset:
                    params['offset'] = offset

                response = requests.get(url, headers=headers, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    bases = data.get('bases', [])

                    if not bases:
                        break

                    all_bases.extend(bases)

                    # Check if there are more bases to fetch
                    offset = data.get('offset')
                    if not offset:
                        break

                    # Limit to max_bases
                    if len(all_bases) >= max_bases:
                        all_bases = all_bases[:max_bases]
                        break

                else:
                    error_msg = f"❌ Failed to load bases: {response.status_code} - {response.text}"
                    self.log_message(error_msg)
                    messagebox.showerror("Error", error_msg)
                    return

            self.available_bases = {f"{base['name']} ({base['id']})": base['id'] for base in all_bases}

            # Update combobox
            base_options = list(self.available_bases.keys())
            self.base_combobox['values'] = base_options

            self.log_message(f"✅ Loaded {len(all_bases)} available bases")
            messagebox.showinfo("Success", f"✅ Loaded {len(all_bases)} available bases")

        except Exception as e:
            error_msg = f"❌ Error loading bases: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def on_base_selected(self, event=None):
        """Handle base selection"""
        selected_base = self.base_combobox.get()
        if selected_base and selected_base in self.available_bases:
            base_id = self.available_bases[selected_base]
            self.base_id.set(base_id)
            self.log_message(f"📋 Selected base: {selected_base}")

            # Auto-load tables for selected base
            self.load_base_tables()

    def load_base_tables(self):
        """Load tables for the selected base"""
        base_id = self.base_id.get()
        if not base_id:
            messagebox.showerror("Error", "Please select a base first")
            return

        self.log_message(f"🔍 Loading tables for base: {base_id}")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Get base schema
            url = f'https://api.airtable.com/v0/meta/bases/{base_id}/tables'
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                tables = response.json().get('tables', [])
                self.available_tables = {f"{table['name']} ({table['id']})": table['id'] for table in tables}

                # Update all table comboboxes
                table_options = list(self.available_tables.keys())
                self.ghl_combobox['values'] = table_options
                self.google_ads_combobox['values'] = table_options
                self.pos_combobox['values'] = table_options
                self.meta_ads_combobox['values'] = table_options

                self.log_message(f"✅ Loaded {len(tables)} tables")

                # Show table info
                table_info = "\n".join([f"• {table['name']} (ID: {table['id']})" for table in tables])
                self.log_message(f"📋 Available tables:\n{table_info}")

                # Auto-detect table IDs
                self.auto_detect_table_ids()

            else:
                error_msg = f"❌ Failed to load tables: {response.status_code} - {response.text}"
                self.log_message(error_msg)
                messagebox.showerror("Error", error_msg)

        except Exception as e:
            error_msg = f"❌ Error loading tables: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def auto_detect_table_ids(self):
        """Auto-detect table IDs based on table names"""
        if not self.available_tables:
            self.log_message("⚠️ No tables loaded. Load base tables first.")
            return

        self.log_message("🤖 Auto-detecting table IDs...")

        # Define detection patterns
        detection_patterns = {
            'ghl': ['ghl', 'gohighlevel', 'leads', 'lead'],
            'googleAds': ['google ads', 'google_ads', 'googleads', 'ads', 'adwords'],
            'pos': ['pos', 'point of sale', 'sales', 'transactions'],
            'metaAds': ['meta ads', 'meta_ads', 'facebook ads', 'fb ads', 'instagram ads']
        }

        detected = {}

        for table_display, table_id in self.available_tables.items():
            table_name = table_display.split(' (')[0].lower()  # Get name without ID

            for source, patterns in detection_patterns.items():
                for pattern in patterns:
                    if pattern in table_name:
                        if source not in detected:  # Take first match
                            detected[source] = table_display
                            break

        # Update comboboxes with detected values
        if 'ghl' in detected:
            self.ghl_combobox.set(detected['ghl'])
            self.ghl_table_id.set(self.available_tables[detected['ghl']])
            self.log_message(f"🎯 Auto-detected GHL table: {detected['ghl']}")

        if 'googleAds' in detected:
            self.google_ads_combobox.set(detected['googleAds'])
            self.google_ads_table_id.set(self.available_tables[detected['googleAds']])
            self.log_message(f"🎯 Auto-detected Google Ads table: {detected['googleAds']}")

        if 'pos' in detected:
            self.pos_combobox.set(detected['pos'])
            self.pos_table_id.set(self.available_tables[detected['pos']])
            self.log_message(f"🎯 Auto-detected POS table: {detected['pos']}")

        if 'metaAds' in detected:
            self.meta_ads_combobox.set(detected['metaAds'])
            self.meta_ads_table_id.set(self.available_tables[detected['metaAds']])
            self.log_message(f"🎯 Auto-detected Meta Ads table: {detected['metaAds']}")

        if detected:
            self.log_message(f"✅ Auto-detection complete! Found {len(detected)} matches")
            messagebox.showinfo("Auto-Detection Complete",
                              f"✅ Auto-detected {len(detected)} table IDs!\n\nCheck the dropdowns and adjust if needed.")
        else:
            self.log_message("⚠️ No tables auto-detected. Please select manually.")
            messagebox.showinfo("Auto-Detection",
                              "⚠️ No tables were auto-detected based on common naming patterns.\n\nPlease select tables manually from the dropdowns.")

    # === CLIENT AIRTABLE SELECTION METHODS ===

    def select_client_airtable_base(self):
        """Open dialog to select Airtable base for client"""
        self.log_message("🔍 Opening client Airtable base selection...")

        # Create selection window
        base_window = tk.Toplevel(self.root)
        base_window.title("Select Client Airtable Base")
        base_window.geometry("800x500")
        base_window.transient(self.root)
        base_window.grab_set()

        # Load bases if not already loaded
        if not hasattr(self, 'client_available_bases') or not self.client_available_bases:
            self.load_client_available_bases()

        # Create treeview for base selection
        tree_frame = ttk.Frame(base_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(tree_frame, text="Select Airtable Base:", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))

        tree = ttk.Treeview(tree_frame, columns=('id',), show='tree headings')
        tree.heading('#0', text='Base Name')
        tree.heading('id', text='Base ID')
        tree.column('#0', width=400)
        tree.column('id', width=300)

        # Add bases to tree
        if hasattr(self, 'client_available_bases'):
            for base_name, base_id in self.client_available_bases.items():
                tree.insert('', tk.END, text=base_name, values=(base_id,))

        tree.pack(fill=tk.BOTH, expand=True)

        # Buttons
        button_frame = ttk.Frame(base_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        def select_base():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                base_name = item['text']
                base_id = item['values'][0]
                self.client_airtable_base_id.set(base_id)
                self.log_message(f"✅ Selected client base: {base_name} ({base_id})")
                self.update_client_airtable_status()
                base_window.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a base first.")

        ttk.Button(button_frame, text="✅ Select", command=select_base).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 Refresh", command=lambda: self.load_client_available_bases()).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ Cancel", command=base_window.destroy).pack(side=tk.RIGHT)

        # Double-click to select
        tree.bind("<Double-1>", lambda e: select_base())

    def select_client_google_ads_table(self):
        """Open dialog to select Google Ads table for client"""
        if not self.client_airtable_base_id.get():
            messagebox.showerror("Error", "Please select an Airtable base first.")
            return

        self.log_message("🔍 Opening client Google Ads table selection...")
        self._select_client_table("Google Ads", self.client_google_ads_table_id, ['google ads', 'ads', 'adwords'])

    def select_client_ghl_table(self):
        """Open dialog to select GHL table for client"""
        if not self.client_airtable_base_id.get():
            messagebox.showerror("Error", "Please select an Airtable base first.")
            return

        self.log_message("🔍 Opening client GHL table selection...")
        self._select_client_table("GHL", self.client_ghl_table_id, ['ghl', 'gohighlevel', 'leads'])

    def _select_client_table(self, table_type, target_var, search_patterns):
        """Generic method to select a table for client"""
        # Load tables for the selected base
        base_id = self.client_airtable_base_id.get()
        tables = self.load_tables_for_base(base_id)

        if not tables:
            messagebox.showerror("Error", f"Could not load tables for base {base_id}")
            return

        # Create selection window
        table_window = tk.Toplevel(self.root)
        table_window.title(f"Select Client {table_type} Table")
        table_window.geometry("800x500")
        table_window.transient(self.root)
        table_window.grab_set()

        # Create treeview for table selection
        tree_frame = ttk.Frame(table_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(tree_frame, text=f"Select {table_type} Table:", font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))

        tree = ttk.Treeview(tree_frame, columns=('id',), show='tree headings')
        tree.heading('#0', text='Table Name')
        tree.heading('id', text='Table ID')
        tree.column('#0', width=400)
        tree.column('id', width=300)

        # Add tables to tree, highlighting likely matches
        for table in tables:
            table_name = table['name']
            table_id = table['id']

            # Check if this table matches our search patterns
            is_match = any(pattern in table_name.lower() for pattern in search_patterns)

            item_id = tree.insert('', tk.END, text=table_name, values=(table_id,))
            if is_match:
                tree.set(item_id, '#0', f"⭐ {table_name}")  # Mark likely matches

        tree.pack(fill=tk.BOTH, expand=True)

        # Buttons
        button_frame = ttk.Frame(table_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        def select_table():
            selection = tree.selection()
            if selection:
                item = tree.item(selection[0])
                table_name = item['text'].replace("⭐ ", "")  # Remove star if present
                table_id = item['values'][0]
                target_var.set(table_id)
                self.log_message(f"✅ Selected client {table_type} table: {table_name} ({table_id})")
                self.update_client_airtable_status()
                table_window.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a table first.")

        ttk.Button(button_frame, text="✅ Select", command=select_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ Cancel", command=table_window.destroy).pack(side=tk.RIGHT)

        # Double-click to select
        tree.bind("<Double-1>", lambda e: select_table())

    def load_client_available_bases(self):
        """Load available Airtable bases for client selection with pagination support"""
        self.log_message("🔍 Loading available Airtable bases for client...")

        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            all_bases = []
            offset = None
            max_bases = 100  # Fetch up to 100 bases

            while len(all_bases) < max_bases:
                # Build URL with pagination
                url = 'https://api.airtable.com/v0/meta/bases'
                params = {}
                if offset:
                    params['offset'] = offset

                self.log_message(f"🔄 Fetching bases... (current count: {len(all_bases)})")
                response = requests.get(url, headers=headers, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    bases = data.get('bases', [])

                    if not bases:
                        # No more bases to fetch
                        break

                    all_bases.extend(bases)
                    self.log_message(f"📊 Fetched {len(bases)} bases in this batch")

                    # Check if there are more bases to fetch
                    offset = data.get('offset')
                    if not offset:
                        # No more pages
                        break

                    # Limit to max_bases
                    if len(all_bases) >= max_bases:
                        all_bases = all_bases[:max_bases]
                        break

                else:
                    error_msg = f"❌ Failed to load bases: {response.status_code} - {response.text}"
                    self.log_message(error_msg)
                    messagebox.showerror("Error", error_msg)
                    return False

            # Store the bases
            self.client_available_bases = {base['name']: base['id'] for base in all_bases}

            self.log_message(f"✅ Successfully loaded {len(all_bases)} available bases for client")

            # Log some base names for verification
            if all_bases:
                sample_names = list(self.client_available_bases.keys())[:5]
                self.log_message(f"📋 Sample bases: {', '.join(sample_names)}")

            return True

        except Exception as e:
            error_msg = f"❌ Error loading bases: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)
            return False

    def load_tables_for_base(self, base_id):
        """Load tables for a specific base"""
        try:
            headers = {
                'Authorization': f'Bearer {self.airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Get base schema
            url = f'https://api.airtable.com/v0/meta/bases/{base_id}/tables'
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                tables = response.json().get('tables', [])
                self.log_message(f"✅ Loaded {len(tables)} tables from base {base_id}")
                return tables
            else:
                self.log_message(f"❌ Failed to load tables: {response.status_code}")
                return []

        except Exception as e:
            self.log_message(f"❌ Error loading tables: {str(e)}")
            return []

    def update_client_airtable_status(self):
        """Update the client Airtable configuration status"""
        base_configured = bool(self.client_airtable_base_id.get())
        google_ads_configured = bool(self.client_google_ads_table_id.get())

        if base_configured and google_ads_configured:
            self.client_airtable_status.set("✅ Fully Configured")
            # Update status label color to green
            for widget in self.root.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'winfo_children'):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Label) and grandchild.cget('textvariable') == str(self.client_airtable_status):
                                    grandchild.configure(foreground="green")
        elif base_configured:
            self.client_airtable_status.set("⚠️ Base Selected - Need Tables")
        else:
            self.client_airtable_status.set("❌ Not Configured")

    # === HELPER METHODS FOR CLIENT FORM ===

    def select_base_for_client(self, base_var):
        """Helper method to select Airtable base for client form"""
        # Always refresh bases to ensure we have the latest list
        self.log_message("🔄 Refreshing base list to ensure latest bases are available...")
        self.load_client_available_bases()

        if not hasattr(self, 'client_available_bases') or not self.client_available_bases:
            messagebox.showerror("Error", "Could not load available bases. Check your API key.")
            return

        # Create simple selection dialog
        selection_window = tk.Toplevel(self.root)
        selection_window.title("Select Airtable Base")
        selection_window.geometry("600x400")
        selection_window.transient(self.root)
        selection_window.grab_set()

        # List of bases
        listbox = tk.Listbox(selection_window)
        listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        base_list = list(self.client_available_bases.items())
        for base_name, base_id in base_list:
            listbox.insert(tk.END, f"{base_name} ({base_id})")

        def select_base():
            selection = listbox.curselection()
            if selection:
                base_name, base_id = base_list[selection[0]]
                base_var.set(base_id)
                selection_window.destroy()

                # Call the callback if it exists
                if hasattr(self, '_client_base_callback'):
                    try:
                        self._client_base_callback()
                    except Exception as e:
                        self.log_message(f"⚠️ Error in base selection callback: {str(e)}")
            else:
                messagebox.showwarning("No Selection", "Please select a base first.")

        # Buttons
        button_frame = ttk.Frame(selection_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        def refresh_bases():
            """Refresh the base list"""
            self.load_client_available_bases()
            # Update the listbox
            listbox.delete(0, tk.END)
            base_list.clear()
            base_list.extend(list(self.client_available_bases.items()))
            for base_name, base_id in base_list:
                listbox.insert(tk.END, f"{base_name} ({base_id})")

        ttk.Button(button_frame, text="✅ Select", command=select_base).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 Refresh", command=refresh_bases).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ Cancel", command=selection_window.destroy).pack(side=tk.RIGHT)

        # Double-click to select
        listbox.bind("<Double-1>", lambda e: select_base())

    def auto_load_tables_for_client(self, base_id, google_ads_var, ghl_var, meta_ads_var=None, pos_var=None):
        """Auto-load table IDs when a base is selected"""
        if not base_id:
            return

        try:
            # Load tables for the selected base
            tables = self.load_tables_for_base(base_id)
            if not tables:
                return

            # Define table name mappings (case-insensitive)
            table_mappings = {
                'google ads': google_ads_var,
                'ghl': ghl_var,
                'meta ads': meta_ads_var if meta_ads_var else None,
                'pos': pos_var if pos_var else None
            }

            # Auto-fill table IDs based on table names
            for table in tables:
                table_name = table['name']
                table_id = table['id']
                table_name_lower = table_name.lower()

                for mapping_name, var in table_mappings.items():
                    if var and mapping_name in table_name_lower:
                        var.set(table_id)
                        self.log_message(f"✅ Auto-filled {mapping_name.title()} table: {table_name}")
                        break

        except Exception as e:
            self.log_message(f"⚠️ Could not auto-load tables: {str(e)}")

    def select_table_for_client(self, base_var, table_var, table_type):
        """Helper method to select Airtable table for client form"""
        base_id = base_var.get()
        if not base_id:
            messagebox.showerror("Error", "Please select a base first.")
            return

        # Load tables for the base
        tables = self.load_tables_for_base(base_id)
        if not tables:
            messagebox.showerror("Error", f"Could not load tables for base {base_id}")
            return

        # Create simple selection dialog
        selection_window = tk.Toplevel(self.root)
        selection_window.title(f"Select {table_type} Table")
        selection_window.geometry("600x400")
        selection_window.transient(self.root)
        selection_window.grab_set()

        # List of tables
        listbox = tk.Listbox(selection_window)
        listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Add tables without any icons - clean list
        for table in tables:
            table_name = table['name']
            table_id = table['id']
            display_name = f"{table_name} ({table_id})"
            listbox.insert(tk.END, display_name)

        def select_table():
            selection = listbox.curselection()
            if selection:
                table = tables[selection[0]]
                table_var.set(table['id'])
                selection_window.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a table first.")

        # Buttons
        button_frame = ttk.Frame(selection_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="✅ Select", command=select_table).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ Cancel", command=selection_window.destroy).pack(side=tk.RIGHT)

        # Double-click to select
        listbox.bind("<Double-1>", lambda e: select_table())

    def validate_configuration(self):
        """Validate the current configuration"""
        self.log_message("🔍 Validating configuration...")

        errors = []
        warnings = []

        # Check client information
        if not self.client_name.get():
            errors.append("Client name is required")
        if not self.store_name.get():
            errors.append("Store name is required")

        # Check template
        if not Path(self.template_path.get()).exists():
            errors.append("Template directory does not exist")

        # Check Airtable configuration
        if not self.base_id.get():
            errors.append("Airtable Base ID is required")
        elif not re.match(r'^app[a-zA-Z0-9]{14}$', self.base_id.get()):
            errors.append("Invalid Base ID format (should be app + 14 characters)")

        # Check enabled data sources have table IDs
        if self.enable_ghl.get() and not self.ghl_table_id.get():
            errors.append("GHL is enabled but no table selected")
        if self.enable_google_ads.get() and not self.google_ads_table_id.get():
            errors.append("Google Ads is enabled but no table selected")
        if self.enable_pos.get() and not self.pos_table_id.get():
            errors.append("POS is enabled but no table selected")
        if self.enable_meta_ads.get() and not self.meta_ads_table_id.get():
            errors.append("Meta Ads is enabled but no table selected")

        # Check table ID formats
        table_ids = [
            (self.ghl_table_id.get(), "GHL"),
            (self.google_ads_table_id.get(), "Google Ads"),
            (self.pos_table_id.get(), "POS"),
            (self.meta_ads_table_id.get(), "Meta Ads")
        ]

        for table_id, source in table_ids:
            if table_id and not re.match(r'^tbl[a-zA-Z0-9]{14}$', table_id):
                errors.append(f"Invalid {source} table ID format (should be tbl + 14 characters)")

        # Check if at least one data source is enabled
        if not any([self.enable_ghl.get(), self.enable_google_ads.get(),
                   self.enable_pos.get(), self.enable_meta_ads.get()]):
            errors.append("At least one data source must be enabled")

        # Validate Google Ads location configuration if Google Ads is enabled
        if self.enable_google_ads.get():
            location_names = [name.strip() for name in self.google_ads_locations.get().split(',') if name.strip()]
            if not location_names:
                warnings.append("Google Ads is enabled but no location names configured")
            elif len(location_names) < 2:
                warnings.append("Consider configuring at least 2 locations for meaningful location analytics")

            # Check for valid location names
            for location in location_names:
                if len(location) < 2:
                    warnings.append(f"Location name '{location}' is very short - consider using more descriptive names")
                if location.lower() in ['location', 'store', 'shop']:
                    warnings.append(f"Location name '{location}' is generic - consider using specific location names")

        # Show results
        if errors:
            error_msg = "❌ Configuration errors found:\n" + "\n".join(f"• {error}" for error in errors)
            self.log_message(error_msg)
            messagebox.showerror("Validation Failed", error_msg)
        else:
            success_msg = "✅ Configuration validation passed!"
            self.log_message(success_msg)
            messagebox.showinfo("Validation Passed", success_msg)

        if warnings:
            warning_msg = "⚠️ Configuration warnings:\n" + "\n".join(f"• {warning}" for warning in warnings)
            self.log_message(warning_msg)

    def log_message(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """Clear the log text"""
        self.log_text.delete(1.0, tk.END)

    def generate_dashboard(self):
        """Generate the client dashboard"""
        # First validate configuration
        if not self._validate_config_for_generation():
            return

        # Run generation in separate thread to prevent UI freezing
        generation_thread = threading.Thread(target=self._generate_dashboard_thread)
        generation_thread.daemon = True
        generation_thread.start()

    def _validate_config_for_generation(self):
        """Quick validation before generation"""
        if not self.client_name.get():
            messagebox.showerror("Error", "Client name is required")
            return False
        if not self.store_name.get():
            messagebox.showerror("Error", "Store name is required")
            return False
        if not self.base_id.get():
            messagebox.showerror("Error", "Base ID is required")
            return False
        if not Path(self.template_path.get()).exists():
            messagebox.showerror("Error", "Template directory does not exist")
            return False
        return True

    def _generate_dashboard_thread(self):
        """Dashboard generation in separate thread"""
        try:
            self.progress_var.set(0)
            self.log_message("🚀 Starting dashboard generation...")

            # Step 1: Prepare configuration
            self.progress_var.set(10)
            config = self._prepare_client_config()
            self.log_message(f"📋 Configuration prepared for: {config['client_name']}")

            # Step 1.5: Parse location configuration
            self.log_message("📋 Starting location configuration parsing...")
            try:
                location_config = self._parse_location_configuration()
                self.log_message(f"✅ Location configuration parsed successfully: {len(location_config['names'])} locations")
            except Exception as e:
                self.log_message(f"⚠️ Error parsing location configuration: {str(e)}")
                # Use default configuration if parsing fails
                location_config = {
                    'names': ['Location 1', 'Location 2', 'Location 3'],
                    'icons': {'Location 1': '🔧', 'Location 2': '🛠️', 'Location 3': '⚙️'},
                    'colors': {'Location 1': '#4CAF50', 'Location 2': '#2196F3', 'Location 3': '#FF9800'}
                }
                self.log_message("📋 Using default location configuration")

            # Step 2: Create output directory
            self.progress_var.set(20)
            output_dir = self._create_output_directory(config)
            self.log_message(f"📁 Output directory created: {output_dir}")

            # Step 3: Copy template files
            self.progress_var.set(40)
            self._copy_template_files(output_dir)
            self.log_message("📋 Template files copied")

            # Step 4: Customize configuration
            self.progress_var.set(60)
            self._customize_client_config(output_dir, config, location_config)
            self.log_message("⚙️ Client configuration customized")

            # Clean up hardcoded locations in HTML
            self._clean_hardcoded_locations(output_dir)

            # Step 5: Update server configuration
            self.progress_var.set(80)
            self._update_server_config(output_dir, config)
            self.log_message("🔧 Server configuration updated")

            # Step 6: Verify generated dashboard
            self.progress_var.set(90)
            verification_results = self._verify_generated_dashboard(output_dir, config)
            self.verification_results = verification_results

            # Step 7: Generate report
            self.progress_var.set(95)
            self._generate_summary_report(output_dir, config, verification_results, location_config)

            self.progress_var.set(100)

            # Show results
            if verification_results.get('errors', []):
                error_count = len(verification_results['errors'])
                self.log_message(f"⚠️ Dashboard generated with {error_count} issues")
                messagebox.showwarning("Generation Complete",
                                     f"Dashboard generated with {error_count} issues. Check the report for details.")
            else:
                self.log_message("✅ Dashboard generated successfully!")
                messagebox.showinfo("Success",
                                  f"✅ Dashboard generated successfully!\n\nLocation: {output_dir}\n\nCheck the generation report for details.")

        except Exception as e:
            self.log_message(f"❌ Generation failed: {str(e)}")
            messagebox.showerror("Generation Failed", f"❌ Generation failed: {str(e)}")
        finally:
            self.progress_var.set(0)

    def _prepare_client_config(self):
        """Prepare client configuration dictionary"""
        # Get enabled sources
        enabled_sources = []
        if self.enable_ghl.get():
            enabled_sources.append('ghl')
        if self.enable_google_ads.get():
            enabled_sources.append('googleAds')
        if self.enable_pos.get():
            enabled_sources.append('pos')
        if self.enable_meta_ads.get():
            enabled_sources.append('metaAds')

        # Prepare table IDs (only for enabled sources)
        table_ids = {}
        table_ids['ghl'] = self.ghl_table_id.get() if self.enable_ghl.get() else None
        table_ids['googleAds'] = self.google_ads_table_id.get() if self.enable_google_ads.get() else None
        table_ids['pos'] = self.pos_table_id.get() if self.enable_pos.get() else None
        table_ids['metaAds'] = self.meta_ads_table_id.get() if self.enable_meta_ads.get() else None

        return {
            'client_name': self.client_name.get(),
            'business_name': self.business_name.get(),
            'store_name': self.store_name.get(),
            'base_id': self.base_id.get(),
            'enabled_sources': enabled_sources,
            'table_ids': table_ids
        }

    def _create_output_directory(self, config):
        """Create output directory with safe naming"""
        # Create safe directory name
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', config['client_name'].lower())
        safe_name = re.sub(r'_+', '_', safe_name).strip('_')

        if self.output_path.get():
            base_path = Path(self.output_path.get())
        else:
            base_path = Path(".")

        output_dir = base_path / f"{safe_name}_dashboard"

        # Handle existing directory
        if output_dir.exists():
            backup_dir = base_path / f"{safe_name}_dashboard_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.log_message(f"📦 Backing up existing dashboard to: {backup_dir}")
            shutil.move(str(output_dir), str(backup_dir))

        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir

    def _copy_template_files(self, output_dir):
        """Copy all template files to output directory"""
        template_path = Path(self.template_path.get())

        # Files to exclude
        exclude_patterns = [
            '__pycache__',
            '*.pyc',
            '.git',
            'logs',
            'test_*',
            'debug_*',
            '*.log'
        ]

        def should_exclude(path):
            for pattern in exclude_patterns:
                if pattern in str(path) or path.name.startswith('test_') or path.name.startswith('debug_'):
                    return True
            return False

        copied_files = 0
        for item in template_path.rglob('*'):
            if item.is_file() and not should_exclude(item):
                relative_path = item.relative_to(template_path)
                dest_path = output_dir / relative_path
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(item, dest_path)
                copied_files += 1

        self.log_message(f"📋 Copied {copied_files} template files")

    def _customize_client_config(self, output_dir, config, location_config):
        """Customize the client-config.js file by generating it from scratch"""
        config_file = output_dir / 'client-config.js'

        # Generate the complete client-config.js from scratch to avoid regex issues
        content = self._generate_client_config_content(config, output_dir, location_config)

        # Content is generated from scratch, no regex needed

        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)

    def _generate_client_config_content(self, config, output_dir, location_config):
        """Generate complete client-config.js content from scratch"""



        # Prepare enabled/disabled sources
        all_sources = ['ghl', 'googleAds', 'pos', 'metaAds']
        enabled_sources = config['enabled_sources']
        disabled_sources = [s for s in all_sources if s not in enabled_sources]

        # Generate table IDs section
        table_ids_section = ""
        for source, table_id in config['table_ids'].items():
            if table_id:
                comment = "// ENABLED"
                table_ids_section += f"            {source}: '{table_id}',{' ' * (30 - len(source))}// {source.upper()}\n"
            else:
                comment = "// DISABLED"
                table_ids_section += f"            {source}: null,{' ' * (35 - len(source))}// {source.upper()} - DISABLED\n"

        # Generate the complete file content using .format() to avoid f-string issues
        content = '''/**
 * CENTRALIZED CLIENT CONFIGURATION FOR {client_name_upper}
 * Single source of truth for all client-specific settings
 *
 * Generated by RL Dashboard Customizer v3.0
 * Client: {client_name}
 * Base ID: {base_id}
 * Generated: {generation_time}
 */

console.log('🎯 LOADING {client_name_upper} CONFIG FROM CORRECT FILE!');
console.log('📁 File: {output_dir_name}/client-config.js');
console.log('🏢 Client: {client_name}');
console.log('🗄️ Base ID: {base_id}');

// Legacy AIRTABLE_CONFIG for backward compatibility with script.js
window.AIRTABLE_CONFIG = {{
    baseId: '{base_id}',
    tables: {{
{table_ids_section}
    }}
}};

window.CLIENT_CONFIG = {{
    // ===== CLIENT INFORMATION =====
    clientName: '{client_name}',
    businessName: '{business_name}',
    storeName: '{store_name}', // Display name for header

    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data

    // ===== GOOGLE ADS LOCATION CONFIGURATION =====
    // Configuration for extracting locations from Google Ads campaign names
    googleAdsLocations: {{
        // List of location names to extract from campaign names
        // These should match the location names used in your Google Ads campaigns
        names: {location_names_json},

        // Optional: Custom icons for each location (emoji or FontAwesome class)
        icons: {location_icons_json},

        // Optional: Custom colors for location cards
        colors: {location_colors_json},

        // Optional: Location aliases (alternative names that should map to the same location)
        aliases: {{
            'Location 1': ['Location 1 Shop', 'Location 1 Store'],
            'Location 2': ['Location 2 Shop', 'Location 2 Store'],
            'Location 3': ['Location 3 Shop', 'Location 3 Store']
        }},

        // Matching options
        caseSensitive: false, // Whether location matching should be case-sensitive
        partialMatch: true,   // Whether to allow partial matches

        // Default icon for unknown locations
        defaultIcon: '📍',
        defaultColor: '#6c757d'
    }},

    // ===== AIRTABLE CONFIGURATION =====
    airtable: {{
        baseId: '{base_id}',

        // Table IDs - set to null to disable a data source
        tables: {{
{table_ids_section}
        }}
    }},

    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {{
        // Enabled data sources (customizer will set based on client needs)
        enabled: {enabled_sources_json},

        // Disabled data sources (will be hidden from UI)
        disabled: {disabled_sources_json},

        // Default date ranges for each source
        defaultDateRanges: {{
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }}
    }},

    // ===== HELPER FUNCTIONS =====

    /**
     * Check if a data source is enabled
     */
    isEnabled: function(dataSource) {{
        return this.dataSources.enabled.includes(dataSource);
    }},

    /**
     * Get table ID for a specific data source
     */
    getTableId: function(dataSource) {{
        return this.airtable.tables[dataSource] || null;
    }},

    /**
     * Get base ID
     */
    getBaseId: function() {{
        return this.airtable.baseId;
    }},

    /**
     * Update store name (for customizer use)
     */
    updateStoreName: function(newStoreName) {{
        this.storeName = newStoreName;
        this.businessName = newStoreName;
        this.clientName = newStoreName;

        // Update header immediately if DOM is ready
        const storeNameElement = document.getElementById('store-name');
        if (storeNameElement) {{
            storeNameElement.textContent = newStoreName;
            console.log('[CONFIG] Store name updated to:', newStoreName);
        }}
    }},

    /**
     * Populate location dropdowns from GHL data
     */
    populateLocationsFromData: function(ghlData) {{
        if (!ghlData || !Array.isArray(ghlData)) {{
            console.warn('[CONFIG] No GHL data provided for location population');
            return;
        }}

        // Extract unique locations from GHL data with multiple field name variations
        const locationFields = ['Location', 'location', 'LOCATION', 'store', 'Store', 'branch', 'Branch'];
        const locations = [...new Set(
            ghlData
                .map(record => {{
                    // Try multiple possible field names for location
                    for (const field of locationFields) {{
                        if (record[field] && typeof record[field] === 'string' && record[field].trim()) {{
                            return record[field].trim();
                        }}
                    }}
                    return null;
                }})
                .filter(location => location)
        )].sort();

        this.locations = locations;
        console.log('[CONFIG] Populated locations from GHL data:', locations);
        console.log('[CONFIG] Total unique locations found:', locations.length);

        if (locations.length === 0) {{
            console.warn('[CONFIG] No locations found in GHL data. Check field names.');
            // Log sample record to help debug field names
            if (ghlData.length > 0) {{
                console.log('[CONFIG] Sample GHL record fields:', Object.keys(ghlData[0]));
            }}
            return;
        }}

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {{
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {{
                // Store the "All Locations" option
                const allOptionText = dropdown.querySelector('option[value="all"]')?.textContent || 'All Locations';

                // Clear ALL existing options (including hardcoded ones)
                dropdown.innerHTML = '';

                // Re-add "All Locations" option first
                const allOption = document.createElement('option');
                allOption.value = 'all';
                allOption.textContent = allOptionText;
                dropdown.appendChild(allOption);

                // Add dynamic location options
                locations.forEach(location => {{
                    const option = document.createElement('option');
                    option.value = location;
                    option.textContent = location;
                    dropdown.appendChild(option);
                }});

                console.log(`[CONFIG] Updated dropdown: ${{dropdownId}} with ${{locations.length}} locations`);
            }} else {{
                console.warn(`[CONFIG] Dropdown not found: ${{dropdownId}}`);
            }}
        }});

        // Force a refresh of any existing filters
        this.refreshLocationFilters();
    }},

    /**
     * Refresh location filters after population
     */
    refreshLocationFilters: function() {{
        // Trigger change events to refresh any dependent UI elements
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {{
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {{
                // Reset to "all" and trigger change event
                dropdown.value = 'all';
                dropdown.dispatchEvent(new Event('change', {{ bubbles: true }}));
            }}
        }});

        console.log('[CONFIG] Location filters refreshed');
    }},

    /**
     * Clear cached data for fresh configuration
     */
    clearCache: function() {{
        // Clear any cached data that might interfere with new configuration
        if (typeof localStorage !== 'undefined') {{
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {{
                const key = localStorage.key(i);
                if (key && (key.includes('airtable') || key.includes('dashboard'))) {{
                    keysToRemove.push(key);
                }}
            }}
            keysToRemove.forEach(key => localStorage.removeItem(key));
            console.log('[CONFIG] Cleared cached data for fresh configuration');
        }}
    }},

    // ===== GOOGLE ADS LOCATION CONFIGURATION HELPERS =====

    /**
     * Update Google Ads location configuration for a new client
     * @param {{Array}} locationNames - Array of location names (e.g., ['Downtown', 'Uptown', 'Westside'])
     * @param {{Object}} options - Optional configuration (icons, colors, aliases, etc.)
     */
    updateGoogleAdsLocations: function(locationNames, options = {{}}) {{
        if (!Array.isArray(locationNames) || locationNames.length === 0) {{
            console.error('[CONFIG] Invalid location names provided. Must be a non-empty array.');
            return false;
        }}

        // Update the location names
        this.googleAdsLocations.names = locationNames;

        // Update optional configurations
        if (options.icons && typeof options.icons === 'object') {{
            this.googleAdsLocations.icons = {{ ...this.googleAdsLocations.icons, ...options.icons }};
        }}

        if (options.colors && typeof options.colors === 'object') {{
            this.googleAdsLocations.colors = {{ ...this.googleAdsLocations.colors, ...options.colors }};
        }}

        if (options.aliases && typeof options.aliases === 'object') {{
            this.googleAdsLocations.aliases = {{ ...this.googleAdsLocations.aliases, ...options.aliases }};
        }}

        if (typeof options.caseSensitive === 'boolean') {{
            this.googleAdsLocations.caseSensitive = options.caseSensitive;
        }}

        if (typeof options.partialMatch === 'boolean') {{
            this.googleAdsLocations.partialMatch = options.partialMatch;
        }}

        if (options.defaultIcon) {{
            this.googleAdsLocations.defaultIcon = options.defaultIcon;
        }}

        if (options.defaultColor) {{
            this.googleAdsLocations.defaultColor = options.defaultColor;
        }}

        console.log('[CONFIG] Updated Google Ads locations:', this.googleAdsLocations.names);
        console.log('[CONFIG] Location configuration updated successfully');

        return true;
    }},

    /**
     * Get current Google Ads location configuration
     */
    getGoogleAdsLocations: function() {{
        return this.googleAdsLocations;
    }},

    // Tab visibility management
    updateTabVisibility: function(dataAvailability = {{}}) {{
        console.log('[CONFIG] Updating tab visibility based on config and data availability');

        const tabMappings = {{
            'pos': [
                // Sales Report tab button and content (POS is integrated into Sales Report)
                'button[data-tab="sales-report"]', // Tab button
                'sales-report', // Tab content
                // POS sections within Sales Report tab (actual HTML elements)
                '#locationRevenueChart', '#locationTransactionsChart',
                '.chart-card:has(#locationRevenueChart)', '.chart-card:has(#locationTransactionsChart)'
            ],
            'metaAds': [
                'button[data-tab="meta-ads-report"]', // Tab button
                'meta-ads-report' // Tab content
            ],
            'googleAds': [
                'button[data-tab="google-ads-report"]', // Tab button
                'google-ads-report' // Tab content
            ]
        }};

        // Check each data source
        Object.keys(tabMappings).forEach(dataSource => {{
            const isEnabled = this.isEnabled(dataSource);
            const hasData = dataAvailability[dataSource] || false;
            const shouldShow = this.shouldShowTab(dataSource, hasData);

            const elements = tabMappings[dataSource];
            this.updateTabElements(elements, shouldShow, dataSource);
        }});
    }},

    shouldShowTab: function(dataSource, hasData = false) {{
        // Check if data source is enabled in configuration
        if (!this.isEnabled(dataSource)) {{
            console.log(`[CONFIG] Hiding ${{dataSource}} tab: disabled in configuration`);
            return false;
        }}

        // For enabled sources, check if they have data
        if (!hasData) {{
            console.log(`[CONFIG] Hiding ${{dataSource}} tab: enabled but no data available`);
            return false;
        }}

        // Show if enabled and has data (or data status unknown)
        console.log(`[CONFIG] Showing ${{dataSource}} tab: enabled and has data`);
        return true;
    }},

    updateTabElements: function(elements, shouldShow, dataSource) {{
        elements.forEach(selector => {{
            // Handle different selector types
            let element;
            if (selector.startsWith('button[')) {{
                element = document.querySelector(selector);
            }} else if (selector.startsWith('.') || selector.startsWith('#')) {{
                // CSS selector (class or ID)
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {{
                    if (shouldShow) {{
                        el.style.display = '';
                        el.classList.remove('hidden');
                    }} else {{
                        el.style.display = 'none';
                        el.classList.add('hidden');
                        console.log(`[CONFIG] Hidden element: ${{selector}} (${{dataSource}})`);
                    }}
                }});
                return; // Skip the single element logic below
            }} else {{
                element = document.getElementById(selector);
            }}

            if (element) {{
                if (shouldShow) {{
                    element.style.display = '';
                    element.classList.remove('hidden');
                }} else {{
                    element.style.display = 'none';
                    element.classList.add('hidden');
                    console.log(`[CONFIG] Hidden tab element: ${{selector}} (${{dataSource}})`);
                }}
            }}
        }});
    }},

    // Special function to handle POS content within Sales Report
    updateSalesReportPOSContent: function(hasData = false) {{
        const shouldShow = this.shouldShowTab('pos', hasData);

        if (!shouldShow) {{
            // Hide POS-specific content and show alternative message
            console.log('[CONFIG] Hiding POS content in Sales Report...');

            // Hide the Revenue by Location chart
            const revenueChart = document.getElementById('locationRevenueChart');
            if (revenueChart) {{
                const revenueCard = revenueChart.closest('.chart-card');
                if (revenueCard) {{
                    revenueCard.style.display = 'none';
                    revenueCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Revenue by Location chart');
                }}
            }}

            // Hide the Transactions by Location chart
            const transactionsChart = document.getElementById('locationTransactionsChart');
            if (transactionsChart) {{
                const transactionsCard = transactionsChart.closest('.chart-card');
                if (transactionsCard) {{
                    transactionsCard.style.display = 'none';
                    transactionsCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Transactions by Location chart');
                }}
            }}

            // Add a replacement message for the hidden POS content
            const chartRow = document.querySelector('#sales-report .chart-row');
            if (chartRow) {{
                // Remove existing POS disabled message if any
                const existingMessage = chartRow.querySelector('.pos-disabled-message');
                if (existingMessage) {{
                    existingMessage.remove();
                }}

                // Add a message explaining POS is not available for this client
                const posMessage = document.createElement('div');
                posMessage.className = 'pos-disabled-message chart-card';
                posMessage.style.cssText = 'text-align: center; padding: 2rem; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 1rem 0;';
                posMessage.innerHTML = `
                    <h3 style="color: #6c757d; margin-bottom: 1rem;">📊 Sales Report - Lead Data Only</h3>
                    <p style="color: #6c757d; margin: 0;">This dashboard is configured to show lead conversion data. POS integration is not enabled for this client.</p>
                `;

                // Add the message to replace the hidden charts
                chartRow.appendChild(posMessage);
                console.log('[CONFIG] Added POS disabled message to Sales Report');
            }}

            console.log('[CONFIG] POS content hidden in Sales Report - showing lead-only view');
        }}
    }}
}};

// Initialize configuration when DOM is ready
document.addEventListener('DOMContentLoaded', function() {{
    // Clear any cached data for fresh start
    window.CLIENT_CONFIG.clearCache();

    // Update store name in header when DOM is ready
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement && window.CLIENT_CONFIG.storeName) {{
        storeNameElement.textContent = window.CLIENT_CONFIG.storeName;
        console.log('[CONFIG] Updated store name in header:', window.CLIENT_CONFIG.storeName);
    }}
}});

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Base ID:', window.CLIENT_CONFIG.getBaseId());
'''.format(
            client_name=config['client_name'],
            client_name_upper=config['client_name'].upper(),
            business_name=config['business_name'],
            store_name=config['store_name'],
            base_id=config['base_id'],
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            output_dir_name=output_dir.name,
            table_ids_section=table_ids_section.rstrip(),
            enabled_sources_json=json.dumps(enabled_sources),
            disabled_sources_json=json.dumps(disabled_sources),
            location_names_json=json.dumps(location_config['names']),
            location_icons_json=json.dumps(location_config['icons']),
            location_colors_json=json.dumps(location_config['colors'])
        )

        return content

    def _clean_hardcoded_locations(self, output_dir):
        """Remove hardcoded location options from HTML file"""
        html_file = output_dir / 'index.html'

        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Remove hardcoded location options from all location dropdowns
        # Keep only "All Locations" option, remove all specific location options

        # Pattern to match location option elements (but not "All Locations")
        location_option_pattern = r'<option value="(?!all)[^"]*">[^<]*</option>\s*'

        # Find all location dropdowns and clean them
        dropdown_ids = ['location-filter', 'report-location', 'compare-location']

        for dropdown_id in dropdown_ids:
            # Find the select element for this dropdown
            select_start = content.find(f'<select id="{dropdown_id}"')
            if select_start == -1:
                continue

            # Find the closing </select> tag
            select_end = content.find('</select>', select_start)
            if select_end == -1:
                continue

            # Extract the select element content
            select_content = content[select_start:select_end + 9]  # +9 for </select>

            # Clean the select content - remove all hardcoded location options
            cleaned_select = re.sub(location_option_pattern, '', select_content, flags=re.MULTILINE)

            # Ensure proper formatting - add newlines after "All Locations" option
            cleaned_select = cleaned_select.replace(
                '<option value="all">All Locations</option>',
                '<option value="all">All Locations</option>\n                            <!-- Dynamic locations will be populated by JavaScript -->'
            )

            # Replace the original select with the cleaned version
            content = content.replace(select_content, cleaned_select)

            self.log_message(f"🧹 Cleaned hardcoded locations from {dropdown_id}")

        # Write the cleaned HTML back
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)

        self.log_message("🧹 Removed all hardcoded locations from HTML")

    def _update_server_config(self, output_dir, config):
        """Update server configuration files"""
        # Update config.py if it exists
        config_py_file = output_dir / 'config.py'
        if config_py_file.exists():
            with open(config_py_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Update any hardcoded values in config.py
            content = re.sub(
                r"CLIENT_NAME = '[^']*'",
                f"CLIENT_NAME = '{config['client_name']}'",
                content
            )

            with open(config_py_file, 'w', encoding='utf-8') as f:
                f.write(content)

    def _verify_generated_dashboard(self, output_dir, config):
        """Comprehensive verification of generated dashboard"""
        verification_results = {
            'files_exist': [],
            'config_accuracy': [],
            'table_ids_correct': [],
            'store_name_updated': [],
            'enabled_sources_correct': [],
            'errors': []
        }

        try:
            # Verify required files exist
            required_files = ['client-config.js', 'index.html', 'script.js', 'server.py', 'styles.css']
            for file in required_files:
                file_path = output_dir / file
                if file_path.exists():
                    verification_results['files_exist'].append(f"✅ {file}")
                    self.log_message(f"✅ File exists: {file}")
                else:
                    verification_results['files_exist'].append(f"❌ {file}")
                    verification_results['errors'].append(f"Missing file: {file}")
                    self.log_message(f"❌ Missing file: {file}")

            # Verify client-config.js content
            config_file = output_dir / 'client-config.js'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check client name
                if f"clientName: '{config['client_name']}'" in content:
                    verification_results['config_accuracy'].append("✅ Client name updated")
                    self.log_message("✅ Client name verified")
                else:
                    verification_results['config_accuracy'].append("❌ Client name not updated")
                    verification_results['errors'].append("Client name not properly updated")
                    self.log_message("❌ Client name not updated")

                # Check store name
                if f"storeName: '{config['store_name']}'" in content:
                    verification_results['store_name_updated'].append("✅ Store name updated")
                    self.log_message("✅ Store name verified")
                else:
                    verification_results['store_name_updated'].append("❌ Store name not updated")
                    verification_results['errors'].append("Store name not properly updated")
                    self.log_message("❌ Store name not updated")

                # Check base ID
                if f"baseId: '{config['base_id']}'" in content:
                    verification_results['config_accuracy'].append("✅ Base ID updated")
                    self.log_message("✅ Base ID verified")
                else:
                    verification_results['config_accuracy'].append("❌ Base ID not updated")
                    verification_results['errors'].append("Base ID not properly updated")
                    self.log_message("❌ Base ID not updated")

                # Check table IDs
                for source, table_id in config['table_ids'].items():
                    if table_id:
                        if f"{source}: '{table_id}'" in content:
                            verification_results['table_ids_correct'].append(f"✅ {source}: {table_id}")
                            self.log_message(f"✅ {source} table ID verified")
                        else:
                            verification_results['table_ids_correct'].append(f"❌ {source}: {table_id}")
                            verification_results['errors'].append(f"Table ID for {source} not properly updated")
                            self.log_message(f"❌ {source} table ID not updated")
                    else:
                        if f"{source}: null" in content:
                            verification_results['table_ids_correct'].append(f"✅ {source}: disabled")
                            self.log_message(f"✅ {source} properly disabled")
                        else:
                            verification_results['table_ids_correct'].append(f"❌ {source}: not disabled")
                            verification_results['errors'].append(f"Table ID for {source} not properly disabled")
                            self.log_message(f"❌ {source} not properly disabled")

                # Check enabled sources
                enabled_sources_str = json.dumps(config['enabled_sources'])
                if f'enabled: {enabled_sources_str}' in content:
                    verification_results['enabled_sources_correct'].append("✅ Enabled sources updated")
                    self.log_message("✅ Enabled sources verified")
                else:
                    verification_results['enabled_sources_correct'].append("❌ Enabled sources not updated")
                    verification_results['errors'].append("Enabled sources not properly updated")
                    self.log_message("❌ Enabled sources not updated")

        except Exception as e:
            verification_results['errors'].append(f"Verification error: {str(e)}")
            self.log_message(f"❌ Verification error: {e}")

        # Log verification summary
        error_count = len(verification_results['errors'])
        if error_count == 0:
            self.log_message("✅ All verification checks passed!")
        else:
            self.log_message(f"⚠️ {error_count} verification issues found")

        return verification_results

    def _generate_summary_report(self, output_dir, config, verification_results, location_config):
        """Generate a comprehensive summary report"""
        report_content = f"""
# Dashboard Generation Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Client Configuration
- **Client Name**: {config['client_name']}
- **Business Name**: {config['business_name']}
- **Store Name**: {config['store_name']}
- **Base ID**: {config['base_id']}
- **Enabled Sources**: {', '.join(config['enabled_sources'])}

## Table IDs
"""

        for source, table_id in config['table_ids'].items():
            status = "✅ Enabled" if table_id else "❌ Disabled"
            report_content += f"- **{source.upper()}**: {table_id or 'null'} {status}\n"

        report_content += f"""

## Verification Results

### Files Created
"""
        for result in verification_results['files_exist']:
            report_content += f"- {result}\n"

        report_content += "\n### Configuration Accuracy\n"
        for result in verification_results['config_accuracy']:
            report_content += f"- {result}\n"

        report_content += "\n### Table IDs\n"
        for result in verification_results['table_ids_correct']:
            report_content += f"- {result}\n"

        report_content += "\n### Store Name\n"
        for result in verification_results['store_name_updated']:
            report_content += f"- {result}\n"

        report_content += "\n### Enabled Sources\n"
        for result in verification_results['enabled_sources_correct']:
            report_content += f"- {result}\n"

        if verification_results['errors']:
            report_content += "\n## ❌ Errors Found\n"
            for error in verification_results['errors']:
                report_content += f"- {error}\n"
        else:
            report_content += "\n## ✅ No Errors Found\n"

        report_content += f"""

## Next Steps
1. Navigate to: `{output_dir}`
2. Install dependencies: `pip install -r requirements.txt`
3. Start server: `python server.py`
4. Open browser: `http://localhost:8000`

## Google Ads Location Configuration
- **Configured Locations**: {', '.join(location_config['names'])}
- **Location Icons**: {', '.join([f"{loc}: {icon}" for loc, icon in location_config['icons'].items()])}
- **Location Colors**: {', '.join([f"{loc}: {color}" for loc, color in location_config['colors'].items()])}

### Testing Location Cards
To test the location-based Google Ads analytics:
1. Navigate to the Google Ads tab
2. Verify location cards appear below the main summary
3. Test with browser console: `testLocationAnalytics()`
4. Verify location extraction: `getLocationMetrics()`

## Testing Checklist
- [ ] Dashboard loads without errors
- [ ] Store name appears in header: "{config['store_name']}"
- [ ] Only enabled tabs are visible: {', '.join(config['enabled_sources'])}
- [ ] Data loads correctly for enabled sources
- [ ] No 404 errors for disabled sources
- [ ] Location cards appear in Google Ads tab (if Google Ads enabled)
- [ ] Location extraction works with campaign names

---
Generated by RL Dashboard Customizer v3.0 Complete
"""

        report_file = output_dir / 'GENERATION_REPORT.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.log_message(f"📄 Summary report saved: {report_file}")

    def view_generation_report(self):
        """View the generation report"""
        if not hasattr(self, 'verification_results') or not self.verification_results:
            messagebox.showinfo("Info", "No generation report available. Generate a dashboard first.")
            return

        # Find the most recent report
        report_files = list(Path(".").glob("*_dashboard/GENERATION_REPORT.md"))
        if not report_files:
            messagebox.showinfo("Info", "No generation report found.")
            return

        # Get the most recent report
        latest_report = max(report_files, key=lambda p: p.stat().st_mtime)

        # Open report in a new window
        report_window = tk.Toplevel(self.root)
        report_window.title("Generation Report")
        report_window.geometry("800x600")

        report_text = scrolledtext.ScrolledText(report_window, wrap=tk.WORD)
        report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        try:
            with open(latest_report, 'r', encoding='utf-8') as f:
                content = f.read()
            report_text.insert(tk.END, content)
            report_text.config(state=tk.DISABLED)
        except Exception as e:
            report_text.insert(tk.END, f"Error loading report: {e}")

    # === CLIENT DATABASE METHODS ===

    def init_client_database(self):
        """Initialize the client database with backup and migration support"""
        try:
            self.db_path = "clients.db"

            # CRITICAL: Backup existing database before any changes
            if os.path.exists(self.db_path):
                backup_path = f"clients_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2(self.db_path, backup_path)
                print(f"✅ Database backed up to: {backup_path}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if table exists and get current schema
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='clients'")
            table_exists = cursor.fetchone() is not None

            if table_exists:
                # Get current schema
                cursor.execute("PRAGMA table_info(clients)")
                current_columns = {row[1]: row[2] for row in cursor.fetchall()}
                print(f"📊 Existing database found with columns: {list(current_columns.keys())}")

                # Check if we need to add new columns for future features
                required_columns = {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'store_name': 'TEXT NOT NULL UNIQUE',
                    'customer_id': 'TEXT NOT NULL',
                    'business_name': 'TEXT',
                    'airtable_base_id': 'TEXT',  # Airtable Base ID for this client
                    'airtable_google_ads_table_id': 'TEXT',  # Google Ads table ID
                    'airtable_ghl_table_id': 'TEXT',  # GHL table ID (placeholder for future)
                    'notes': 'TEXT',
                    'created_date': 'TEXT',
                    'last_updated': 'TEXT'
                }

                # Add missing columns if needed (for future schema updates)
                for col_name, col_type in required_columns.items():
                    if col_name not in current_columns and col_name != 'id':
                        try:
                            cursor.execute(f"ALTER TABLE clients ADD COLUMN {col_name} {col_type}")
                            print(f"✅ Added missing column: {col_name}")
                        except sqlite3.OperationalError as e:
                            if "duplicate column name" not in str(e):
                                print(f"⚠️ Could not add column {col_name}: {e}")
            else:
                # Create new table
                cursor.execute('''
                    CREATE TABLE clients (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        store_name TEXT NOT NULL UNIQUE,
                        customer_id TEXT NOT NULL,
                        business_name TEXT,
                        airtable_base_id TEXT,
                        airtable_google_ads_table_id TEXT,
                        airtable_ghl_table_id TEXT,
                        notes TEXT,
                        created_date TEXT,
                        last_updated TEXT
                    )
                ''')
                print("✅ Created new clients table")

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"❌ Error initializing client database: {e}")
            # Try to restore from backup if available
            self.attempt_database_recovery()

    def attempt_database_recovery(self):
        """Attempt to recover database from backup files"""
        try:
            # Look for backup files
            backup_files = [f for f in os.listdir('.') if f.startswith('clients_backup_') and f.endswith('.db')]

            if backup_files:
                # Use the most recent backup
                latest_backup = max(backup_files, key=lambda x: os.path.getmtime(x))
                print(f"🔄 Attempting recovery from: {latest_backup}")

                # Copy backup to main database
                shutil.copy2(latest_backup, self.db_path)
                print(f"✅ Database recovered from backup: {latest_backup}")
                return True
            else:
                print("⚠️ No backup files found for recovery")
                return False

        except Exception as e:
            print(f"❌ Database recovery failed: {e}")
            return False

    def list_database_backups(self):
        """List available database backup files"""
        try:
            backup_files = [f for f in os.listdir('.') if f.startswith('clients_backup_') and f.endswith('.db')]
            backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            if backup_files:
                print("📁 Available database backups:")
                for i, backup in enumerate(backup_files, 1):
                    mod_time = datetime.fromtimestamp(os.path.getmtime(backup))
                    size = os.path.getsize(backup)
                    print(f"  {i}. {backup} ({mod_time.strftime('%Y-%m-%d %H:%M:%S')}, {size} bytes)")
            else:
                print("📁 No database backups found")

            return backup_files

        except Exception as e:
            print(f"❌ Error listing backups: {e}")
            return []

    def restore_from_backup(self, backup_filename):
        """Restore database from a specific backup file"""
        try:
            if not os.path.exists(backup_filename):
                print(f"❌ Backup file not found: {backup_filename}")
                return False

            # Create current backup before restore
            if os.path.exists(self.db_path):
                current_backup = f"clients_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2(self.db_path, current_backup)
                print(f"💾 Current database backed up to: {current_backup}")

            # Restore from backup
            shutil.copy2(backup_filename, self.db_path)
            print(f"✅ Database restored from: {backup_filename}")

            # Verify the restored database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM clients")
            count = cursor.fetchone()[0]
            conn.close()

            print(f"📊 Restored database contains {count} clients")
            return True

        except Exception as e:
            print(f"❌ Restore failed: {e}")
            return False

    def add_client_to_db(self, store_name, customer_id, business_name="", notes="",
                        airtable_base_id="", airtable_google_ads_table_id="", airtable_ghl_table_id=""):
        """Add a client to the database with all new fields"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute('''
                INSERT OR REPLACE INTO clients
                (store_name, customer_id, business_name, airtable_base_id,
                 airtable_google_ads_table_id, airtable_ghl_table_id, notes, created_date, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (store_name, customer_id, business_name, airtable_base_id,
                  airtable_google_ads_table_id, airtable_ghl_table_id, notes, current_time, current_time))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"Error adding client to database: {e}")
            return False

    def get_all_clients(self):
        """Get all clients from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM clients ORDER BY store_name')
            clients = cursor.fetchall()

            conn.close()
            return clients

        except Exception as e:
            print(f"Error getting clients from database: {e}")
            return []

    def search_clients(self, search_term):
        """Search clients by store name or business name"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM clients
                WHERE store_name LIKE ? OR business_name LIKE ? OR customer_id LIKE ?
                ORDER BY store_name
            ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

            clients = cursor.fetchall()
            conn.close()
            return clients

        except Exception as e:
            print(f"Error searching clients: {e}")
            return []

    def delete_client(self, client_id):
        """Delete a client from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM clients WHERE id = ?', (client_id,))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"Error deleting client: {e}")
            return False

    def export_client_data(self):
        """Export client data to various formats"""
        # Create export dialog
        export_dialog = tk.Toplevel(self.root)
        export_dialog.title("Export Client Data")
        export_dialog.geometry("500x400")
        export_dialog.transient(self.root)
        export_dialog.grab_set()

        # Center the dialog
        export_dialog.update_idletasks()
        x = (export_dialog.winfo_screenwidth() // 2) - (export_dialog.winfo_width() // 2)
        y = (export_dialog.winfo_screenheight() // 2) - (export_dialog.winfo_height() // 2)
        export_dialog.geometry(f"+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(export_dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="📤 Export Client Data",
                 font=("Segoe UI", 14, "bold")).pack(pady=(0, 20))

        # Format selection
        format_frame = ttk.LabelFrame(main_frame, text="Export Format", padding=10)
        format_frame.pack(fill=tk.X, pady=(0, 20))

        export_format = tk.StringVar(value="text")

        ttk.Radiobutton(format_frame, text="📄 Text with Bullets (• Business Name (Customer ID: 123-456-7890))",
                       variable=export_format, value="text").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(format_frame, text="📊 CSV (Comma Separated Values)",
                       variable=export_format, value="csv").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(format_frame, text="🔧 JSON (JavaScript Object Notation)",
                       variable=export_format, value="json").pack(anchor=tk.W, pady=2)

        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Export Options", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 20))

        include_notes = tk.BooleanVar(value=True)
        include_dates = tk.BooleanVar(value=True)
        sort_by_name = tk.BooleanVar(value=True)

        ttk.Checkbutton(options_frame, text="Include notes", variable=include_notes).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="Include dates", variable=include_dates).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="Sort by store name", variable=sort_by_name).pack(anchor=tk.W)

        # Progress frame (initially hidden)
        progress_frame = ttk.Frame(main_frame)
        progress_var = tk.StringVar(value="")
        progress_label = ttk.Label(progress_frame, textvariable=progress_var)
        progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')

        def do_export():
            """Perform the export operation"""
            try:
                # Show progress
                progress_frame.pack(fill=tk.X, pady=(0, 10))
                progress_label.pack()
                progress_bar.pack(fill=tk.X, pady=(5, 0))
                progress_bar.start()
                progress_var.set("Preparing export...")
                export_dialog.update()

                # Get all clients
                clients = self.get_all_clients()
                if not clients:
                    messagebox.showwarning("No Data", "No clients found to export.")
                    return

                # Sort if requested
                if sort_by_name.get():
                    clients = sorted(clients, key=lambda x: x[1].lower())  # Sort by store_name

                # Choose file location
                format_val = export_format.get()
                if format_val == "text":
                    file_extension = "txt"
                    file_types = [("Text files", "*.txt"), ("All files", "*.*")]
                elif format_val == "csv":
                    file_extension = "csv"
                    file_types = [("CSV files", "*.csv"), ("All files", "*.*")]
                else:  # json
                    file_extension = "json"
                    file_types = [("JSON files", "*.json"), ("All files", "*.*")]

                from datetime import datetime
                default_filename = f"clients_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"

                from tkinter import filedialog
                file_path = filedialog.asksaveasfilename(
                    title="Save Export File",
                    defaultextension=f".{file_extension}",
                    filetypes=file_types,
                    initialname=default_filename
                )

                if not file_path:
                    return  # User cancelled

                progress_var.set("Generating export file...")
                export_dialog.update()

                # Generate export content
                if format_val == "text":
                    content = self.generate_text_export(clients, include_notes.get(), include_dates.get())
                elif format_val == "csv":
                    content = self.generate_csv_export(clients, include_notes.get(), include_dates.get())
                else:  # json
                    content = self.generate_json_export(clients, include_notes.get(), include_dates.get())

                # Write file
                progress_var.set("Writing file...")
                export_dialog.update()

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                # Success
                progress_bar.stop()
                progress_frame.pack_forget()

                messagebox.showinfo("Export Complete",
                                  f"Successfully exported {len(clients)} clients to:\n{file_path}")
                export_dialog.destroy()

            except Exception as e:
                progress_bar.stop()
                progress_frame.pack_forget()
                messagebox.showerror("Export Error", f"Failed to export data:\n{str(e)}")

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="📤 Export", command=do_export).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancel", command=export_dialog.destroy).pack(side=tk.RIGHT)

    def generate_text_export(self, clients, include_notes, include_dates):
        """Generate text export with bullet format"""
        lines = []
        lines.append("CLIENT LIST EXPORT")
        lines.append("=" * 50)
        lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Total Clients: {len(clients)}")
        lines.append("")

        for client in clients:
            # client tuple: (id, store_name, customer_id, business_name, notes, created_date, last_updated)
            store_name = client[1]
            customer_id = client[2]
            business_name = client[3] or store_name
            notes = client[4]
            created_date = client[5]

            # Format customer ID with dashes for readability
            if customer_id and len(customer_id) >= 9:
                formatted_id = f"{customer_id[:3]}-{customer_id[3:6]}-{customer_id[6:]}"
            else:
                formatted_id = customer_id

            # Main bullet point
            lines.append(f"• {business_name} (Customer ID: {formatted_id})")

            if include_notes and notes:
                lines.append(f"  Notes: {notes}")

            if include_dates and created_date:
                lines.append(f"  Added: {created_date}")

            lines.append("")  # Empty line between clients

        return "\n".join(lines)

    def generate_csv_export(self, clients, include_notes, include_dates):
        """Generate CSV export"""
        import csv
        import io

        output = io.StringIO()

        # Determine headers
        headers = ["Store Name", "Business Name", "Customer ID"]
        if include_notes:
            headers.append("Notes")
        if include_dates:
            headers.extend(["Created Date", "Last Updated"])

        writer = csv.writer(output)
        writer.writerow(headers)

        for client in clients:
            # client tuple: (id, store_name, customer_id, business_name, notes, created_date, last_updated)
            row = [
                client[1],  # store_name
                client[3] or client[1],  # business_name or store_name
                client[2]   # customer_id
            ]

            if include_notes:
                row.append(client[4] or "")  # notes

            if include_dates:
                row.append(client[5] or "")  # created_date
                row.append(client[6] or "")  # last_updated

            writer.writerow(row)

        return output.getvalue()

    def generate_json_export(self, clients, include_notes, include_dates):
        """Generate JSON export"""
        import json

        export_data = {
            "export_info": {
                "generated": datetime.now().isoformat(),
                "total_clients": len(clients),
                "format_version": "1.0"
            },
            "clients": []
        }

        for client in clients:
            # client tuple: (id, store_name, customer_id, business_name, notes, created_date, last_updated)
            client_data = {
                "store_name": client[1],
                "business_name": client[3] or client[1],
                "customer_id": client[2]
            }

            if include_notes:
                client_data["notes"] = client[4] or ""

            if include_dates:
                client_data["created_date"] = client[5] or ""
                client_data["last_updated"] = client[6] or ""

            export_data["clients"].append(client_data)

        return json.dumps(export_data, indent=2, ensure_ascii=False)

    def show_database_management_dialog(self):
        """Show database backup and recovery options"""
        db_dialog = tk.Toplevel(self.root)
        db_dialog.title("Database Management")
        db_dialog.geometry("600x500")
        db_dialog.transient(self.root)
        db_dialog.grab_set()

        # Center the dialog
        db_dialog.update_idletasks()
        x = (db_dialog.winfo_screenwidth() // 2) - (db_dialog.winfo_width() // 2)
        y = (db_dialog.winfo_screenheight() // 2) - (db_dialog.winfo_height() // 2)
        db_dialog.geometry(f"+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(db_dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="🔧 Database Management",
                 font=("Segoe UI", 14, "bold")).pack(pady=(0, 20))

        # Current database info
        info_frame = ttk.LabelFrame(main_frame, text="Current Database", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        try:
            if os.path.exists(self.db_path):
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM clients")
                count = cursor.fetchone()[0]
                conn.close()

                size = os.path.getsize(self.db_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(self.db_path))

                info_text = f"📊 Records: {count}\n📁 Size: {size} bytes\n🕒 Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}"
            else:
                info_text = "❌ No database file found"
        except Exception as e:
            info_text = f"❌ Error reading database: {e}"

        ttk.Label(info_frame, text=info_text, font=("Segoe UI", 10)).pack(anchor=tk.W)

        # Backup section
        backup_frame = ttk.LabelFrame(main_frame, text="Backup Management", padding=10)
        backup_frame.pack(fill=tk.X, pady=(0, 20))

        def create_backup():
            try:
                backup_path = f"clients_manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2(self.db_path, backup_path)
                messagebox.showinfo("Backup Created", f"Database backed up to:\n{backup_path}")
                refresh_backup_list()
            except Exception as e:
                messagebox.showerror("Backup Failed", f"Failed to create backup:\n{e}")

        ttk.Button(backup_frame, text="💾 Create Manual Backup", command=create_backup).pack(pady=5)

        # Backup list
        backup_list_frame = ttk.Frame(backup_frame)
        backup_list_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        ttk.Label(backup_list_frame, text="Available Backups:", font=("Segoe UI", 10, "bold")).pack(anchor=tk.W)

        backup_listbox = tk.Listbox(backup_list_frame, height=6)
        backup_listbox.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        def refresh_backup_list():
            backup_listbox.delete(0, tk.END)
            backup_files = self.list_database_backups()
            for backup in backup_files:
                mod_time = datetime.fromtimestamp(os.path.getmtime(backup))
                size = os.path.getsize(backup)
                display_text = f"{backup} ({mod_time.strftime('%Y-%m-%d %H:%M:%S')}, {size} bytes)"
                backup_listbox.insert(tk.END, display_text)

        def restore_selected():
            selection = backup_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a backup to restore.")
                return

            backup_files = self.list_database_backups()
            selected_backup = backup_files[selection[0]]

            if messagebox.askyesno("Confirm Restore",
                                 f"Are you sure you want to restore from:\n{selected_backup}\n\n"
                                 "This will replace your current database!"):
                if self.restore_from_backup(selected_backup):
                    messagebox.showinfo("Restore Complete", "Database restored successfully!")
                    db_dialog.destroy()
                else:
                    messagebox.showerror("Restore Failed", "Failed to restore database.")

        # Restore button
        restore_frame = ttk.Frame(backup_list_frame)
        restore_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(restore_frame, text="🔄 Restore Selected", command=restore_selected).pack(side=tk.LEFT)
        ttk.Button(restore_frame, text="🔄 Refresh List", command=refresh_backup_list).pack(side=tk.RIGHT)

        # Initial load
        refresh_backup_list()

        # Close button
        ttk.Button(main_frame, text="❌ Close", command=db_dialog.destroy).pack(pady=(20, 0))

    def manage_clients(self):
        """Open client management window"""
        client_window = tk.Toplevel(self.root)
        client_window.title("Client Management")
        client_window.geometry("800x600")
        client_window.transient(self.root)
        client_window.grab_set()

        # Main frame
        main_frame = ttk.Frame(client_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="👥 Client Management",
                               font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Search frame
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="Search:", font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 5))

        def search_clients():
            search_term = search_var.get().strip()
            if search_term:
                clients = self.search_clients(search_term)
            else:
                clients = self.get_all_clients()
            update_client_list(clients)

        search_btn = ttk.Button(search_frame, text="🔍 Search", command=search_clients)
        search_btn.pack(side=tk.LEFT, padx=(0, 5))

        refresh_btn = ttk.Button(search_frame, text="🔄 Refresh",
                                command=lambda: update_client_list(self.get_all_clients()))
        refresh_btn.pack(side=tk.LEFT)

        # Client list frame
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Treeview for client list
        columns = ("ID", "Store Name", "Customer ID", "Business Name", "Base ID", "Google Ads Table", "GHL Table", "Last Updated")
        client_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # Configure columns
        client_tree.heading("ID", text="ID")
        client_tree.heading("Store Name", text="Store Name")
        client_tree.heading("Customer ID", text="Customer ID")
        client_tree.heading("Business Name", text="Business Name")
        client_tree.heading("Base ID", text="Base ID")
        client_tree.heading("Google Ads Table", text="Google Ads Table")
        client_tree.heading("GHL Table", text="GHL Table")
        client_tree.heading("Last Updated", text="Last Updated")

        client_tree.column("ID", width=50)
        client_tree.column("Store Name", width=150)
        client_tree.column("Customer ID", width=120)
        client_tree.column("Business Name", width=150)
        client_tree.column("Base ID", width=180)
        client_tree.column("Google Ads Table", width=180)
        client_tree.column("GHL Table", width=180)
        client_tree.column("Last Updated", width=140)

        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=client_tree.yview)
        client_tree.configure(yscrollcommand=scrollbar.set)

        client_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def update_client_list(clients):
            """Update the client list display"""
            for item in client_tree.get_children():
                client_tree.delete(item)

            for client in clients:
                # Map database columns to display columns
                # Database: [id, store_name, customer_id, business_name, notes, created_date, last_updated, account_id, airtable_base_id, airtable_google_ads_table_id, airtable_ghl_table_id]
                # Display: ("ID", "Store Name", "Customer ID", "Business Name", "Base ID", "Google Ads Table", "GHL Table", "Last Updated")

                client_id = client[0] if len(client) > 0 else ""
                store_name = client[1] if len(client) > 1 else ""
                customer_id = client[2] if len(client) > 2 else ""
                business_name = client[3] if len(client) > 3 else ""
                # Skip notes (index 4), created_date (index 5)
                last_updated = client[6] if len(client) > 6 else ""
                # Skip account_id (index 7) - we removed this
                airtable_base_id = client[8] if len(client) > 8 else ""
                airtable_google_ads_table_id = client[9] if len(client) > 9 else ""
                airtable_ghl_table_id = client[10] if len(client) > 10 else ""

                # Format the display values (no truncation - show full IDs)
                display_values = (
                    client_id,
                    store_name,
                    customer_id,
                    business_name,
                    airtable_base_id or "",  # Show full Base ID
                    airtable_google_ads_table_id or "",  # Show full Google Ads Table ID
                    airtable_ghl_table_id or "",  # Show full GHL Table ID
                    last_updated
                )

                client_tree.insert("", tk.END, values=display_values)

        # Load initial client list
        update_client_list(self.get_all_clients())

        # Action buttons frame
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=(0, 10))

        def add_new_client():
            """Open add client dialog"""
            add_window = tk.Toplevel(client_window)
            add_window.title("Add New Client")
            add_window.geometry("600x700")
            add_window.transient(client_window)
            add_window.grab_set()

            # Form frame
            form_frame = ttk.Frame(add_window)
            form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # Form fields
            ttk.Label(form_frame, text="Store Name:", font=("Segoe UI", 9, "bold")).grid(
                row=0, column=0, sticky=tk.W, pady=(0, 5))
            store_name_var = tk.StringVar()
            ttk.Entry(form_frame, textvariable=store_name_var, width=30).grid(
                row=0, column=1, sticky=tk.EW, pady=(0, 5))

            ttk.Label(form_frame, text="Customer ID:", font=("Segoe UI", 9, "bold")).grid(
                row=1, column=0, sticky=tk.W, pady=(0, 5))
            customer_id_var = tk.StringVar()
            ttk.Entry(form_frame, textvariable=customer_id_var, width=30).grid(
                row=1, column=1, sticky=tk.EW, pady=(0, 5))

            ttk.Label(form_frame, text="Business Name:", font=("Segoe UI", 9, "bold")).grid(
                row=2, column=0, sticky=tk.W, pady=(0, 5))
            business_name_var = tk.StringVar()
            ttk.Entry(form_frame, textvariable=business_name_var, width=30).grid(
                row=2, column=1, sticky=tk.EW, pady=(0, 5))

            ttk.Label(form_frame, text="Airtable Base ID:", font=("Segoe UI", 9, "bold")).grid(
                row=3, column=0, sticky=tk.W, pady=(0, 5))
            airtable_base_id_var = tk.StringVar()
            base_entry = ttk.Entry(form_frame, textvariable=airtable_base_id_var, width=25)
            base_entry.grid(row=3, column=1, sticky=tk.EW, pady=(0, 5), padx=(0, 5))

            def select_base_and_auto_load():
                """Select base and auto-load table IDs"""
                # Store the original callback
                original_callback = getattr(self, '_client_base_callback', None)

                # Set a callback to auto-load tables after base selection
                def auto_load_callback():
                    base_id = airtable_base_id_var.get()
                    if base_id:
                        self.auto_load_tables_for_client(
                            base_id,
                            google_ads_table_id_var,
                            ghl_table_id_var,
                            meta_ads_table_id_var,
                            pos_table_id_var
                        )
                    # Restore original callback
                    if original_callback:
                        self._client_base_callback = original_callback
                    else:
                        delattr(self, '_client_base_callback')

                self._client_base_callback = auto_load_callback
                self.select_base_for_client(airtable_base_id_var)

            ttk.Button(form_frame, text="🔍", width=3,
                      command=select_base_and_auto_load).grid(row=3, column=2, pady=(0, 5))

            # Google Ads Table
            ttk.Label(form_frame, text="🔍 Google Ads Table ID:", font=("Segoe UI", 9, "bold")).grid(
                row=4, column=0, sticky=tk.W, pady=(0, 5))
            google_ads_table_id_var = tk.StringVar()
            gads_entry = ttk.Entry(form_frame, textvariable=google_ads_table_id_var, width=25)
            gads_entry.grid(row=4, column=1, sticky=tk.EW, pady=(0, 5), padx=(0, 5))
            ttk.Button(form_frame, text="🔍", width=3,
                      command=lambda: self.select_table_for_client(airtable_base_id_var, google_ads_table_id_var, "Google Ads")).grid(row=4, column=2, pady=(0, 5))

            # GHL Table
            ttk.Label(form_frame, text="📞 GHL Table ID:", font=("Segoe UI", 9, "bold")).grid(
                row=5, column=0, sticky=tk.W, pady=(0, 5))
            ghl_table_id_var = tk.StringVar()
            ghl_entry = ttk.Entry(form_frame, textvariable=ghl_table_id_var, width=25)
            ghl_entry.grid(row=5, column=1, sticky=tk.EW, pady=(0, 5), padx=(0, 5))
            ttk.Button(form_frame, text="🔍", width=3,
                      command=lambda: self.select_table_for_client(airtable_base_id_var, ghl_table_id_var, "GHL")).grid(row=5, column=2, pady=(0, 5))

            # Meta Ads Table (Placeholder)
            ttk.Label(form_frame, text="📘 Meta Ads Table ID:", font=("Segoe UI", 9, "bold"), foreground="gray").grid(
                row=6, column=0, sticky=tk.W, pady=(0, 5))
            meta_ads_table_id_var = tk.StringVar()
            meta_entry = ttk.Entry(form_frame, textvariable=meta_ads_table_id_var, width=25, state="disabled")
            meta_entry.grid(row=6, column=1, sticky=tk.EW, pady=(0, 5), padx=(0, 5))
            ttk.Button(form_frame, text="🔍", width=3, state="disabled").grid(row=6, column=2, pady=(0, 5))

            # POS Table (Placeholder)
            ttk.Label(form_frame, text="🏪 POS Table ID:", font=("Segoe UI", 9, "bold"), foreground="gray").grid(
                row=7, column=0, sticky=tk.W, pady=(0, 5))
            pos_table_id_var = tk.StringVar()
            pos_entry = ttk.Entry(form_frame, textvariable=pos_table_id_var, width=25, state="disabled")
            pos_entry.grid(row=7, column=1, sticky=tk.EW, pady=(0, 5), padx=(0, 5))
            ttk.Button(form_frame, text="🔍", width=3, state="disabled").grid(row=7, column=2, pady=(0, 5))

            ttk.Label(form_frame, text="Notes (Smart Parse):", font=("Segoe UI", 9, "bold")).grid(
                row=8, column=0, sticky=tk.W, pady=(0, 5))
            notes_text = tk.Text(form_frame, width=30, height=4)
            notes_text.grid(row=8, column=1, sticky=tk.EW, pady=(0, 10))

            # Smart parsing for notes
            def on_notes_change():
                notes_content = notes_text.get("1.0", tk.END).strip()
                if notes_content:
                    business_name, customer_id_parsed = self.parse_notes_for_client_info(notes_content)
                    if business_name:
                        # Auto-fill Business Name if empty
                        if not business_name_var.get():
                            business_name_var.set(business_name)
                        # Auto-fill Store Name if empty (same as business name)
                        if not store_name_var.get():
                            store_name_var.set(business_name)
                    if customer_id_parsed and not customer_id_var.get():
                        customer_id_var.set(customer_id_parsed)

            notes_text.bind('<KeyRelease>', lambda e: on_notes_change())
            notes_text.bind('<FocusOut>', lambda e: on_notes_change())

            form_frame.columnconfigure(1, weight=1)

            def save_client():
                store_name = store_name_var.get().strip()
                customer_id = customer_id_var.get().strip()
                business_name = business_name_var.get().strip()
                airtable_base_id = airtable_base_id_var.get().strip()
                google_ads_table_id = google_ads_table_id_var.get().strip()
                ghl_table_id = ghl_table_id_var.get().strip()
                notes = notes_text.get("1.0", tk.END).strip()

                if not store_name or not customer_id:
                    messagebox.showerror("Error", "Store Name and Customer ID are required!")
                    return

                if self.add_client_to_db(store_name, customer_id, business_name, notes,
                                       airtable_base_id, google_ads_table_id, ghl_table_id):
                    messagebox.showinfo("Success", "Client added successfully!")
                    add_window.destroy()
                    update_client_list(self.get_all_clients())
                else:
                    messagebox.showerror("Error", "Failed to add client!")

            # Buttons
            button_frame = ttk.Frame(form_frame)
            button_frame.grid(row=9, column=0, columnspan=3, pady=(20, 0), sticky=tk.EW)

            ttk.Button(button_frame, text="💾 Save", command=save_client).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="❌ Cancel", command=add_window.destroy).pack(side=tk.LEFT)

        def use_selected_client():
            """Use selected client's customer ID"""
            selection = client_tree.selection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a client first!")
                return

            item = client_tree.item(selection[0])
            values = item['values']
            customer_id = values[2]  # Customer ID is in column 2

            # Set the customer ID in the Google Ads form
            self.gads_customer_id.set(customer_id)
            messagebox.showinfo("Success", f"Customer ID {customer_id} has been loaded!")
            client_window.destroy()

        def delete_selected_client():
            """Delete selected client"""
            selection = client_tree.selection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a client to delete!")
                return

            item = client_tree.item(selection[0])
            values = item['values']
            client_id = values[0]  # ID is in column 0
            store_name = values[1]  # Store name is in column 1

            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{store_name}'?"):
                if self.delete_client(client_id):
                    messagebox.showinfo("Success", "Client deleted successfully!")
                    update_client_list(self.get_all_clients())
                else:
                    messagebox.showerror("Error", "Failed to delete client!")

        def export_clients():
            """Export client list to file"""
            self.export_client_data()

        def manage_database():
            """Open database management dialog"""
            self.show_database_management_dialog()

        # Action buttons
        ttk.Button(action_frame, text="➕ Add New Client", command=add_new_client).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="✅ Use Selected", command=use_selected_client).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="🗑️ Delete Selected", command=delete_selected_client).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="📤 Export", command=export_clients).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="🔧 Database", command=manage_database).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="❌ Close", command=client_window.destroy).pack(side=tk.RIGHT)

        # Bind double-click to use client
        client_tree.bind("<Double-1>", lambda e: use_selected_client())

    def load_customer_from_database(self):
        """Load customer from database for Google Ads Hub"""
        # Create customer selection window
        customer_window = tk.Toplevel(self.root)
        customer_window.title("Load Customer from Database")
        customer_window.geometry("1000x600")
        customer_window.transient(self.root)
        customer_window.grab_set()

        # Main frame
        main_frame = ttk.Frame(customer_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text="👥 Select Customer from Database",
                               font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Instructions
        instructions = ttk.Label(main_frame,
                                text="Select a customer to auto-load Customer ID and Airtable configuration:",
                                font=("Segoe UI", 9), foreground="#666666")
        instructions.pack(pady=(0, 10))

        # Client list frame
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Treeview for client list
        columns = ("ID", "Store Name", "Customer ID", "Business Name", "Base ID", "Google Ads Table", "GHL Table")
        client_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # Configure columns
        client_tree.heading("ID", text="ID")
        client_tree.heading("Store Name", text="Store Name")
        client_tree.heading("Customer ID", text="Customer ID")
        client_tree.heading("Business Name", text="Business Name")
        client_tree.heading("Base ID", text="Base ID")
        client_tree.heading("Google Ads Table", text="Google Ads Table")
        client_tree.heading("GHL Table", text="GHL Table")

        client_tree.column("ID", width=50)
        client_tree.column("Store Name", width=120)
        client_tree.column("Customer ID", width=120)
        client_tree.column("Business Name", width=120)
        client_tree.column("Base ID", width=150)
        client_tree.column("Google Ads Table", width=150)
        client_tree.column("GHL Table", width=150)

        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=client_tree.yview)
        client_tree.configure(yscrollcommand=scrollbar.set)

        client_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def update_client_list():
            """Update the client list display"""
            for item in client_tree.get_children():
                client_tree.delete(item)

            clients = self.get_all_clients()
            for client in clients:
                # Map database columns to display columns
                client_id = client[0] if len(client) > 0 else ""
                store_name = client[1] if len(client) > 1 else ""
                customer_id = client[2] if len(client) > 2 else ""
                business_name = client[3] if len(client) > 3 else ""
                last_updated = client[6] if len(client) > 6 else ""
                airtable_base_id = client[8] if len(client) > 8 else ""
                airtable_google_ads_table_id = client[9] if len(client) > 9 else ""
                airtable_ghl_table_id = client[10] if len(client) > 10 else ""

                # Format the display values
                display_values = (
                    client_id,
                    store_name,
                    customer_id,
                    business_name,
                    airtable_base_id or "",
                    airtable_google_ads_table_id or "",
                    airtable_ghl_table_id or ""
                )

                client_tree.insert("", tk.END, values=display_values)

        # Load initial client list
        update_client_list()

        def load_selected_customer():
            """Load selected customer into Google Ads Hub"""
            selection = client_tree.selection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select a customer first!")
                return

            item = client_tree.item(selection[0])
            values = item['values']

            # Extract values
            customer_id = values[2]  # Customer ID
            store_name = values[1]   # Store Name
            base_id = values[4]      # Base ID
            google_ads_table_id = values[5]  # Google Ads Table ID
            ghl_table_id = values[6]  # GHL Table ID

            # Set Customer ID in Google Ads Hub
            self.gads_customer_id.set(customer_id)

            # Store Airtable configuration for later use
            self.selected_customer_config = {
                'store_name': store_name,
                'customer_id': customer_id,
                'base_id': base_id,
                'google_ads_table_id': google_ads_table_id,
                'ghl_table_id': ghl_table_id
            }

            # Auto-load Airtable API key and customer configuration
            self.auto_load_customer_airtable_config(store_name, base_id, google_ads_table_id)

            # Update customer config status
            if hasattr(self, 'customer_config_status'):
                if base_id and google_ads_table_id:
                    self.customer_config_status.set(f"✅ {store_name} (Airtable ready)")
                else:
                    self.customer_config_status.set(f"⚠️ {store_name} (Airtable incomplete)")

            # Log the selection
            self.gads_log(f"✅ Loaded customer: {store_name}")
            self.gads_log(f"📋 Customer ID: {customer_id}")
            if base_id:
                self.gads_log(f"🗂️ Airtable Base: {base_id}")
            if google_ads_table_id:
                self.gads_log(f"🔍 Google Ads Table: {google_ads_table_id}")
            if ghl_table_id:
                self.gads_log(f"📞 GHL Table: {ghl_table_id}")

            customer_window.destroy()
            messagebox.showinfo("Customer Loaded",
                               f"✅ Customer loaded successfully!\n\n"
                               f"Store: {store_name}\n"
                               f"Customer ID: {customer_id}\n"
                               f"Airtable: {'✅ Auto-configured' if base_id else '❌ Not configured'}\n\n"
                               f"🚀 Ready to extract and sync data!")

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="✅ Load Selected Customer",
                  command=load_selected_customer).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔄 Refresh List",
                  command=update_client_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancel",
                  command=customer_window.destroy).pack(side=tk.RIGHT)

        # Double-click to load customer
        client_tree.bind("<Double-1>", lambda e: load_selected_customer())

    def auto_load_customer_airtable_config(self, store_name, base_id, google_ads_table_id):
        """Automatically load Airtable API key and customer-specific configuration"""
        try:
            # Auto-load Airtable API key
            api_key = "**********************************************************************************"
            self.gads_airtable_api_key.set(api_key)
            self.gads_log("🔑 Auto-loaded Airtable API key")

            # If customer has complete Airtable configuration, auto-load it
            if base_id and google_ads_table_id:
                self.gads_log("🔄 Auto-loading customer Airtable configuration...")

                # Load available bases first
                self.auto_load_bases_and_tables(base_id, google_ads_table_id)

            else:
                self.gads_log("⚠️ Customer has incomplete Airtable configuration")
                self.gads_log("💡 Use 'Fetch Bases' and 'Fetch Tables' to configure manually")

        except Exception as e:
            self.gads_log(f"❌ Error auto-loading customer config: {str(e)}")

    def auto_load_bases_and_tables(self, target_base_id, target_table_id):
        """Auto-load bases and tables for customer configuration"""
        def load_thread():
            try:
                # Fetch bases
                self.gads_log("📋 Fetching available bases...")
                api_key = self.gads_airtable_api_key.get().strip()

                if not api_key:
                    self.gads_log("❌ No API key available")
                    return

                # Fetch bases using the same logic as fetch_airtable_bases_gads
                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }

                response = requests.get('https://api.airtable.com/v0/meta/bases',
                                      headers=headers, timeout=10)

                if response.status_code == 200:
                    bases_data = response.json()
                    bases = bases_data.get('bases', [])

                    # Store available bases
                    self.gads_available_bases = {}
                    base_found = False

                    for base in bases:
                        base_name = base.get('name', 'Unknown')
                        base_id = base.get('id', '')
                        self.gads_available_bases[base_name] = base_id

                        # Check if this is our target base
                        if base_id == target_base_id:
                            base_found = True
                            self.gads_base_combo['values'] = list(self.gads_available_bases.keys())
                            self.gads_airtable_base.set(base_name)
                            self.gads_log(f"✅ Found and selected base: {base_name}")

                            # Now fetch tables for this base
                            self.auto_load_tables_for_base(target_base_id, target_table_id)
                            break

                    if not base_found:
                        self.gads_log(f"⚠️ Target base ID not found: {target_base_id}")
                        self.gads_base_combo['values'] = list(self.gads_available_bases.keys())

                else:
                    self.gads_log(f"❌ Failed to fetch bases: {response.status_code}")

            except Exception as e:
                self.gads_log(f"❌ Error auto-loading bases: {str(e)}")

        threading.Thread(target=load_thread, daemon=True).start()

    def auto_load_tables_for_base(self, base_id, target_table_id):
        """Auto-load tables for the selected base"""
        try:
            self.gads_log("📊 Fetching tables for selected base...")
            api_key = self.gads_airtable_api_key.get().strip()

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            response = requests.get(f'https://api.airtable.com/v0/meta/bases/{base_id}/tables',
                                  headers=headers, timeout=10)

            if response.status_code == 200:
                tables_data = response.json()
                tables = tables_data.get('tables', [])

                # Store available tables
                self.gads_available_tables = {}
                table_found = False

                for table in tables:
                    table_name = table.get('name', 'Unknown')
                    table_id = table.get('id', '')
                    display_name = f"{table_name} ({table_id})"
                    self.gads_available_tables[display_name] = table_id

                    # Check if this is our target table
                    if table_id == target_table_id:
                        table_found = True
                        self.gads_table_combo['values'] = list(self.gads_available_tables.keys())
                        self.gads_airtable_table.set(display_name)
                        self.gads_log(f"✅ Found and selected table: {table_name}")
                        break

                if not table_found:
                    self.gads_log(f"⚠️ Target table ID not found: {target_table_id}")
                    self.gads_table_combo['values'] = list(self.gads_available_tables.keys())

                # Update UI on main thread
                self.root.after(100, lambda: self.update_airtable_ui_after_auto_load())

            else:
                self.gads_log(f"❌ Failed to fetch tables: {response.status_code}")

        except Exception as e:
            self.gads_log(f"❌ Error auto-loading tables: {str(e)}")

    def update_airtable_ui_after_auto_load(self):
        """Update UI elements after auto-loading Airtable configuration"""
        try:
            # Update customer config status
            if hasattr(self, 'customer_config_status') and hasattr(self, 'selected_customer_config'):
                config = self.selected_customer_config
                store_name = config.get('store_name', 'Unknown')
                base_id = config.get('base_id')
                table_id = config.get('google_ads_table_id')

                if base_id and table_id:
                    self.customer_config_status.set(f"✅ {store_name} (Auto-configured)")
                    self.gads_log("🎉 Customer Airtable configuration auto-loaded successfully!")

                    # Update button text to show it's already configured
                    if hasattr(self, 'auto_populate_btn'):
                        self.auto_populate_btn.configure(text="✅ Already Auto-Configured")
                else:
                    self.customer_config_status.set(f"⚠️ {store_name} (Incomplete config)")

                    # Keep original button text for manual configuration
                    if hasattr(self, 'auto_populate_btn'):
                        self.auto_populate_btn.configure(text="🔄 Auto-Populate from Customer")

        except Exception as e:
            self.gads_log(f"❌ Error updating UI: {str(e)}")

    def auto_populate_airtable_from_customer(self):
        """Auto-populate Airtable settings from loaded customer configuration"""
        if not hasattr(self, 'selected_customer_config') or not self.selected_customer_config:
            messagebox.showwarning("No Customer",
                                 "Please load a customer first using the '👥 Load Customer' button.")
            return

        config = self.selected_customer_config

        # Check if already auto-configured
        if (self.gads_airtable_api_key.get() and
            self.gads_airtable_base.get() and
            self.gads_airtable_table.get()):
            messagebox.showinfo("Already Configured",
                               f"✅ Airtable is already configured for {config['store_name']}!\n\n"
                               f"API Key: ✅ Loaded\n"
                               f"Base: {self.gads_airtable_base.get()}\n"
                               f"Table: {self.gads_airtable_table.get()}\n\n"
                               f"🚀 Ready to extract and sync data!")
            return

        # Set API key (hardcoded)
        api_key = "**********************************************************************************"
        self.gads_airtable_api_key.set(api_key)

        # Check if we have Airtable configuration
        if not config.get('base_id') or not config.get('google_ads_table_id'):
            messagebox.showwarning("Incomplete Configuration",
                                 f"Customer '{config['store_name']}' doesn't have complete Airtable configuration.\n\n"
                                 "Please configure the customer's Airtable settings in Client Management first.")
            return

        try:
            # Load bases to populate the dropdown
            self.gads_log("🔄 Loading Airtable bases...")
            self.fetch_airtable_bases_gads()

            # Wait a moment for bases to load, then set the base
            def set_base_after_load():
                base_id = config['base_id']

                # Find the base name from the loaded bases
                base_name = None
                if hasattr(self, 'gads_available_bases'):
                    for name, id_val in self.gads_available_bases.items():
                        if id_val == base_id:
                            base_name = name
                            break

                if base_name:
                    self.gads_airtable_base.set(base_name)
                    self.gads_log(f"✅ Set Airtable base: {base_name}")

                    # Load tables for this base
                    self.fetch_google_ads_tables()

                    # Set the Google Ads table
                    def set_table_after_load():
                        table_id = config['google_ads_table_id']

                        # Find the table name from the loaded tables
                        table_name = None
                        if hasattr(self, 'gads_available_tables'):
                            for name, id_val in self.gads_available_tables.items():
                                if id_val == table_id:
                                    table_name = name
                                    break

                        if table_name:
                            self.gads_airtable_table.set(table_name)
                            self.gads_log(f"✅ Set Google Ads table: {table_name}")

                            messagebox.showinfo("Auto-Populate Complete",
                                               f"✅ Airtable configuration loaded successfully!\n\n"
                                               f"Customer: {config['store_name']}\n"
                                               f"Base: {base_name}\n"
                                               f"Table: {table_name}\n\n"
                                               f"You can now proceed with data extraction and sync.")
                        else:
                            self.gads_log(f"⚠️ Could not find table with ID: {table_id}")
                            messagebox.showwarning("Table Not Found",
                                                 f"Could not find Google Ads table with ID: {table_id}\n\n"
                                                 "Please select the table manually.")

                    # Schedule table setting after a short delay
                    self.root.after(2000, set_table_after_load)

                else:
                    self.gads_log(f"⚠️ Could not find base with ID: {base_id}")
                    messagebox.showwarning("Base Not Found",
                                         f"Could not find Airtable base with ID: {base_id}\n\n"
                                         "Please select the base manually.")

            # Schedule base setting after a short delay to allow bases to load
            self.root.after(2000, set_base_after_load)

        except Exception as e:
            error_msg = f"Error auto-populating Airtable settings: {str(e)}"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Auto-Populate Error", error_msg)

    def run(self):
        """Run the application"""
        self.root.mainloop()

    # === GOOGLE ADS HUB METHODS ===

    def gads_log(self, message):
        """Add message to Google Ads log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        if hasattr(self, 'gads_log_text'):
            self.gads_log_text.insert(tk.END, formatted_message)
            self.gads_log_text.see(tk.END)

    def on_date_preset_change(self, event=None):
        """Handle date preset selection change"""
        preset = self.gads_date_preset.get()
        if preset == "Custom range":
            self.custom_date_frame.grid()
            # Set default dates
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            self.gads_start_date.set(start_date.strftime("%Y-%m-%d"))
            self.gads_end_date.set(end_date.strftime("%Y-%m-%d"))
        else:
            self.custom_date_frame.grid_remove()

    def open_start_date_picker(self):
        """Open calendar picker for start date"""
        self.open_date_picker("start")

    def open_end_date_picker(self):
        """Open calendar picker for end date"""
        self.open_date_picker("end")

    def open_date_picker(self, date_type):
        """Open a beautiful calendar picker"""
        try:
            from tkcalendar import Calendar
        except ImportError:
            messagebox.showerror("Missing Library",
                               "tkcalendar library is required for calendar picker.\n"
                               "Please install it with: pip install tkcalendar")
            return

        # Create calendar window
        cal_window = tk.Toplevel(self.root)
        cal_window.title(f"Select {'Start' if date_type == 'start' else 'End'} Date")
        cal_window.geometry("350x400")
        cal_window.transient(self.root)
        cal_window.grab_set()

        # Center the window
        cal_window.update_idletasks()
        x = (cal_window.winfo_screenwidth() // 2) - (350 // 2)
        y = (cal_window.winfo_screenheight() // 2) - (400 // 2)
        cal_window.geometry(f"350x400+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(cal_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_text = f"📅 Select {'Start' if date_type == 'start' else 'End'} Date"
        title_label = ttk.Label(main_frame, text=title_text,
                               font=("Segoe UI", 12, "bold"))
        title_label.pack(pady=(0, 15))

        # Get current date or default
        current_date = datetime.now()
        if date_type == "start" and self.gads_start_date.get():
            try:
                current_date = datetime.strptime(self.gads_start_date.get(), "%Y-%m-%d")
            except:
                pass
        elif date_type == "end" and self.gads_end_date.get():
            try:
                current_date = datetime.strptime(self.gads_end_date.get(), "%Y-%m-%d")
            except:
                pass

        # Calendar widget
        cal = Calendar(main_frame,
                      selectmode='day',
                      year=current_date.year,
                      month=current_date.month,
                      day=current_date.day,
                      date_pattern='yyyy-mm-dd',
                      background='white',
                      foreground='black',
                      bordercolor='#cccccc',
                      headersbackground='#f0f0f0',
                      normalbackground='white',
                      normalforeground='black',
                      weekendbackground='#f8f8f8',
                      weekendforeground='black',
                      othermonthforeground='#cccccc',
                      othermonthbackground='white',
                      selectbackground='#0078d4',
                      selectforeground='white')
        cal.pack(pady=(0, 15))

        # Selected date display
        selected_frame = ttk.Frame(main_frame)
        selected_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(selected_frame, text="Selected Date:",
                 font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
        selected_date_var = tk.StringVar(value=cal.get_date())
        selected_date_label = ttk.Label(selected_frame, textvariable=selected_date_var,
                                       font=("Segoe UI", 9), foreground="#0078d4")
        selected_date_label.pack(side=tk.LEFT, padx=(10, 0))

        # Update selected date display when calendar changes
        def on_date_select():
            selected_date_var.set(cal.get_date())

        cal.bind("<<CalendarSelected>>", lambda e: on_date_select())

        # Quick select buttons
        quick_frame = ttk.LabelFrame(main_frame, text="Quick Select", padding=10)
        quick_frame.pack(fill=tk.X, pady=(0, 15))

        quick_buttons = [
            ("Today", 0),
            ("Yesterday", 1),
            ("1 Week Ago", 7),
            ("1 Month Ago", 30),
            ("3 Months Ago", 90)
        ]

        for i, (text, days_ago) in enumerate(quick_buttons):
            def set_quick_date(days=days_ago):
                target_date = datetime.now() - timedelta(days=days)
                cal.selection_set(target_date.date())
                selected_date_var.set(target_date.strftime("%Y-%m-%d"))

            ttk.Button(quick_frame, text=text, width=12,
                      command=set_quick_date).grid(row=i//3, column=i%3,
                                                   padx=5, pady=2, sticky=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def select_date():
            selected_date = cal.get_date()
            if date_type == "start":
                self.gads_start_date.set(selected_date)
            else:
                self.gads_end_date.set(selected_date)
            cal_window.destroy()

        ttk.Button(button_frame, text="✅ Select Date",
                  command=select_date).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancel",
                  command=cal_window.destroy).pack(side=tk.LEFT)

    def set_quick_range(self, days):
        """Set a quick date range"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        self.gads_start_date.set(start_date.strftime("%Y-%m-%d"))
        self.gads_end_date.set(end_date.strftime("%Y-%m-%d"))

    def test_google_ads_connection(self):
        """Test Google Ads API connection using OAuth credentials"""
        if not self.validate_google_ads_credentials():
            return

        self.gads_log("🔗 Testing Google Ads API connection...")

        def test_thread():
            try:
                self.gads_connection_status.set("🔄 Testing connection...")

                # Get credentials
                customer_id = self.gads_customer_id.get().strip().replace("-", "")
                developer_token = self.gads_developer_token.get().strip()
                refresh_token = self.gads_refresh_token.get().strip()
                manager_account = self.gads_manager_account.get().strip()

                self.gads_log(f"📡 Connecting to Google Ads API...")
                self.gads_log(f"👤 Customer ID: {customer_id}")
                self.gads_log(f"🏢 Manager Account: {manager_account}")

                # Try to import Google Ads client
                try:
                    from google.ads.googleads.client import GoogleAdsClient

                    # Create client configuration
                    client_config = {
                        "client_id": "************-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com",
                        "client_secret": "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",
                        "refresh_token": refresh_token,
                        "developer_token": developer_token,
                        "login_customer_id": manager_account,
                        "use_proto_plus": True
                    }

                    # Create Google Ads client
                    client = GoogleAdsClient.load_from_dict(client_config)

                    # Simple test query
                    ga_service = client.get_service("GoogleAdsService")
                    query = "SELECT customer.id FROM customer LIMIT 1"

                    search_request = client.get_type("SearchGoogleAdsRequest")
                    search_request.customer_id = customer_id
                    search_request.query = query

                    results = ga_service.search(request=search_request)

                    # If we get here, connection is successful
                    self.gads_log("✅ Google Ads API connection successful!")
                    self.gads_log("🎯 Ready to extract campaign data")
                    self.gads_connection_status.set("✅ Connected successfully")
                    messagebox.showinfo("Connection Success", "✅ Successfully connected to Google Ads API!")

                except ImportError:
                    self.gads_log("⚠️ Google Ads API library not installed")
                    self.gads_log("📦 Please install: pip install google-ads")
                    self.gads_connection_status.set("⚠️ Library missing")
                    messagebox.showwarning("Library Missing",
                                         "Google Ads API library not installed.\n\n"
                                         "Please install it using:\npip install google-ads")

            except Exception as e:
                error_msg = f"API connection test failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                self.gads_connection_status.set("❌ Connection failed")
                messagebox.showerror("Connection Failed", error_msg)

        threading.Thread(target=test_thread, daemon=True).start()

    def load_google_ads_campaigns(self):
        """Load available campaigns from Google Ads"""
        self.gads_log("📋 Loading Google Ads campaigns...")

        def load_thread():
            try:
                # TODO: Implement actual Google Ads API campaign loading
                # For now, simulate campaign loading
                self.gads_log("🔍 Fetching campaign list...")
                time.sleep(1)

                # Simulate campaign data
                campaigns = [
                    "Campaign 1 - Daphne Location",
                    "Campaign 2 - Mobile Location",
                    "Campaign 3 - Foley Location",
                    "Campaign 4 - General Repair Services"
                ]

                self.gads_log(f"✅ Found {len(campaigns)} campaigns:")
                for campaign in campaigns:
                    self.gads_log(f"   📊 {campaign}")

                messagebox.showinfo("Campaigns Loaded", f"✅ Successfully loaded {len(campaigns)} campaigns!")

            except Exception as e:
                error_msg = f"Failed to load campaigns: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Load Failed", error_msg)

        threading.Thread(target=load_thread, daemon=True).start()

    def extract_google_ads_data(self):
        """Extract Google Ads data for selected date range"""
        self.gads_log("📥 Starting Google Ads data extraction...")

        # Validate inputs
        customer_id = self.gads_customer_id.get().strip()
        developer_token = self.gads_developer_token.get().strip()
        refresh_token = self.gads_refresh_token.get().strip()
        manager_account = self.gads_manager_account.get().strip()

        if not all([customer_id, developer_token, refresh_token, manager_account]):
            self.gads_log("❌ Please configure all Google Ads API credentials first")
            messagebox.showerror("Missing Credentials",
                               "Please configure all Google Ads API credentials:\n"
                               "• Customer ID\n"
                               "• Developer Token\n"
                               "• Refresh Token\n"
                               "• Manager Account")
            return

        def extract_thread():
            try:
                # Get date range
                date_preset = self.gads_date_preset.get()
                if date_preset == "Custom range":
                    start_date = self.gads_start_date.get()
                    end_date = self.gads_end_date.get()

                    # Validate custom dates
                    if not start_date or not end_date:
                        self.gads_log("❌ Please select both start and end dates")
                        messagebox.showerror("Missing Dates", "Please select both start and end dates")
                        return
                else:
                    end_date = datetime.now()
                    if date_preset == "Last 7 days":
                        start_date = end_date - timedelta(days=7)
                    elif date_preset == "Last 30 days":
                        start_date = end_date - timedelta(days=30)
                    elif date_preset == "Last 90 days":
                        start_date = end_date - timedelta(days=90)
                    elif date_preset == "This month":
                        start_date = end_date.replace(day=1)
                    elif date_preset == "Last month":
                        first_day_this_month = end_date.replace(day=1)
                        end_date = first_day_this_month - timedelta(days=1)
                        start_date = end_date.replace(day=1)
                    else:
                        start_date = end_date - timedelta(days=30)

                    start_date = start_date.strftime("%Y-%m-%d")
                    end_date = end_date.strftime("%Y-%m-%d")

                self.gads_log(f"📅 Date range: {start_date} to {end_date}")
                self.gads_log("🔄 Extracting campaign performance data...")

                # Create Google Ads client
                try:
                    from google.ads.googleads.client import GoogleAdsClient
                    from google.ads.googleads.errors import GoogleAdsException
                except ImportError:
                    self.gads_log("❌ Google Ads API library not installed")
                    messagebox.showerror("Missing Library",
                                       "Google Ads API library is required.\n"
                                       "Please install it with: pip install google-ads")
                    return

                client_config = {
                    "client_id": "************-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com",
                    "client_secret": "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",
                    "refresh_token": refresh_token,
                    "developer_token": developer_token,
                    "login_customer_id": manager_account,
                    "use_proto_plus": True
                }

                client = GoogleAdsClient.load_from_dict(client_config)

                # Create Google Ads Query Language (GAQL) query
                query = f"""
                SELECT
                    campaign.id,
                    campaign.name,
                    metrics.cost_micros,
                    metrics.impressions,
                    metrics.clicks,
                    metrics.conversions,
                    segments.date
                FROM campaign
                WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
                ORDER BY segments.date
                """

                # Execute query
                ga_service = client.get_service("GoogleAdsService")
                customer_id_clean = customer_id.replace("-", "")

                self.gads_log(f"🔍 Executing query for customer ID: {customer_id_clean}")
                self.gads_log(f"📊 Query: {query.strip()}")

                # Make the request
                search_request = client.get_type("SearchGoogleAdsRequest")
                search_request.customer_id = customer_id_clean
                search_request.query = query

                results = ga_service.search(request=search_request)

                # Process results
                data = []
                for row in results:
                    campaign_id = row.campaign.id
                    campaign_name = row.campaign.name
                    cost = row.metrics.cost_micros / 1000000  # Convert micros to actual currency
                    impressions = row.metrics.impressions
                    clicks = row.metrics.clicks
                    conversions = row.metrics.conversions
                    date = row.segments.date

                    data.append({
                        'Date': str(date),
                        'Campaign ID': campaign_id,
                        'Campaign Name': campaign_name,
                        'Cost': cost,
                        'Impressions': impressions,
                        'Clicks': clicks,
                        'Conversions': conversions,
                        'CTR': (clicks / impressions * 100 if impressions > 0 else 0),
                        'CPC': (cost / clicks if clicks > 0 else 0),
                        'Conv. Rate': (conversions / clicks * 100 if clicks > 0 else 0),
                        'Cost per Conv.': (cost / conversions if conversions > 0 else 0)
                    })

                # Convert to DataFrame
                self.google_ads_data = pd.DataFrame(data)

                # Display summary
                if not data:
                    self.gads_log("⚠️ No data found for the specified date range.")
                    self.gads_extraction_status.set("⚠️ No data found")
                    messagebox.showwarning("No Data", "No data found for the specified date range and customer.")
                else:
                    total_cost = self.google_ads_data['Cost'].sum()
                    total_clicks = self.google_ads_data['Clicks'].sum()
                    total_impressions = self.google_ads_data['Impressions'].sum()
                    total_conversions = self.google_ads_data['Conversions'].sum()

                    self.gads_log(f"✅ Data extraction complete. Found {len(data)} records.")
                    self.gads_log(f"💰 Total Cost: ${total_cost:.2f}")
                    self.gads_log(f"👆 Total Clicks: {total_clicks:,}")
                    self.gads_log(f"👁️ Total Impressions: {total_impressions:,}")
                    self.gads_log(f"🎯 Total Conversions: {total_conversions:.2f}")

                    # Update extraction status
                    self.gads_extraction_status.set(f"✅ {len(self.google_ads_data)} records extracted")

                    # Show sample data in log
                    self.gads_log("\n📊 Sample data (first 3 rows):")
                    sample_data = self.google_ads_data.head(3)
                    for _, row in sample_data.iterrows():
                        self.gads_log(f"📅 {row['Date']} | 📢 {row['Campaign Name']} | 💰 ${row['Cost']:.2f} | 👆 {row['Clicks']}")

                    # Auto-sync if enabled
                    if self.gads_auto_sync.get():
                        self.gads_log("🔄 Auto-sync enabled, syncing to Airtable...")
                        self.sync_google_ads_to_airtable()

                    messagebox.showinfo("Extraction Complete",
                                      f"✅ Successfully extracted {len(self.google_ads_data)} records!\n\n"
                                      f"💰 Total Cost: ${total_cost:.2f}\n"
                                      f"👆 Total Clicks: {total_clicks:,}\n"
                                      f"👁️ Total Impressions: {total_impressions:,}\n"
                                      f"🎯 Total Conversions: {total_conversions:.2f}")

            except Exception as e:
                # Handle Google Ads API specific errors
                if "GoogleAdsException" in str(type(e)):
                    self.gads_log(f"❌ Google Ads API error: {str(e)}")
                    if hasattr(e, 'failure') and hasattr(e.failure, 'errors'):
                        for error in e.failure.errors:
                            self.gads_log(f"   Error: {error.message}")
                        error_msg = f"Google Ads API Error: {e.failure.errors[0].message}"
                    else:
                        error_msg = f"Google Ads API Error: {str(e)}"
                else:
                    error_msg = f"Extraction failed: {str(e)}"
                    self.gads_log(f"❌ {error_msg}")

                self.gads_extraction_status.set("❌ Extraction failed")
                messagebox.showerror("Extraction Failed", error_msg)

        threading.Thread(target=extract_thread, daemon=True).start()

    def preview_google_ads_data(self):
        """Preview extracted Google Ads data"""
        if not hasattr(self, 'google_ads_data') or self.google_ads_data is None:
            self.gads_log("❌ No data to preview. Please extract data first.")
            messagebox.showwarning("No Data", "Please extract Google Ads data first")
            return

        # Create preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("📊 Google Ads Data Preview")
        preview_window.geometry("800x600")

        # Create text widget with scrollbar
        text_frame = ttk.Frame(preview_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        preview_text = scrolledtext.ScrolledText(text_frame, font=("Consolas", 10))
        preview_text.pack(fill=tk.BOTH, expand=True)

        # Display data summary
        preview_content = f"""📊 Google Ads Data Preview
{'=' * 50}

📈 Summary Statistics:
• Total Records: {len(self.google_ads_data)}
• Date Range: {self.google_ads_data['Date'].min()} to {self.google_ads_data['Date'].max()}
• Total Cost: ${self.google_ads_data['Cost'].sum():.2f}
• Total Clicks: {self.google_ads_data['Clicks'].sum():,}
• Total Impressions: {self.google_ads_data['Impressions'].sum():,}
• Total Conversions: {self.google_ads_data['Conversions'].sum()}

📋 Sample Data (First 10 rows):
{'-' * 50}
{self.google_ads_data.head(10).to_string(index=False)}

📊 Campaign Performance:
{'-' * 50}
"""

        # Add campaign-level summary
        if 'Campaign Name' in self.google_ads_data.columns:
            campaign_summary = self.google_ads_data.groupby('Campaign Name').agg({
                'Cost': 'sum',
                'Clicks': 'sum',
                'Impressions': 'sum',
                'Conversions': 'sum'
            }).round(2)
            preview_content += campaign_summary.to_string()

        preview_text.insert(tk.END, preview_content)
        preview_text.config(state=tk.DISABLED)

        self.gads_log(f"👁️ Previewed {len(self.google_ads_data)} records")

    def export_google_ads_csv(self):
        """Export Google Ads data to CSV"""
        if not hasattr(self, 'google_ads_data') or self.google_ads_data is None:
            self.gads_log("❌ No data to export. Please extract data first.")
            messagebox.showwarning("No Data", "Please extract Google Ads data first")
            return

        # Ask user for save location
        filename = filedialog.asksaveasfilename(
            title="Save Google Ads Data",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialname=f"google_ads_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )

        if filename:
            try:
                self.google_ads_data.to_csv(filename, index=False)
                self.gads_log(f"💾 Exported {len(self.google_ads_data)} records to: {filename}")
                messagebox.showinfo("Export Complete", f"✅ Data exported successfully!\n\nFile: {filename}")
            except Exception as e:
                error_msg = f"Export failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Export Failed", error_msg)

    def sync_google_ads_to_airtable(self):
        """Sync Google Ads data to Airtable"""
        if not hasattr(self, 'google_ads_data') or self.google_ads_data is None:
            self.gads_log("❌ No data to sync. Please extract data first.")
            messagebox.showwarning("No Data", "Please extract Google Ads data first")
            return

        # Get Airtable credentials from the Google Ads Hub
        api_key = self.gads_airtable_api_key.get().strip()
        base_name = self.gads_airtable_base.get().strip()
        table_name = self.gads_airtable_table.get().strip()

        if not all([api_key, base_name, table_name]):
            self.gads_log("❌ Please configure Airtable connection first")
            messagebox.showerror("Missing Configuration",
                               "Please configure Airtable connection in Step 2 first")
            return

        base_id = self.gads_available_bases.get(base_name)
        table_id = self.gads_available_tables.get(table_name)

        if not base_id or not table_id:
            self.gads_log("❌ Invalid base or table selection")
            return

        def sync_thread():
            try:
                self.gads_log("🔄 Starting Airtable sync...")
                self.gads_sync_status.set("🔄 Syncing to Airtable...")

                # Create Airtable manager
                if not self.airtable_manager:
                    self.airtable_manager = EnhancedAirtableManager()

                self.airtable_manager.set_credentials(api_key, base_id, table_id)

                # Test connection
                success, message = self.airtable_manager.test_connection()
                if not success:
                    self.gads_log(f"❌ Connection failed: {message}")
                    self.gads_sync_status.set("❌ Connection failed")
                    messagebox.showerror("Sync Failed", f"Airtable connection failed:\n{message}")
                    return

                self.gads_log("✅ Airtable connection verified")

                # Check table schema compatibility
                self.gads_log("🔍 Checking table schema compatibility...")
                print("🔍 DEBUG: About to get table schema...")

                airtable_fields = self.airtable_manager.get_table_schema()
                print(f"🔍 DEBUG: Got table schema: {airtable_fields}")

                data_columns = list(self.google_ads_data.columns)
                print(f"🔍 DEBUG: Data columns: {data_columns}")

                self.gads_log(f"🔍 Data columns: {data_columns}")
                self.gads_log(f"🔍 Airtable fields: {airtable_fields}")

                # Check for field mismatches
                missing_in_airtable = [col for col in data_columns if col not in airtable_fields and airtable_fields]
                if missing_in_airtable and airtable_fields:
                    self.gads_log(f"⚠️ Fields missing in Airtable: {missing_in_airtable}")
                    print(f"⚠️ DEBUG: Fields missing in Airtable: {missing_in_airtable}")

                # Upload data
                sync_mode = self.gads_sync_mode.get()
                print(f"🔍 DEBUG: Sync mode: {sync_mode}")
                print(f"🔍 DEBUG: Data shape: {self.google_ads_data.shape}")

                self.gads_log(f"📤 Uploading {len(self.google_ads_data)} records in {sync_mode} mode...")

                # Debug: Show data structure before upload
                self.gads_log(f"🔍 Sample data types: {dict(self.google_ads_data.dtypes)}")
                print(f"🔍 DEBUG: Data types: {dict(self.google_ads_data.dtypes)}")

                if len(self.google_ads_data) > 0:
                    sample_row = self.google_ads_data.iloc[0]
                    self.gads_log(f"🔍 Sample row: {dict(sample_row)}")
                    print(f"🔍 DEBUG: Sample row: {dict(sample_row)}")

                print("🔍 DEBUG: About to call upload_data...")
                uploaded_count, errors, skipped_count = self.airtable_manager.upload_data(
                    self.google_ads_data, mode=sync_mode
                )
                print(f"🔍 DEBUG: Upload_data returned: uploaded={uploaded_count}, errors={len(errors) if errors else 0}, skipped={skipped_count}")

                # Report results with detailed error information
                if errors:
                    self.gads_log(f"⚠️ Upload completed with {len(errors)} errors:")
                    # Show ALL errors, not just first 3
                    for i, error in enumerate(errors, 1):
                        self.gads_log(f"   Error {i}: {error}")

                    # Also print to console for debugging
                    print(f"\n🚨 DETAILED ERROR ANALYSIS:")
                    print(f"Total errors: {len(errors)}")
                    for i, error in enumerate(errors, 1):
                        print(f"Error {i}: {error}")

                    result_msg = f"Uploaded {uploaded_count} records with {len(errors)} errors"
                    self.gads_sync_status.set(f"⚠️ {uploaded_count} uploaded, {len(errors)} errors")
                else:
                    self.gads_log(f"✅ Successfully uploaded {uploaded_count} records!")
                    result_msg = f"Successfully uploaded {uploaded_count} records"
                    self.gads_sync_status.set(f"✅ {uploaded_count} records synced")

                if sync_mode == "incremental" and skipped_count > 0:
                    self.gads_log(f"⏭️ Skipped {skipped_count} existing records")
                    result_msg += f" (skipped {skipped_count} existing)"

                messagebox.showinfo("Sync Complete", result_msg)

            except Exception as e:
                error_msg = f"Sync failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                self.gads_sync_status.set("❌ Sync failed")
                messagebox.showerror("Sync Failed", error_msg)

        threading.Thread(target=sync_thread, daemon=True).start()

    def view_google_ads_stats(self):
        """View Google Ads Airtable statistics"""
        api_key = self.airtable_api_key.get().strip()
        base_id = self.airtable_base_id.get().strip()
        table_id = self.google_ads_table_id.get().strip()

        if not all([api_key, base_id, table_id]):
            self.gads_log("❌ Please configure Airtable credentials first")
            messagebox.showerror("Missing Configuration",
                               "Please configure Airtable credentials first")
            return

        def stats_thread():
            try:
                self.gads_log("📊 Fetching Airtable statistics...")

                # Create Airtable manager
                if not self.airtable_manager:
                    self.airtable_manager = EnhancedAirtableManager()

                self.airtable_manager.set_credentials(api_key, base_id, table_id)

                # Get records
                records = self.airtable_manager.get_records(max_records=1000)

                if not records:
                    self.gads_log("📊 No records found in Airtable")
                    messagebox.showinfo("Statistics", "No records found in Airtable table")
                    return

                # Calculate statistics
                total_records = len(records)
                latest_date = self.airtable_manager.get_latest_date()

                # Extract data for analysis
                total_cost = 0
                total_clicks = 0
                total_impressions = 0
                campaigns = set()

                for record in records:
                    fields = record.get('fields', {})

                    if 'Campaign Name' in fields:
                        campaigns.add(fields['Campaign Name'])

                    cost = fields.get('Cost', 0)
                    clicks = fields.get('Clicks', 0)
                    impressions = fields.get('Impressions', 0)

                    if isinstance(cost, (int, float)):
                        total_cost += cost
                    if isinstance(clicks, (int, float)):
                        total_clicks += clicks
                    if isinstance(impressions, (int, float)):
                        total_impressions += impressions

                # Display statistics
                stats_msg = f"""📊 Google Ads Airtable Statistics:

📈 Overview:
• Total Records: {total_records:,}
• Latest Date: {latest_date or 'Unknown'}
• Unique Campaigns: {len(campaigns)}

💰 Performance:
• Total Cost: ${total_cost:,.2f}
• Total Clicks: {total_clicks:,}
• Total Impressions: {total_impressions:,}
• Average CTR: {(total_clicks/total_impressions*100):.2f}% (if impressions > 0)
• Average CPC: ${(total_cost/total_clicks):.2f} (if clicks > 0)

🎯 Campaigns:
{chr(10).join(f'• {campaign}' for campaign in sorted(campaigns)[:10])}
{'• ... and more' if len(campaigns) > 10 else ''}
"""

                self.gads_log("✅ Statistics retrieved successfully")
                messagebox.showinfo("Airtable Statistics", stats_msg)

            except Exception as e:
                error_msg = f"Failed to get statistics: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Statistics Failed", error_msg)

        threading.Thread(target=stats_thread, daemon=True).start()

    def clear_google_ads_data(self):
        """Clear all Google Ads data from Airtable"""
        api_key = self.airtable_api_key.get().strip()
        base_id = self.airtable_base_id.get().strip()
        table_id = self.google_ads_table_id.get().strip()

        if not all([api_key, base_id, table_id]):
            self.gads_log("❌ Please configure Airtable credentials first")
            messagebox.showerror("Missing Configuration",
                               "Please configure Airtable credentials first")
            return

        # Confirm deletion
        result = messagebox.askyesno("Confirm Clear",
                                   "⚠️ This will delete ALL Google Ads data from Airtable.\n\nAre you sure you want to continue?")
        if not result:
            return

        def clear_thread():
            try:
                self.gads_log("🗑️ Clearing all Google Ads data from Airtable...")

                # Create Airtable manager
                if not self.airtable_manager:
                    self.airtable_manager = EnhancedAirtableManager()

                self.airtable_manager.set_credentials(api_key, base_id, table_id)

                # Clear records
                success, message = self.airtable_manager.clear_all_records()

                if success:
                    self.gads_log(f"✅ {message}")
                    messagebox.showinfo("Clear Complete", message)
                else:
                    self.gads_log(f"❌ {message}")
                    messagebox.showerror("Clear Failed", message)

            except Exception as e:
                error_msg = f"Clear failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Clear Failed", error_msg)

        threading.Thread(target=clear_thread, daemon=True).start()

    def clear_gads_log(self):
        """Clear the Google Ads activity log"""
        self.gads_log_text.delete(1.0, tk.END)
        self.gads_log("🗑️ Activity log cleared")

    # === GOOGLE ADS OAUTH AND CREDENTIAL METHODS ===

    def get_google_ads_oauth_tokens(self):
        """Start OAuth flow to get refresh token for Google Ads API"""
        self.gads_log("🔐 Starting Google Ads OAuth flow...")

        def oauth_thread():
            try:
                # Check for gads-secret.json file
                gads_secrets_file = "gads-secret.json"

                if not os.path.exists(gads_secrets_file):
                    self.gads_log(f"❌ Google Ads credentials file not found: {gads_secrets_file}")
                    messagebox.showerror(
                        "Google Ads Credentials File Missing",
                        f"❌ Could not find {gads_secrets_file}\n\n"
                        "Please ensure the Google Ads credentials file is in the same folder as the application."
                    )
                    return

                self.gads_log(f"📄 Using Google Ads credentials file: {gads_secrets_file}")

                # Import required OAuth libraries
                from google_auth_oauthlib.flow import InstalledAppFlow

                # Google Ads API scopes
                GOOGLE_ADS_SCOPES = ['https://www.googleapis.com/auth/adwords']

                # Start OAuth flow for Google Ads API
                self.gads_log(f"🚀 Starting Google Ads OAuth flow with scopes: {GOOGLE_ADS_SCOPES}")
                flow = InstalledAppFlow.from_client_secrets_file(
                    gads_secrets_file, GOOGLE_ADS_SCOPES)

                self.gads_log("🌐 Opening browser for Google Ads authentication...")

                credentials = flow.run_local_server(port=0)

                # Update UI with refresh token
                self.gads_refresh_token.set(credentials.refresh_token)
                self.gads_log(f"✅ Successfully obtained refresh token: {credentials.refresh_token[:10]}...")
                self.gads_log("🔐 OAuth authentication completed successfully!")

                # Auto-save credentials
                self.save_settings()
                self.gads_log("💾 Credentials saved automatically")

                messagebox.showinfo("Success", "Google Ads OAuth tokens obtained successfully!")

            except Exception as e:
                error_msg = f"❌ Error during OAuth flow: {str(e)}"
                self.gads_log(error_msg)
                messagebox.showerror("OAuth Error", f"Failed to obtain OAuth tokens: {str(e)}")

        # Run OAuth flow in a separate thread to keep UI responsive
        import threading
        threading.Thread(target=oauth_thread, daemon=True).start()

    def validate_google_ads_credentials(self):
        """Validate Google Ads credentials"""
        customer_id = self.gads_customer_id.get().strip()
        developer_token = self.gads_developer_token.get().strip()
        refresh_token = self.gads_refresh_token.get().strip()
        manager_account = self.gads_manager_account.get().strip()

        missing_fields = []
        if not customer_id:
            missing_fields.append("Customer ID")
        if not developer_token:
            missing_fields.append("Developer Token")
        if not refresh_token:
            missing_fields.append("Refresh Token")
        if not manager_account:
            missing_fields.append("Manager Account")

        if missing_fields:
            error_msg = f"Missing required credentials: {', '.join(missing_fields)}"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Missing Credentials", error_msg)
            return False

        # Validate Customer ID format (should be 10 digits)
        customer_id_clean = customer_id.replace("-", "")
        if not customer_id_clean.isdigit() or len(customer_id_clean) != 10:
            error_msg = "Customer ID must be 10 digits (format: 123-456-7890)"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Invalid Customer ID", error_msg)
            return False

        self.gads_log("✅ All credentials are present and valid format")

        # Auto-save valid credentials
        self.save_settings()
        self.gads_log("💾 Valid credentials saved automatically")

        messagebox.showinfo("Validation Success", "All Google Ads credentials are valid!")
        return True

    def validate_google_ads_credentials_for_account_listing(self):
        """Validate Google Ads credentials for account listing (no customer ID required)"""
        developer_token = self.gads_developer_token.get().strip()
        refresh_token = self.gads_refresh_token.get().strip()
        manager_account = self.gads_manager_account.get().strip()

        missing_fields = []
        if not developer_token:
            missing_fields.append("Developer Token")
        if not refresh_token:
            missing_fields.append("Refresh Token")
        if not manager_account:
            missing_fields.append("Manager Account")

        if missing_fields:
            error_msg = f"Missing required credentials for account listing: {', '.join(missing_fields)}"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Missing Credentials",
                               f"{error_msg}\n\n"
                               "Note: Customer ID is not required for viewing accounts.\n"
                               "Use 'View Accounts' to select a customer ID.")
            return False

        # Validate format of credentials
        if len(developer_token) < 10:
            self.gads_log("❌ Developer token appears to be too short")
            messagebox.showerror("Invalid Developer Token",
                               "Developer token appears to be invalid (too short).")
            return False

        if not refresh_token.startswith("1//"):
            self.gads_log("❌ Refresh token format appears invalid")
            messagebox.showerror("Invalid Refresh Token",
                               "Refresh token format appears invalid.\n"
                               "Should start with '1//'")
            return False

        if not manager_account.isdigit() or len(manager_account) < 10:
            self.gads_log("❌ Manager account ID format appears invalid")
            messagebox.showerror("Invalid Manager Account",
                               "Manager account ID should be a 10+ digit number.")
            return False

        self.gads_log("✅ Required credentials for account listing are present and valid format")
        return True

    def save_google_ads_settings(self):
        """Manually save Google Ads settings"""
        self.gads_log("💾 Saving Google Ads settings...")

        try:
            if self.save_settings():
                self.gads_log("✅ Google Ads settings saved successfully!")
                messagebox.showinfo("Settings Saved", "Google Ads settings have been saved successfully!")
            else:
                self.gads_log("❌ Failed to save Google Ads settings")
                messagebox.showerror("Save Error", "Failed to save Google Ads settings")
        except Exception as e:
            error_msg = f"Error saving settings: {str(e)}"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Save Error", error_msg)

    def load_default_google_ads_settings(self):
        """Load default hardcoded Google Ads settings"""
        self.gads_log("🔄 Loading default Google Ads settings...")

        try:
            # Set default values
            self.gads_customer_id.set("")  # Keep customer ID empty for user to select
            self.gads_developer_token.set("bGBR3u83ozZjmx1WXckSkQ")
            self.gads_refresh_token.set("1//01NsefTx-HCBvCgYIARAAGAESNwF-L9IrG7rF3r0GzraiGQPO-cBWzppPxA0pggmi8KtWhZqpSZBDUAG3J_qbKx1xWo06hY9GaSc")
            self.gads_manager_account.set("**********")

            self.gads_log("✅ Default settings loaded successfully!")
            self.gads_log("💡 You can now use 'View Accounts' to select a customer ID")

            messagebox.showinfo("Defaults Loaded",
                               "Default Google Ads settings have been loaded!\n\n"
                               "• Developer Token: Set\n"
                               "• Refresh Token: Set\n"
                               "• Manager Account: **********\n\n"
                               "Use 'View Accounts' to select a customer ID.")
        except Exception as e:
            error_msg = f"Error loading defaults: {str(e)}"
            self.gads_log(f"❌ {error_msg}")
            messagebox.showerror("Load Error", error_msg)

    def view_google_ads_accounts(self):
        """View all accessible Google Ads accounts under the manager account"""
        if not self.validate_google_ads_credentials_for_account_listing():
            return

        self.gads_log("🔍 Fetching accessible Google Ads accounts...")
        self.gads_log("💡 No customer ID required - using OAuth credentials to discover accounts")

        def fetch_accounts_thread():
            try:
                # Get credentials (no customer ID needed!)
                developer_token = self.gads_developer_token.get().strip()
                refresh_token = self.gads_refresh_token.get().strip()
                manager_account = self.gads_manager_account.get().strip()

                self.gads_log(f"📡 Connecting to Google Ads API for account discovery...")
                self.gads_log(f"🏢 Manager Account: {manager_account}")
                self.gads_log("🔍 Using CustomerService.ListAccessibleCustomers() method")

                # Try to import Google Ads client
                try:
                    from google.ads.googleads.client import GoogleAdsClient

                    # Create client configuration
                    client_config = {
                        "client_id": "************-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com",
                        "client_secret": "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",
                        "refresh_token": refresh_token,
                        "developer_token": developer_token,
                        "login_customer_id": manager_account,
                        "use_proto_plus": True
                    }

                    # Create Google Ads client
                    client = GoogleAdsClient.load_from_dict(client_config)

                    # Step 1: Get list of accessible customer resource names
                    customer_service = client.get_service("CustomerService")
                    accessible_customers = customer_service.list_accessible_customers()

                    self.gads_log(f"📋 Found {len(accessible_customers.resource_names)} accessible customer resource names")

                    # Step 2: Get detailed information for each accessible customer
                    accounts = []
                    ga_service = client.get_service("GoogleAdsService")

                    for resource_name in accessible_customers.resource_names:
                        try:
                            # Extract customer ID from resource name (format: customers/**********)
                            customer_id = resource_name.split('/')[-1]

                            # Query customer details
                            query = """
                                SELECT
                                    customer.id,
                                    customer.descriptive_name,
                                    customer.currency_code,
                                    customer.time_zone,
                                    customer.status
                                FROM customer
                                LIMIT 1
                            """

                            search_request = client.get_type("SearchGoogleAdsRequest")
                            search_request.customer_id = customer_id
                            search_request.query = query

                            results = ga_service.search(request=search_request)

                            # Process results
                            for row in results:
                                customer = row.customer
                                if customer.status.name == 'ENABLED':  # Only include enabled accounts
                                    accounts.append({
                                        'id': customer.id,
                                        'name': customer.descriptive_name or f"Account {customer.id}",
                                        'currency': customer.currency_code,
                                        'timezone': customer.time_zone,
                                        'status': customer.status.name
                                    })
                                    break  # Only need one result per customer

                        except Exception as e:
                            # Skip accounts that can't be accessed
                            self.gads_log(f"⚠️ Skipping inaccessible account {resource_name}: {str(e)}")
                            continue

                    # Sort accounts by name for better display
                    accounts.sort(key=lambda x: x['name'].lower())

                    self.gads_log(f"✅ Successfully retrieved {len(accounts)} enabled accounts")

                    if accounts:
                        # Display accounts in a new window
                        self.show_accounts_window(accounts)
                    else:
                        self.gads_log("⚠️ No enabled accounts found")
                        messagebox.showwarning("No Accounts",
                                             "No enabled Google Ads accounts were found.\n\n"
                                             "This could mean:\n"
                                             "• All accounts are suspended/disabled\n"
                                             "• You don't have access to any accounts\n"
                                             "• The manager account ID is incorrect")

                except ImportError:
                    self.gads_log("⚠️ Google Ads API library not installed")
                    self.gads_log("📦 Please install: pip install google-ads")
                    messagebox.showwarning("Library Missing",
                                         "Google Ads API library not installed.\n\n"
                                         "Please install it using:\npip install google-ads")

            except Exception as e:
                error_msg = f"Failed to fetch accounts: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Fetch Error", error_msg)

        # Run in separate thread to keep UI responsive
        threading.Thread(target=fetch_accounts_thread, daemon=True).start()

    def search_google_ads_accounts(self):
        """Open search interface for Google Ads accounts"""
        # Create search window
        search_window = tk.Toplevel(self.root)
        search_window.title("Search Google Ads Accounts")
        search_window.geometry("600x500")
        search_window.transient(self.root)
        search_window.grab_set()

        # Main frame
        main_frame = ttk.Frame(search_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="🔍 Search Google Ads Accounts",
                               font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Important notice about API limitations
        notice_frame = ttk.LabelFrame(main_frame, text="⚠️ Important: API vs Web Interface Differences", padding=10)
        notice_frame.pack(fill=tk.X, pady=(0, 15))

        notice_text = """The Google Ads API has different permissions than the web interface:

• Web Interface: Can see all accounts through manager relationships
• API Interface: Limited to accounts with direct access only

For best results:
✅ Use exact Customer ID if you know it (e.g., 123-456-7890)
✅ Use "Search Google Ads" to see what's accessible via API
⚠️ Name search only works within your directly accessible accounts"""

        ttk.Label(notice_frame, text=notice_text, font=("Segoe UI", 9),
                 justify=tk.LEFT, wraplength=550).pack()

        # Search options frame
        options_frame = ttk.LabelFrame(main_frame, text="Search Options", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))

        # Option 1: Search from local client database
        ttk.Label(options_frame, text="Option 1: Search from Local Client Database",
                 font=("Segoe UI", 10, "bold")).pack(anchor=tk.W)
        ttk.Label(options_frame, text="Search your saved clients by store name or business name:",
                 font=("Segoe UI", 9), foreground="#666666").pack(anchor=tk.W, pady=(0, 5))

        client_search_frame = ttk.Frame(options_frame)
        client_search_frame.pack(fill=tk.X, pady=(0, 10))

        client_search_var = tk.StringVar()
        client_search_entry = ttk.Entry(client_search_frame, textvariable=client_search_var, width=30)
        client_search_entry.pack(side=tk.LEFT, padx=(0, 5))

        def search_local_clients():
            search_term = client_search_var.get().strip()
            if not search_term:
                messagebox.showwarning("Search Required", "Please enter a search term.")
                return

            clients = self.search_clients(search_term)
            if not clients:
                messagebox.showinfo("No Results", f"No clients found matching '{search_term}'.")
                return

            # Show results in a new window
            results_window = tk.Toplevel(search_window)
            results_window.title("Client Search Results")
            results_window.geometry("600x400")
            results_window.transient(search_window)

            results_frame = ttk.Frame(results_window)
            results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            ttk.Label(results_frame, text=f"Search Results for '{search_term}':",
                     font=("Segoe UI", 12, "bold")).pack(pady=(0, 10))

            # Results listbox
            results_listbox = tk.Listbox(results_frame, height=10)
            results_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            for client in clients:
                display_text = f"{client[1]} (ID: {client[2]}) - {client[3] or 'No business name'}"
                results_listbox.insert(tk.END, display_text)

            def use_selected():
                selection = results_listbox.curselection()
                if not selection:
                    messagebox.showwarning("No Selection", "Please select a client.")
                    return

                selected_client = clients[selection[0]]
                customer_id = selected_client[2]
                self.gads_customer_id.set(customer_id)
                messagebox.showinfo("Success", f"Customer ID {customer_id} has been loaded!")
                results_window.destroy()
                search_window.destroy()

            ttk.Button(results_frame, text="Use Selected", command=use_selected).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(results_frame, text="Close", command=results_window.destroy).pack(side=tk.LEFT)

        ttk.Button(client_search_frame, text="🔍 Search Local Clients",
                  command=search_local_clients).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(client_search_frame, text="👥 Manage Clients",
                  command=lambda: [search_window.destroy(), self.manage_clients()]).pack(side=tk.LEFT)

        # Separator
        ttk.Separator(options_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # Option 2: Search Google Ads API
        ttk.Label(options_frame, text="Option 2: Search Google Ads API",
                 font=("Segoe UI", 10, "bold")).pack(anchor=tk.W)
        ttk.Label(options_frame, text="Search directly from Google Ads (requires valid credentials):",
                 font=("Segoe UI", 9), foreground="#666666").pack(anchor=tk.W, pady=(0, 5))

        api_search_frame = ttk.Frame(options_frame)
        api_search_frame.pack(fill=tk.X, pady=(0, 10))

        api_search_var = tk.StringVar()
        api_search_entry = ttk.Entry(api_search_frame, textvariable=api_search_var, width=30)
        api_search_entry.pack(side=tk.LEFT, padx=(0, 5))

        def search_google_ads_api():
            search_term = api_search_var.get().strip()
            if not search_term:
                messagebox.showwarning("Search Required", "Please enter a search term.")
                return

            search_window.destroy()
            self.search_google_ads_account(search_term)

        ttk.Button(api_search_frame, text="🔍 Search Google Ads",
                  command=search_google_ads_api).pack(side=tk.LEFT, padx=(0, 5))

        def list_all_accessible():
            search_window.destroy()
            self.view_google_ads_accounts()

        ttk.Button(api_search_frame, text="📋 List All Accessible",
                  command=list_all_accessible).pack(side=tk.LEFT)

        # Separator
        ttk.Separator(options_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # Option 3: Manual entry
        ttk.Label(options_frame, text="Option 3: Manual Entry",
                 font=("Segoe UI", 10, "bold")).pack(anchor=tk.W)
        ttk.Label(options_frame, text="Enter Customer ID directly:",
                 font=("Segoe UI", 9), foreground="#666666").pack(anchor=tk.W, pady=(0, 5))

        manual_frame = ttk.Frame(options_frame)
        manual_frame.pack(fill=tk.X)

        manual_var = tk.StringVar()
        manual_entry = ttk.Entry(manual_frame, textvariable=manual_var, width=20)
        manual_entry.pack(side=tk.LEFT, padx=(0, 5))

        def use_manual_id():
            customer_id = manual_var.get().strip()
            if not customer_id:
                messagebox.showwarning("ID Required", "Please enter a Customer ID.")
                return

            self.gads_customer_id.set(customer_id)
            messagebox.showinfo("Success", f"Customer ID {customer_id} has been loaded!")
            search_window.destroy()

        ttk.Button(manual_frame, text="✅ Use This ID", command=use_manual_id).pack(side=tk.LEFT)

        # Close button
        ttk.Button(main_frame, text="❌ Close", command=search_window.destroy).pack(pady=(10, 0))

    def search_google_ads_account(self, search_term):
        """Search for a specific Google Ads account by customer ID or list all accessible accounts"""
        def search_thread():
            try:
                self.gads_log(f"🔍 Searching for account: '{search_term}'")
                self.gads_log("ℹ️ Note: API search by name is limited. Listing all accessible accounts instead...")

                # Get credentials
                refresh_token = self.gads_refresh_token.get().strip()
                developer_token = self.gads_developer_token.get().strip()
                manager_account = self.gads_manager_account.get().strip()

                if not all([refresh_token, developer_token]):
                    self.gads_log("❌ Missing required credentials for search")
                    messagebox.showerror("Missing Credentials", "Please fill in Google Ads API credentials first.")
                    return

                try:
                    from google.ads.googleads.client import GoogleAdsClient

                    # Configure client with hardcoded OAuth credentials (same as other functions)
                    client_config = {
                        "client_id": "************-fo4vc6ge3jct4jkafj33h19e4uv79kd1.apps.googleusercontent.com",
                        "client_secret": "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",
                        "refresh_token": refresh_token,
                        "developer_token": developer_token,
                        "login_customer_id": manager_account,
                        "use_proto_plus": True
                    }

                    client = GoogleAdsClient.load_from_dict(client_config)

                    # Check if search term is a customer ID (numeric)
                    if search_term.replace('-', '').isdigit():
                        # Search by customer ID
                        customer_id = search_term.replace('-', '')
                        self.gads_log(f"🔢 Searching by customer ID: {customer_id}")

                        try:
                            # Try to get customer details directly
                            ga_service = client.get_service("GoogleAdsService")
                            query = """
                                SELECT
                                    customer.id,
                                    customer.descriptive_name,
                                    customer.currency_code,
                                    customer.time_zone,
                                    customer.status
                                FROM customer
                                LIMIT 1
                            """

                            search_request = client.get_type("SearchGoogleAdsRequest")
                            search_request.customer_id = customer_id
                            search_request.query = query

                            results = ga_service.search(request=search_request)

                            found_accounts = []
                            for row in results:
                                customer = row.customer
                                found_accounts.append({
                                    'id': customer.id,
                                    'name': customer.descriptive_name or f"Account {customer.id}",
                                    'currency': customer.currency_code,
                                    'timezone': customer.time_zone,
                                    'status': customer.status.name
                                })
                                break

                            if found_accounts:
                                self.gads_log(f"✅ Found account by ID: {found_accounts[0]['name']}")
                                self.show_search_results_window(found_accounts, search_term)
                            else:
                                self.gads_log(f"❌ No account found with ID: {customer_id}")
                                messagebox.showwarning("Account Not Found",
                                                     f"No account found with customer ID: {customer_id}\n\n"
                                                     "This could mean:\n"
                                                     "• The account doesn't exist\n"
                                                     "• You don't have access to this account\n"
                                                     "• The customer ID is incorrect")

                        except Exception as e:
                            self.gads_log(f"❌ Error searching by customer ID: {str(e)}")
                            messagebox.showerror("Search Error",
                                               f"Could not access account {customer_id}:\n{str(e)}\n\n"
                                               "You may not have permission to access this account.")

                    else:
                        # Search by name - this is more complex as we need to search through accessible accounts
                        self.gads_log(f"📝 Searching by account name: '{search_term}'")
                        self.gads_log("ℹ️ API name search is limited to directly accessible accounts only")
                        self.gads_log("💡 This is different from the web interface which shows manager account relationships")

                        # Get accessible customers and filter by name
                        customer_service = client.get_service("CustomerService")
                        accessible_customers = customer_service.list_accessible_customers()

                        found_accounts = []
                        ga_service = client.get_service("GoogleAdsService")

                        for resource_name in accessible_customers.resource_names:
                            try:
                                customer_id = resource_name.split('/')[-1]

                                query = """
                                    SELECT
                                        customer.id,
                                        customer.descriptive_name,
                                        customer.currency_code,
                                        customer.time_zone,
                                        customer.status
                                    FROM customer
                                    LIMIT 1
                                """

                                search_request = client.get_type("SearchGoogleAdsRequest")
                                search_request.customer_id = customer_id
                                search_request.query = query

                                results = ga_service.search(request=search_request)

                                for row in results:
                                    customer = row.customer
                                    account_name = customer.descriptive_name or f"Account {customer.id}"

                                    # Check if search term matches account name (case insensitive)
                                    if search_term.lower() in account_name.lower():
                                        found_accounts.append({
                                            'id': customer.id,
                                            'name': account_name,
                                            'currency': customer.currency_code,
                                            'timezone': customer.time_zone,
                                            'status': customer.status.name
                                        })
                                    break

                            except Exception:
                                continue  # Skip inaccessible accounts

                        if found_accounts:
                            self.gads_log(f"✅ Found {len(found_accounts)} matching accounts")
                            self.show_search_results_window(found_accounts, search_term)
                        else:
                            self.gads_log(f"❌ No accounts found matching: '{search_term}'")
                            self.gads_log("💡 Remember: API search is limited to directly accessible accounts")
                            messagebox.showwarning("No Matches Found",
                                                 f"No accounts found matching: '{search_term}'\n\n"
                                                 "🔍 Why this might happen:\n"
                                                 "• API search is limited to directly accessible accounts\n"
                                                 "• The account may only be accessible through manager relationships\n"
                                                 "• Account name spelling might be different\n\n"
                                                 "💡 Solutions:\n"
                                                 "• Use the exact Customer ID if you know it\n"
                                                 "• Try 'List All Accessible Accounts' to see what's available\n"
                                                 "• Check the account name in the Google Ads web interface")

                except ImportError:
                    self.gads_log("⚠️ Google Ads API library not installed")
                    messagebox.showerror("Library Missing", "Google Ads API library not installed.\n\nPlease install: pip install google-ads")

            except Exception as e:
                error_msg = f"Search failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                messagebox.showerror("Search Error", error_msg)

        # Run in separate thread
        threading.Thread(target=search_thread, daemon=True).start()

    def show_search_results_window(self, accounts, search_term):
        """Display search results in a new window"""
        # Create new window
        results_window = tk.Toplevel(self.root)
        results_window.title(f"Search Results: {search_term}")
        results_window.geometry("800x500")
        results_window.transient(self.root)
        results_window.grab_set()

        # Header
        header_frame = ttk.Frame(results_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(header_frame, text=f"🔍 Search Results for: '{search_term}'",
                 font=("Segoe UI", 14, "bold")).pack(side=tk.LEFT)

        ttk.Label(header_frame, text=f"Found {len(accounts)} matches",
                 font=("Segoe UI", 10), foreground="#666666").pack(side=tk.RIGHT)

        # Create treeview for results
        tree_frame = ttk.Frame(results_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Treeview with columns
        columns = ("ID", "Account Name", "Currency", "Time Zone", "Status")
        tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # Configure columns
        tree.heading("ID", text="Customer ID")
        tree.heading("Account Name", text="Account Name")
        tree.heading("Currency", text="Currency")
        tree.heading("Time Zone", text="Time Zone")
        tree.heading("Status", text="Status")

        # Set column widths
        tree.column("ID", width=120, anchor="center")
        tree.column("Account Name", width=300, anchor="w")
        tree.column("Currency", width=80, anchor="center")
        tree.column("Time Zone", width=150, anchor="center")
        tree.column("Status", width=100, anchor="center")

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Populate treeview with search results
        for account in accounts:
            # Format customer ID with dashes for readability
            id_str = str(account['id'])
            if len(id_str) >= 9:
                formatted_id = f"{id_str[:3]}-{id_str[3:6]}-{id_str[6:]}"
            else:
                formatted_id = id_str

            tree.insert("", tk.END, values=(
                formatted_id,
                account['name'],
                account['currency'],
                account['timezone'],
                account['status']
            ))

        # Buttons frame
        buttons_frame = ttk.Frame(results_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def select_search_result():
            """Select the highlighted search result"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select an account first.")
                return

            # Get selected account data
            item = tree.item(selection[0])
            values = item['values']
            formatted_customer_id = values[0]
            account_name = values[1]

            # Remove dashes from customer ID
            customer_id = formatted_customer_id.replace('-', '')

            # Set the customer ID in the main form
            self.gads_customer_id.set(customer_id)

            # Log the selection
            self.gads_log(f"✅ Selected search result: {account_name} ({formatted_customer_id})")

            # Save settings
            self.save_settings()

            # Close the window
            results_window.destroy()

            messagebox.showinfo("Account Selected",
                               f"Selected account: {account_name}\nCustomer ID: {formatted_customer_id}")

        def new_search():
            """Start a new search"""
            results_window.destroy()
            self.view_google_ads_accounts()

        # Buttons
        ttk.Button(buttons_frame, text="Select Account",
                  command=select_search_result).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(buttons_frame, text="New Search",
                  command=new_search).pack(side=tk.LEFT, padx=5)

        ttk.Button(buttons_frame, text="Cancel",
                  command=results_window.destroy).pack(side=tk.RIGHT)

    def show_accounts_window(self, accounts):
        """Display accounts in a new window with selection capability"""
        # Create new window
        accounts_window = tk.Toplevel(self.root)
        accounts_window.title("Google Ads Accounts")
        accounts_window.geometry("900x700")
        accounts_window.transient(self.root)
        accounts_window.grab_set()

        # Header
        header_frame = ttk.Frame(accounts_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(header_frame, text="📊 Google Ads Accounts",
                 font=("Segoe UI", 14, "bold")).pack(side=tk.LEFT)

        ttk.Label(header_frame, text=f"Found {len(accounts)} accessible accounts",
                 font=("Segoe UI", 10), foreground="#666666").pack(side=tk.RIGHT)

        # Search frame
        search_frame = ttk.LabelFrame(accounts_window, text="🔍 Search for Account", padding=10)
        search_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Search instructions
        ttk.Label(search_frame, text="If you can't find your account above, search by name or customer ID:",
                 font=("Segoe UI", 9), foreground="#666666").pack(anchor=tk.W)

        # Search input frame
        search_input_frame = ttk.Frame(search_frame)
        search_input_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(search_input_frame, text="Account Name or Customer ID:").pack(side=tk.LEFT)

        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_input_frame, textvariable=search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(10, 5))

        def search_account():
            """Search for a specific account by name or customer ID"""
            search_term = search_var.get().strip()
            if not search_term:
                messagebox.showwarning("Search Required", "Please enter an account name or customer ID to search.")
                return

            # Close current window and perform search
            accounts_window.destroy()
            self.search_google_ads_account(search_term)

        ttk.Button(search_input_frame, text="Search", command=search_account).pack(side=tk.LEFT)

        # Manual entry frame
        manual_frame = ttk.Frame(search_frame)
        manual_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(manual_frame, text="Or enter Customer ID directly:").pack(side=tk.LEFT)

        manual_var = tk.StringVar()
        manual_entry = ttk.Entry(manual_frame, textvariable=manual_var, width=20)
        manual_entry.pack(side=tk.LEFT, padx=(10, 5))

        def set_manual_id():
            """Set customer ID manually"""
            customer_id = manual_var.get().strip().replace('-', '')
            if not customer_id:
                messagebox.showwarning("ID Required", "Please enter a customer ID.")
                return

            if not customer_id.isdigit():
                messagebox.showerror("Invalid ID", "Customer ID must contain only numbers.")
                return

            # Set the customer ID
            self.gads_customer_id.set(customer_id)
            self.gads_log(f"✅ Manually set customer ID: {customer_id}")
            self.save_settings()
            accounts_window.destroy()
            messagebox.showinfo("Customer ID Set", f"Customer ID set to: {customer_id}")

        ttk.Button(manual_frame, text="Set ID", command=set_manual_id).pack(side=tk.LEFT)

        # Create treeview for accounts
        tree_frame = ttk.Frame(accounts_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Treeview with columns
        columns = ("ID", "Account Name", "Currency", "Time Zone", "Status")
        tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=20)

        # Configure columns
        tree.heading("ID", text="Customer ID")
        tree.heading("Account Name", text="Account Name")
        tree.heading("Currency", text="Currency")
        tree.heading("Time Zone", text="Time Zone")
        tree.heading("Status", text="Status")

        # Set column widths
        tree.column("ID", width=120, anchor="center")
        tree.column("Account Name", width=300, anchor="w")
        tree.column("Currency", width=80, anchor="center")
        tree.column("Time Zone", width=150, anchor="center")
        tree.column("Status", width=100, anchor="center")

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Populate treeview with accounts
        for account in accounts:
            # Format customer ID with dashes for readability
            # Convert integer ID to string first
            id_str = str(account['id'])
            if len(id_str) >= 9:  # Standard Google Ads customer ID format
                formatted_id = f"{id_str[:3]}-{id_str[3:6]}-{id_str[6:]}"
            else:
                formatted_id = id_str  # Use as-is if shorter than expected

            tree.insert("", tk.END, values=(
                formatted_id,
                account['name'],
                account['currency'],
                account['timezone'],
                account['status']
            ))

        # Buttons frame
        buttons_frame = ttk.Frame(accounts_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def select_account():
            """Select the highlighted account and set it as customer ID"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select an account first.")
                return

            # Get selected account data
            item = tree.item(selection[0])
            values = item['values']
            formatted_customer_id = values[0]  # Formatted ID with dashes
            account_name = values[1]

            # Remove dashes from customer ID for API usage
            customer_id = formatted_customer_id.replace('-', '')

            # Set the customer ID in the main form
            self.gads_customer_id.set(customer_id)

            # Log the selection
            self.gads_log(f"✅ Selected account: {account_name} ({formatted_customer_id})")

            # Save settings
            self.save_settings()

            # Close the window
            accounts_window.destroy()

            messagebox.showinfo("Account Selected",
                               f"Selected account: {account_name}\nCustomer ID: {formatted_customer_id}")

        def refresh_accounts():
            """Refresh the accounts list"""
            accounts_window.destroy()
            self.view_google_ads_accounts()

        # Buttons
        ttk.Button(buttons_frame, text="Select Account",
                  command=select_account, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Refresh",
                  command=refresh_accounts, width=10).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Close",
                  command=accounts_window.destroy, width=10).pack(side=tk.RIGHT)

        # Double-click to select
        tree.bind("<Double-1>", lambda e: select_account())

        # Focus on the window
        accounts_window.focus_set()

    # === SETTINGS PERSISTENCE METHODS ===

    def load_settings(self):
        """Load saved settings from configuration"""
        try:
            config = self.config_manager.config

            # Load Google Ads credentials with hardcoded defaults
            gads_creds = config.get("google_ads_credentials", {})
            if hasattr(self, 'gads_customer_id'):
                self.gads_customer_id.set(gads_creds.get("customer_id", ""))
            if hasattr(self, 'gads_developer_token'):
                # Use hardcoded default if empty or not set
                saved_token = gads_creds.get("developer_token", "")
                default_token = "bGBR3u83ozZjmx1WXckSkQ"
                self.gads_developer_token.set(saved_token if saved_token else default_token)
            if hasattr(self, 'gads_refresh_token'):
                # Use hardcoded default if empty or not set
                saved_refresh = gads_creds.get("refresh_token", "")
                default_refresh = "1//01NsefTx-HCBvCgYIARAAGAESNwF-L9IrG7rF3r0GzraiGQPO-cBWzppPxA0pggmi8KtWhZqpSZBDUAG3J_qbKx1xWo06hY9GaSc"
                self.gads_refresh_token.set(saved_refresh if saved_refresh else default_refresh)
            if hasattr(self, 'gads_manager_account'):
                self.gads_manager_account.set(gads_creds.get("manager_account_id", "**********"))

            # Load Google Ads Airtable settings
            gads_airtable = config.get("google_ads_airtable", {})
            if hasattr(self, 'gads_airtable_api_key'):
                self.gads_airtable_api_key.set(gads_airtable.get("api_key", ""))
            if hasattr(self, 'gads_airtable_base'):
                self.gads_airtable_base.set(gads_airtable.get("base_id", ""))
            if hasattr(self, 'gads_airtable_table'):
                self.gads_airtable_table.set(gads_airtable.get("table_id", ""))
            if hasattr(self, 'gads_sync_mode'):
                self.gads_sync_mode.set(gads_airtable.get("sync_mode", "incremental"))
            if hasattr(self, 'gads_auto_sync'):
                self.gads_auto_sync.set(gads_airtable.get("auto_sync", False))

            # Load Google Ads settings
            gads_settings = config.get("google_ads_settings", {})
            if hasattr(self, 'gads_date_preset'):
                self.gads_date_preset.set(gads_settings.get("last_date_preset", "Last 30 days"))

            # Load window settings
            window_settings = config.get("window_settings", {})
            geometry = window_settings.get("geometry", "1000x900")
            self.root.geometry(geometry)

            print("✅ Settings loaded successfully")

        except Exception as e:
            print(f"⚠️ Error loading settings: {e}")

    def save_settings(self):
        """Save current settings to configuration"""
        try:
            config = self.config_manager.config

            # Save Google Ads credentials
            if hasattr(self, 'gads_customer_id'):
                config["google_ads_credentials"]["customer_id"] = self.gads_customer_id.get()
            if hasattr(self, 'gads_developer_token'):
                config["google_ads_credentials"]["developer_token"] = self.gads_developer_token.get()
            if hasattr(self, 'gads_refresh_token'):
                config["google_ads_credentials"]["refresh_token"] = self.gads_refresh_token.get()
            if hasattr(self, 'gads_manager_account'):
                config["google_ads_credentials"]["manager_account_id"] = self.gads_manager_account.get()

            # Save Google Ads Airtable settings
            if hasattr(self, 'gads_airtable_api_key'):
                config["google_ads_airtable"]["api_key"] = self.gads_airtable_api_key.get()
            if hasattr(self, 'gads_airtable_base'):
                config["google_ads_airtable"]["base_id"] = self.gads_airtable_base.get()
            if hasattr(self, 'gads_airtable_table'):
                config["google_ads_airtable"]["table_id"] = self.gads_airtable_table.get()
            if hasattr(self, 'gads_sync_mode'):
                config["google_ads_airtable"]["sync_mode"] = self.gads_sync_mode.get()
            if hasattr(self, 'gads_auto_sync'):
                config["google_ads_airtable"]["auto_sync"] = self.gads_auto_sync.get()

            # Save Google Ads settings
            if hasattr(self, 'gads_date_preset'):
                config["google_ads_settings"]["last_date_preset"] = self.gads_date_preset.get()

            # Save window settings
            config["window_settings"]["geometry"] = self.root.geometry()

            # Save to file
            if self.config_manager.save_config():
                print("✅ Settings saved successfully")
                return True
            else:
                print("❌ Failed to save settings")
                return False

        except Exception as e:
            print(f"❌ Error saving settings: {e}")
            return False

    def on_closing(self):
        """Handle application closing"""
        try:
            # Save settings before closing
            self.save_settings()
            self.root.destroy()
        except Exception as e:
            print(f"Error during closing: {e}")
            self.root.destroy()

    def fetch_airtable_bases_gads(self):
        """Fetch available Airtable bases for Google Ads Hub"""
        api_key = self.gads_airtable_api_key.get().strip()

        if not api_key:
            self.gads_log("❌ Please enter Airtable API key first")
            messagebox.showerror("Missing API Key", "Please enter your Airtable API key")
            return

        def fetch_thread():
            try:
                self.gads_log("🔍 Fetching available Airtable bases...")
                self.gads_airtable_status.set("🔄 Fetching bases...")

                # Use the same method as other tabs
                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }

                response = requests.get('https://api.airtable.com/v0/meta/bases',
                                      headers=headers, timeout=10)

                if response.status_code == 200:
                    bases_data = response.json()
                    bases = bases_data.get('bases', [])

                    self.gads_available_bases = {}
                    base_names = []

                    for base in bases:
                        base_name = base.get('name', 'Unknown')
                        base_id = base.get('id', '')
                        self.gads_available_bases[base_name] = base_id
                        base_names.append(base_name)

                    # Update combobox
                    self.gads_base_combo['values'] = base_names

                    self.gads_log(f"✅ Found {len(bases)} Airtable bases")
                    self.gads_airtable_status.set(f"✅ {len(bases)} bases available")

                    if bases:
                        self.gads_log("📋 Available bases:")
                        for name in base_names[:5]:  # Show first 5
                            self.gads_log(f"   • {name}")
                        if len(base_names) > 5:
                            self.gads_log(f"   • ... and {len(base_names) - 5} more")

                else:
                    error_msg = f"Failed to fetch bases: {response.status_code}"
                    self.gads_log(f"❌ {error_msg}")
                    self.gads_airtable_status.set("❌ Failed to fetch bases")
                    messagebox.showerror("Fetch Failed", error_msg)

            except Exception as e:
                error_msg = f"Error fetching bases: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                self.gads_airtable_status.set("❌ Connection error")
                messagebox.showerror("Fetch Error", error_msg)

        threading.Thread(target=fetch_thread, daemon=True).start()

    def fetch_google_ads_tables(self):
        """Fetch Google Ads tables from selected base"""
        api_key = self.gads_airtable_api_key.get().strip()
        base_name = self.gads_airtable_base.get().strip()

        if not api_key or not base_name:
            self.gads_log("❌ Please select API key and base first")
            messagebox.showerror("Missing Selection", "Please enter API key and select a base first")
            return

        base_id = self.gads_available_bases.get(base_name)
        if not base_id:
            self.gads_log("❌ Invalid base selection")
            return

        def fetch_thread():
            try:
                self.gads_log(f"🔍 Fetching tables from base: {base_name}")
                self.gads_airtable_status.set("🔄 Fetching tables...")

                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }

                response = requests.get(f'https://api.airtable.com/v0/meta/bases/{base_id}/tables',
                                      headers=headers, timeout=10)

                if response.status_code == 200:
                    tables_data = response.json()
                    tables = tables_data.get('tables', [])

                    # Filter for Google Ads related tables
                    google_ads_tables = []
                    google_ads_keywords = ['google', 'ads', 'gads', 'adwords', 'campaign', 'advertising']

                    for table in tables:
                        table_name = table.get('name', '').lower()
                        table_id = table.get('id', '')

                        # Check if table name contains Google Ads keywords
                        if any(keyword in table_name for keyword in google_ads_keywords):
                            google_ads_tables.append({
                                'name': table.get('name', 'Unknown'),
                                'id': table_id
                            })

                    # If no Google Ads tables found, show all tables
                    if not google_ads_tables:
                        google_ads_tables = [{
                            'name': table.get('name', 'Unknown'),
                            'id': table.get('id', '')
                        } for table in tables]
                        self.gads_log("⚠️ No Google Ads tables detected, showing all tables")

                    # Update combobox
                    self.gads_available_tables = {}
                    table_names = []

                    for table in google_ads_tables:
                        table_name = table['name']
                        table_id = table['id']
                        self.gads_available_tables[table_name] = table_id
                        table_names.append(table_name)

                    self.gads_table_combo['values'] = table_names

                    self.gads_log(f"✅ Found {len(google_ads_tables)} suitable tables")
                    self.gads_airtable_status.set(f"✅ {len(google_ads_tables)} tables available")

                    if google_ads_tables:
                        self.gads_log("📊 Available Google Ads tables:")
                        for table in google_ads_tables:
                            self.gads_log(f"   • {table['name']}")

                else:
                    error_msg = f"Failed to fetch tables: {response.status_code}"
                    self.gads_log(f"❌ {error_msg}")
                    self.gads_airtable_status.set("❌ Failed to fetch tables")
                    messagebox.showerror("Fetch Failed", error_msg)

            except Exception as e:
                error_msg = f"Error fetching tables: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                self.gads_airtable_status.set("❌ Connection error")
                messagebox.showerror("Fetch Error", error_msg)

        threading.Thread(target=fetch_thread, daemon=True).start()

    def test_airtable_connection_gads(self):
        """Test Airtable connection for Google Ads Hub"""
        api_key = self.gads_airtable_api_key.get().strip()
        base_name = self.gads_airtable_base.get().strip()
        table_name = self.gads_airtable_table.get().strip()

        if not all([api_key, base_name, table_name]):
            self.gads_log("❌ Please configure all Airtable settings first")
            messagebox.showerror("Missing Configuration",
                               "Please enter API key and select base and table")
            return

        base_id = self.gads_available_bases.get(base_name)
        table_id = self.gads_available_tables.get(table_name)

        if not base_id or not table_id:
            self.gads_log("❌ Invalid base or table selection")
            return

        def test_thread():
            try:
                self.gads_log("🔍 Testing Airtable connection...")
                self.gads_airtable_status.set("🔄 Testing connection...")

                # Create temporary manager for testing
                temp_manager = EnhancedAirtableManager()
                temp_manager.set_credentials(api_key, base_id, table_id)

                success, message = temp_manager.test_connection()

                if success:
                    self.gads_log("✅ Airtable connection successful!")
                    self.gads_airtable_status.set("✅ Connected successfully")

                    # Get some basic stats
                    records = temp_manager.get_records(max_records=10)
                    self.gads_log(f"📊 Table contains data (sample: {len(records)} records)")

                    messagebox.showinfo("Connection Success",
                                      f"✅ Successfully connected to Airtable!\n\nBase: {base_name}\nTable: {table_name}")
                else:
                    self.gads_log(f"❌ Connection failed: {message}")
                    self.gads_airtable_status.set("❌ Connection failed")
                    messagebox.showerror("Connection Failed", f"Failed to connect:\n{message}")

            except Exception as e:
                error_msg = f"Connection test failed: {str(e)}"
                self.gads_log(f"❌ {error_msg}")
                self.gads_airtable_status.set("❌ Test failed")
                messagebox.showerror("Test Failed", error_msg)

        threading.Thread(target=test_thread, daemon=True).start()


def main():
    """Main function"""
    print("🚀 RL Dashboard Customizer v3.0 Complete")
    print("=" * 50)
    print("Features:")
    print("✅ Full GUI interface")
    print("✅ Uses base template (clean production template)")
    print("✅ Dynamic tab management")
    print("✅ Store title customization")
    print("✅ Comprehensive verification")
    print("✅ Detailed generation reports")
    print("=" * 50)

    try:
        app = RLDashboardCustomizerV3Complete()
        app.run()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
