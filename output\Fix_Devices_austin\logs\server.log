2025-07-20 16:12:15 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-20 16:12:15 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\Tools\output\Fix_Devices_austin
2025-07-20 16:12:15 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-20 16:12:15 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-20 16:12:15 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-20 16:12:15 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-20 16:12:15 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-20 16:12:24 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:24] "GET / HTTP/1.1" 200 -
2025-07-20 16:12:24 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:24] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-20 16:12:24 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:24] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-20 16:12:24 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:24] "GET /client-config.js HTTP/1.1" 200 -
2025-07-20 16:12:24 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:24] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-20 16:12:25 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:25] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-20 16:12:25 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-20 16:12:25 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:25] "GET /api/airtable/records?baseId=appDZpet4V3mQxj1S&tableId=tblJTqbxeqm5lEaHQ HTTP/1.1" 200 -
2025-07-20 16:12:27 | INFO     | root | Server-side pagination complete: 264 total records from 3 pages
2025-07-20 16:12:27 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 16:12:27] "GET /api/airtable/records?baseId=appDZpet4V3mQxj1S&tableId=tblE0GD3uSy9BcLre HTTP/1.1" 200 -
2025-07-21 10:31:08 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-21 10:31:08 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\Tools\output\Fix_Devices_austin
2025-07-21 10:31:08 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-21 10:31:08 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-21 10:31:08 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-21 10:31:08 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-21 10:31:08 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "GET / HTTP/1.1" 200 -
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "GET /styles.css HTTP/1.1" 200 -
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "GET /client-config.js HTTP/1.1" 200 -
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-21 10:31:17 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:17] "GET /script.js HTTP/1.1" 200 -
2025-07-21 10:31:20 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-21 10:31:20 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:20] "GET /api/airtable/records?baseId=appDZpet4V3mQxj1S&tableId=tblJTqbxeqm5lEaHQ HTTP/1.1" 200 -
2025-07-21 10:31:21 | INFO     | root | Server-side pagination complete: 264 total records from 3 pages
2025-07-21 10:31:21 | INFO     | werkzeug | 127.0.0.1 - - [21/Jul/2025 10:31:21] "GET /api/airtable/records?baseId=appDZpet4V3mQxj1S&tableId=tblE0GD3uSy9BcLre HTTP/1.1" 200 -
