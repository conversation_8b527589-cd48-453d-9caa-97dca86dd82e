/**
 * CENTRALIZED CLIENT CONFIGURATION FOR QUICK FIX
 * Single source of truth for all client-specific settings
 *
 * Generated by RL Dashboard Customizer v3.0
 * Client: Quick Fix
 * Base ID: app7ffftdM6e3yekG
 * Generated: 2025-07-15 12:34:42
 */



// Legacy AIRTABLE_CONFIG for backward compatibility with script.js
window.AIRTABLE_CONFIG = {
    baseId: 'app7ffftdM6e3yekG',
    tables: {
            ghl: 'tblcdFVUC3zJrbmNf',                           // GHL
            googleAds: 'tblRBXdh6L6zm9CZn',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
    }
};

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'Quick Fix',
    businessName: 'Quick Fix',
    storeName: 'Quick Fix', // Display name for header

    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data

    // ===== GOOGLE ADS LOCATION CONFIGURATION =====
    // Configuration for extracting locations from Google Ads campaign names
    googleAdsLocations: {
        // List of location names to extract from campaign names
        // These should match the location names used in your Google Ads campaigns
        names: ['Daphne', 'Mobile', 'Foley'],

        // Optional: Custom icons for each location (emoji or FontAwesome class)
        icons: {
            'Daphne': '🏪',
            'Mobile': '📱',
            'Foley': '🏢'
        },

        // Optional: Custom colors for location cards
        colors: {
            'Daphne': '#4CAF50',
            'Mobile': '#2196F3',
            'Foley': '#FF9800'
        },

        // Optional: Location aliases (alternative names that should map to the same location)
        aliases: {
            'Daphne': ['Daphne Store', 'Daphne Location'],
            'Mobile': ['Mobile Store', 'Mobile Location'],
            'Foley': ['Foley Store', 'Foley Location']
        },

        // Matching options
        caseSensitive: false, // Whether location matching should be case-sensitive
        partialMatch: true,   // Whether to allow partial matches (e.g., "Daphne" matches "Daphne Store")

        // Default icon for unknown locations
        defaultIcon: '📍',
        defaultColor: '#6c757d'
    },

    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'app7ffftdM6e3yekG',

        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblcdFVUC3zJrbmNf',                           // GHL
            googleAds: 'tblRBXdh6L6zm9CZn',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
        }
    },

    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources (customizer will set based on client needs)
        enabled: ["ghl", "googleAds"],

        // Disabled data sources (will be hidden from UI)
        disabled: ["pos", "metaAds"],

        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }
    },

    // ===== HELPER FUNCTIONS =====

    /**
     * Check if a data source is enabled
     */
    isEnabled: function(dataSource) {
        return this.dataSources.enabled.includes(dataSource);
    },

    /**
     * Get table ID for a specific data source
     */
    getTableId: function(dataSource) {
        return this.airtable.tables[dataSource] || null;
    },

    /**
     * Get base ID
     */
    getBaseId: function() {
        return this.airtable.baseId;
    },

    /**
     * Update store name (for customizer use)
     */
    updateStoreName: function(newStoreName) {
        this.storeName = newStoreName;
        this.businessName = newStoreName;
        this.clientName = newStoreName;

        // Update header immediately if DOM is ready
        const storeNameElement = document.getElementById('store-name');
        if (storeNameElement) {
            storeNameElement.textContent = newStoreName;

        }
    },

    /**
     * Populate location dropdowns from GHL data
     */
    populateLocationsFromData: function(ghlData) {
        console.log('[CONFIG] 🔍 populateLocationsFromData called with data:', ghlData ? ghlData.length : 0, 'records');
        console.log('[CONFIG] 🔍 googleAdsLocations config:', this.googleAdsLocations);

        // Check if we're in single location mode
        const hideLocationPerformance = this.googleAdsLocations && this.googleAdsLocations.hideLocationPerformance;
        console.log('[CONFIG] 🔍 hideLocationPerformance flag:', hideLocationPerformance);

        if (hideLocationPerformance) {
            console.log('[CONFIG] ✅ Single location mode detected via hideLocationPerformance - using business name for location dropdown');
            this.updateLocationDropdownsForSingleLocation();
            return;
        }

        if (!ghlData || !Array.isArray(ghlData)) {
            console.warn('[CONFIG] No GHL data provided for location population');
            return;
        }

        // Extract unique locations from GHL data with multiple field name variations
        const locationFields = ['Location', 'location', 'LOCATION', 'store', 'Store', 'branch', 'Branch'];
        console.log('[CONFIG] 🔍 Checking location fields:', locationFields);

        const locations = [...new Set(
            ghlData
                .map(record => {
                    // Try multiple possible field names for location
                    for (const field of locationFields) {
                        if (record[field] && typeof record[field] === 'string' && record[field].trim()) {
                            return record[field].trim();
                        }
                    }
                    return null;
                })
                .filter(location => location)
        )].sort();

        console.log('[CONFIG] 🔍 Extracted locations:', locations);
        console.log('[CONFIG] 🔍 Location count:', locations.length);
        this.locations = locations;


        if (locations.length === 0) {
            console.warn('[CONFIG] No locations found in GHL data. Check field names.');
            // Log sample record to help debug field names
            if (ghlData.length > 0) {
                console.log('[CONFIG] Sample GHL record fields:', Object.keys(ghlData[0]));
            }

            // 🎯 NEW: Treat no locations as single location mode
            console.log('[CONFIG] No locations detected - treating as single location mode');
            this.updateLocationDropdownsForSingleLocation();
            return;
        }

        // 🎯 NEW: Check if we only have 1 location (also treat as single location mode)
        if (locations.length === 1) {
            console.log('[CONFIG] ✅ Only 1 location detected - switching to single location mode');
            console.log('[CONFIG] 🔍 Single location name:', locations[0]);
            this.updateLocationDropdownsForSingleLocation();
            return;
        }

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Store the "All Locations" option
                const allOptionText = dropdown.querySelector('option[value="all"]')?.textContent || 'All Locations';

                // Clear ALL existing options (including hardcoded ones)
                dropdown.innerHTML = '';

                // Re-add "All Locations" option first
                const allOption = document.createElement('option');
                allOption.value = 'all';
                allOption.textContent = allOptionText;
                dropdown.appendChild(allOption);

                // Add dynamic location options
                locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location;
                    option.textContent = location;
                    dropdown.appendChild(option);
                });


            } else {
                console.warn(`[CONFIG] Dropdown not found: ${dropdownId}`);
            }
        });

        // Force a refresh of any existing filters
        this.refreshLocationFilters();

        // 🎯 NEW: Show header controls for multi-location mode
        this.updateHeaderForMultiLocation();
    },

    /**
     * Refresh location filters after population
     */
    refreshLocationFilters: function() {
        // Trigger change events to refresh any dependent UI elements
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Reset to "all" and trigger change event
                dropdown.value = 'all';
                dropdown.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
    },

    /**
     * Update location dropdowns for single location mode
     */
    updateLocationDropdownsForSingleLocation: function() {
        // Get the business name from configuration
        const businessName = this.businessName || this.clientName || 'Store';
        const locationName = businessName.includes('Store') ? businessName : `${businessName} Store`;

        console.log(`[CONFIG] Updating location dropdowns for single location: ${locationName}`);

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Clear existing options
                dropdown.innerHTML = '';

                // Add single location option
                const option = document.createElement('option');
                option.value = 'all';
                option.textContent = locationName;
                dropdown.appendChild(option);

                console.log(`[CONFIG] Updated dropdown: ${dropdownId} with single location: ${locationName}`);
            } else {
                console.warn(`[CONFIG] Dropdown not found: ${dropdownId}`);
            }
        });

        // 🎯 NEW: Hide header controls and show "Single Location Mode" for single location
        this.updateHeaderForSingleLocation();
    },

    /**
     * Update header controls for single location mode
     */
    updateHeaderForSingleLocation: function() {
        console.log('[CONFIG] 🎯 updateHeaderForSingleLocation called');
        console.log('[CONFIG] 🔍 Document ready state:', document.readyState);

        // Check if DOM is ready, if not, delay execution
        if (document.readyState === 'loading') {
            console.log('[CONFIG] ⏳ DOM not ready, delaying header update');
            document.addEventListener('DOMContentLoaded', () => {
                this.updateHeaderForSingleLocation();
            });
            return;
        }

        // Hide header filter controls
        const elementsToHide = [
            'date-filter',
            'location-filter',
            'custom-range-toggle',
            'custom-date-range'
        ];

        elementsToHide.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
                console.log(`[CONFIG] ✅ Hidden header element: ${elementId}`);
            } else {
                console.warn(`[CONFIG] ❌ Element not found: ${elementId}`);
            }
        });

        // Hide the labels too
        const labelsToHide = document.querySelectorAll('label[for="date-filter"], label[for="location-filter"]');
        labelsToHide.forEach(label => {
            label.style.display = 'none';
        });

        // Create and show "Single Location Mode" text
        this.showSingleLocationModeText();
    },

    /**
     * Show "Single Location Mode" text in header
     */
    showSingleLocationModeText: function() {
        // Check if text already exists
        let modeText = document.getElementById('single-location-mode-text');

        if (!modeText) {
            // Create the text element
            modeText = document.createElement('div');
            modeText.id = 'single-location-mode-text';
            modeText.className = 'single-location-mode';
            modeText.textContent = 'Single Location Mode';

            // Find the filter container and add the text
            const filterContainer = document.querySelector('.filter-container');
            if (filterContainer) {
                filterContainer.appendChild(modeText);
                console.log('[CONFIG] Added "Single Location Mode" text to header');
            }
        } else {
            // Show existing text
            modeText.style.display = 'block';
        }
    },

    /**
     * Update header controls for multi-location mode
     */
    updateHeaderForMultiLocation: function() {
        console.log('[CONFIG] Updating header for multi-location mode');

        // Show header filter controls
        const elementsToShow = [
            'date-filter',
            'location-filter',
            'custom-range-toggle'
        ];

        elementsToShow.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = '';
                console.log(`[CONFIG] Shown header element: ${elementId}`);
            }
        });

        // Show the labels too
        const labelsToShow = document.querySelectorAll('label[for="date-filter"], label[for="location-filter"]');
        labelsToShow.forEach(label => {
            label.style.display = '';
        });

        // Hide "Single Location Mode" text
        this.hideSingleLocationModeText();
    },

    /**
     * Hide "Single Location Mode" text in header
     */
    hideSingleLocationModeText: function() {
        const modeText = document.getElementById('single-location-mode-text');
        if (modeText) {
            modeText.style.display = 'none';
            console.log('[CONFIG] Hidden "Single Location Mode" text');
        }
    },

    /**
     * Clear cached data for fresh configuration
     */
    clearCache: function() {
        // Clear any cached data that might interfere with new configuration
        if (typeof localStorage !== 'undefined') {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('airtable') || key.includes('dashboard'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));

        }
    },

    // ===== GOOGLE ADS LOCATION CONFIGURATION HELPERS =====

    /**
     * Update Google Ads location configuration for a new client
     * @param {Array} locationNames - Array of location names (e.g., ['Downtown', 'Uptown', 'Westside'])
     * @param {Object} options - Optional configuration (icons, colors, aliases, etc.)
     */
    updateGoogleAdsLocations: function(locationNames, options = {}) {
        if (!Array.isArray(locationNames) || locationNames.length === 0) {
            console.error('[CONFIG] Invalid location names provided. Must be a non-empty array.');
            return false;
        }

        // Update the location names
        this.googleAdsLocations.names = locationNames;

        // Update optional configurations
        if (options.icons && typeof options.icons === 'object') {
            this.googleAdsLocations.icons = { ...this.googleAdsLocations.icons, ...options.icons };
        }

        if (options.colors && typeof options.colors === 'object') {
            this.googleAdsLocations.colors = { ...this.googleAdsLocations.colors, ...options.colors };
        }

        if (options.aliases && typeof options.aliases === 'object') {
            this.googleAdsLocations.aliases = { ...this.googleAdsLocations.aliases, ...options.aliases };
        }

        if (typeof options.caseSensitive === 'boolean') {
            this.googleAdsLocations.caseSensitive = options.caseSensitive;
        }

        if (typeof options.partialMatch === 'boolean') {
            this.googleAdsLocations.partialMatch = options.partialMatch;
        }

        if (options.defaultIcon) {
            this.googleAdsLocations.defaultIcon = options.defaultIcon;
        }

        if (options.defaultColor) {
            this.googleAdsLocations.defaultColor = options.defaultColor;
        }



        return true;
    },

    /**
     * Get current Google Ads location configuration
     */
    getGoogleAdsLocations: function() {
        return this.googleAdsLocations;
    },

    /**
     * Quick setup for common client scenarios
     */
    setupQuickFixLocations: function() {
        return this.updateGoogleAdsLocations(['Daphne', 'Mobile', 'Foley'], {
            icons: { 'Daphne': '🏪', 'Mobile': '📱', 'Foley': '🏢' },
            colors: { 'Daphne': '#4CAF50', 'Mobile': '#2196F3', 'Foley': '#FF9800' }
        });
    },

    /**
     * Example setup for a client with different locations
     */
    setupExampleClient: function() {
        return this.updateGoogleAdsLocations(['Downtown', 'Uptown', 'Westside', 'Eastside'], {
            icons: {
                'Downtown': '🏙️',
                'Uptown': '🏘️',
                'Westside': '🌅',
                'Eastside': '🌄'
            },
            colors: {
                'Downtown': '#FF5722',
                'Uptown': '#9C27B0',
                'Westside': '#FF9800',
                'Eastside': '#4CAF50'
            },
            aliases: {
                'Downtown': ['Downtown Store', 'City Center'],
                'Uptown': ['Uptown Location', 'North Store'],
                'Westside': ['West Location', 'Sunset Store'],
                'Eastside': ['East Location', 'Sunrise Store']
            }
        });
    },

    // Tab visibility management
    updateTabVisibility: function(dataAvailability = {}) {


        const tabMappings = {
            'pos': [
                // Sales Report tab button and content (POS is integrated into Sales Report)
                'button[data-tab="sales-report"]', // Tab button
                'sales-report', // Tab content
                // POS sections within Sales Report tab (actual HTML elements)
                '#locationRevenueChart', '#locationTransactionsChart',
                '.chart-card:has(#locationRevenueChart)', '.chart-card:has(#locationTransactionsChart)'
            ],
            'metaAds': [
                'button[data-tab="meta-ads-report"]', // Tab button
                'meta-ads-report' // Tab content
            ],
            'googleAds': [
                'button[data-tab="google-ads-report"]', // Tab button
                'google-ads-report' // Tab content
            ],
            'ghl': [
                'button[data-tab="lead-report"]', // Tab button
                'lead-report' // Tab content
            ],
            'masterOverview': [
                'button[data-tab="master-overview"]', // Tab button
                'master-overview' // Tab content
            ]
        };

        // Check each data source
        Object.keys(tabMappings).forEach(dataSource => {
            try {
                const isEnabled = this.isEnabled(dataSource);
                const hasData = dataAvailability[dataSource] || false;
                const shouldShow = this.shouldShowTab(dataSource, hasData);

                const elements = tabMappings[dataSource];
                this.updateTabElements(elements, shouldShow, dataSource);
            } catch (error) {
                console.warn(`[CONFIG] Error processing tab visibility for ${dataSource}:`, error);
                // Hide the tab if there's an error
                const elements = tabMappings[dataSource];
                this.updateTabElements(elements, false, dataSource);
            }
        });

        // Handle Master Overview visibility based on available data sources
        this.updateMasterOverviewVisibility(dataAvailability);

        // Set smart default tab after all visibility updates
        this.setSmartDefaultTab(dataAvailability);

        // Hide Performance by Location section if configured
        this.updateLocationPerformanceVisibility();
    },

    shouldShowTab: function(dataSource, hasData = false) {
        // Check if data source is enabled in configuration
        if (!this.isEnabled(dataSource)) {

            return false;
        }

        // For enabled sources, check if they have data
        if (!hasData) {

            return false;
        }

        // Show if enabled and has data (or data status unknown)

        return true;
    },

    updateMasterOverviewVisibility: function(dataAvailability) {
        // Count how many data sources have data
        const enabledSources = ['ghl', 'googleAds', 'pos', 'metaAds'];
        const sourcesWithData = enabledSources.filter(source => {
            return this.isEnabled(source) && dataAvailability[source];
        });

        console.log(`[CONFIG] Data sources with data: ${sourcesWithData.length} (${sourcesWithData.join(', ')})`);

        // Hide Master Overview if only one or no data sources have data
        const shouldShowMasterOverview = sourcesWithData.length > 1;

        if (shouldShowMasterOverview) {
            console.log('[CONFIG] Showing Master Overview: multiple data sources available');
        } else {
            console.log('[CONFIG] Hiding Master Overview: only one or no data sources available');
        }

        // Update Master Overview tab visibility
        const masterOverviewElements = ['button[data-tab="master-overview"]', 'master-overview'];
        this.updateTabElements(masterOverviewElements, shouldShowMasterOverview, 'masterOverview');
    },

    setSmartDefaultTab: function(dataAvailability) {
        // Priority order: GHL > Google Ads > POS > Meta Ads
        const tabPriority = [
            { source: 'ghl', tabId: 'lead-report' },
            { source: 'googleAds', tabId: 'google-ads-report' },
            { source: 'pos', tabId: 'sales-report' },
            { source: 'metaAds', tabId: 'meta-ads-report' }
        ];

        // Find the highest priority tab that has data
        let defaultTabId = null;
        for (const tab of tabPriority) {
            if (this.isEnabled(tab.source) && dataAvailability[tab.source]) {
                defaultTabId = tab.tabId;
                console.log(`[CONFIG] Setting default tab to: ${defaultTabId} (${tab.source} has data)`);
                break;
            }
        }

        // If no data sources have data, fall back to the first enabled source
        if (!defaultTabId) {
            for (const tab of tabPriority) {
                if (this.isEnabled(tab.source)) {
                    defaultTabId = tab.tabId;
                    console.log(`[CONFIG] Fallback default tab to: ${defaultTabId} (${tab.source} is enabled)`);
                    break;
                }
            }
        }

        // Apply the default tab selection
        if (defaultTabId) {
            this.activateDefaultTab(defaultTabId);
        }
    },

    activateDefaultTab: function(tabId) {
        // Remove active class from all tabs
        const allTabButtons = document.querySelectorAll('.tab-button');
        const allTabContents = document.querySelectorAll('.tab-content');

        allTabButtons.forEach(btn => btn.classList.remove('active'));
        allTabContents.forEach(content => content.classList.remove('active'));

        // Activate the selected tab
        const targetButton = document.querySelector(`button[data-tab="${tabId}"]`);
        const targetContent = document.getElementById(tabId);

        if (targetButton && targetContent) {
            targetButton.classList.add('active');
            targetContent.classList.add('active');
            console.log(`[CONFIG] Activated default tab: ${tabId}`);
        } else {
            console.warn(`[CONFIG] Could not find tab elements for: ${tabId}`);
        }
    },

    updateLocationPerformanceVisibility: function() {
        // Check if location performance should be hidden
        const hideLocationPerformance = this.googleAdsLocations && this.googleAdsLocations.hideLocationPerformance;

        if (hideLocationPerformance) {
            console.log('[CONFIG] Hiding Performance by Location section due to configuration');

            // Hide the entire location performance section
            const locationSection = document.getElementById('location-performance-section');
            if (locationSection) {
                locationSection.style.display = 'none';
                locationSection.classList.add('hidden');
            }
        } else {
            console.log('[CONFIG] Showing Performance by Location section');

            // Show the location performance section
            const locationSection = document.getElementById('location-performance-section');
            if (locationSection) {
                locationSection.style.display = 'block';
                locationSection.classList.remove('hidden');
            }
        }
    },

    updateTabElements: function(elements, shouldShow, dataSource) {
        elements.forEach(selector => {
            // Handle different selector types
            let element;
            if (selector.startsWith('button[')) {
                element = document.querySelector(selector);
            } else if (selector.startsWith('.') || selector.startsWith('#')) {
                // CSS selector (class or ID)
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (shouldShow) {
                        el.style.display = '';
                        el.classList.remove('hidden');
                    } else {
                        el.style.display = 'none';
                        el.classList.add('hidden');

                    }
                });
                return; // Skip the single element logic below
            } else {
                element = document.getElementById(selector);
            }

            if (element) {
                if (shouldShow) {
                    element.style.display = '';
                    element.classList.remove('hidden');
                } else {
                    element.style.display = 'none';
                    element.classList.add('hidden');

                }
            }
        });
    },

    // Special function to handle POS content within Sales Report
    updateSalesReportPOSContent: function(hasData = false) {
        const shouldShow = this.shouldShowTab('pos', hasData);

        if (!shouldShow) {
            // Hide POS-specific content and show alternative message


            // Hide the Revenue by Location chart
            const revenueChart = document.getElementById('locationRevenueChart');
            if (revenueChart) {
                const revenueCard = revenueChart.closest('.chart-card');
                if (revenueCard) {
                    revenueCard.style.display = 'none';
                    revenueCard.classList.add('hidden');

                }
            }

            // Hide the Transactions by Location chart
            const transactionsChart = document.getElementById('locationTransactionsChart');
            if (transactionsChart) {
                const transactionsCard = transactionsChart.closest('.chart-card');
                if (transactionsCard) {
                    transactionsCard.style.display = 'none';
                    transactionsCard.classList.add('hidden');

                }
            }

            // Add a replacement message for the hidden POS content
            const chartRow = document.querySelector('#sales-report .chart-row');
            if (chartRow) {
                // Remove existing POS disabled message if any
                const existingMessage = chartRow.querySelector('.pos-disabled-message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                // Add a message explaining POS is not available for this client
                const posMessage = document.createElement('div');
                posMessage.className = 'pos-disabled-message chart-card';
                posMessage.style.cssText = 'text-align: center; padding: 2rem; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 1rem 0;';
                posMessage.innerHTML = `
                    <h3 style="color: #6c757d; margin-bottom: 1rem;">📊 Sales Report - Lead Data Only</h3>
                    <p style="color: #6c757d; margin: 0;">This dashboard is configured to show lead conversion data. POS integration is not enabled for this client.</p>
                `;

                // Add the message to replace the hidden charts
                chartRow.appendChild(posMessage);

            }


        }
    }
};

// Initialize configuration when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Clear any cached data for fresh start
    window.CLIENT_CONFIG.clearCache();

    // Update store name in header when DOM is ready
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement && window.CLIENT_CONFIG.storeName) {
        storeNameElement.textContent = window.CLIENT_CONFIG.storeName;

    }
});


