#!/usr/bin/env python3
"""
Client Database Recovery Tool
Helps recover and recreate client database with proper backup management
"""

import sqlite3
import os
import shutil
from datetime import datetime

def create_backup_if_exists():
    """Create backup of existing database"""
    db_path = "clients.db"
    if os.path.exists(db_path):
        backup_path = f"clients_recovery_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(db_path, backup_path)
        print(f"✅ Existing database backed up to: {backup_path}")
        return backup_path
    return None

def analyze_existing_database():
    """Analyze the current database"""
    db_path = "clients.db"
    if not os.path.exists(db_path):
        print("❌ No database file found")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get schema
        cursor.execute("PRAGMA table_info(clients)")
        columns = cursor.fetchall()
        print("\n📊 Current Database Schema:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Get data
        cursor.execute("SELECT * FROM clients")
        records = cursor.fetchall()
        print(f"\n📋 Current Records ({len(records)} total):")
        for i, record in enumerate(records, 1):
            print(f"  {i}. {record}")
        
        conn.close()
        return records
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        return None

def manual_data_entry():
    """Allow manual entry of client data"""
    print("\n🔧 Manual Client Data Entry")
    print("Enter your client data (press Enter with empty store name to finish):")
    
    clients = []
    while True:
        print(f"\n--- Client #{len(clients) + 1} ---")
        store_name = input("Store Name: ").strip()
        if not store_name:
            break
            
        customer_id = input("Customer ID: ").strip()
        if not customer_id:
            print("❌ Customer ID is required!")
            continue
            
        business_name = input("Business Name (optional): ").strip()
        notes = input("Notes (optional): ").strip()
        
        clients.append({
            'store_name': store_name,
            'customer_id': customer_id,
            'business_name': business_name or store_name,
            'notes': notes
        })
        
        print(f"✅ Added: {store_name} ({customer_id})")
    
    return clients

def create_database_with_data(clients):
    """Create new database with provided client data"""
    # Backup existing database
    backup_path = create_backup_if_exists()
    
    # Create new database
    db_path = "clients.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_name TEXT NOT NULL UNIQUE,
            customer_id TEXT NOT NULL,
            business_name TEXT,
            notes TEXT,
            created_date TEXT,
            last_updated TEXT
        )
    ''')
    
    # Insert data
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for client in clients:
        cursor.execute('''
            INSERT OR REPLACE INTO clients
            (store_name, customer_id, business_name, notes, created_date, last_updated)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            client['store_name'],
            client['customer_id'],
            client['business_name'],
            client['notes'],
            current_time,
            current_time
        ))
    
    conn.commit()
    conn.close()
    
    print(f"\n✅ Database created with {len(clients)} clients")
    if backup_path:
        print(f"💾 Previous database backed up to: {backup_path}")

def quick_setup_sample_data():
    """Quick setup with sample data for testing"""
    sample_clients = [
        {
            'store_name': 'Fix My Gadget',
            'customer_id': '1234567890',
            'business_name': 'Fix My Gadget Electronics',
            'notes': 'Main repair shop'
        },
        {
            'store_name': 'Mobile Masters',
            'customer_id': '9876543210',
            'business_name': 'Mobile Masters Inc',
            'notes': 'Phone repair specialist'
        }
    ]
    
    create_database_with_data(sample_clients)
    print("🧪 Sample database created for testing")

def main():
    print("🔧 CLIENT DATABASE RECOVERY TOOL")
    print("=" * 50)
    
    # Analyze current database
    print("\n1. Analyzing current database...")
    current_data = analyze_existing_database()
    
    print("\nWhat would you like to do?")
    print("1. Manual data entry (recreate your client list)")
    print("2. Keep current database as-is")
    print("3. Create sample database for testing")
    print("4. Exit")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        clients = manual_data_entry()
        if clients:
            create_database_with_data(clients)
        else:
            print("❌ No clients entered")
    
    elif choice == "2":
        print("✅ Keeping current database unchanged")
    
    elif choice == "3":
        quick_setup_sample_data()
    
    elif choice == "4":
        print("👋 Exiting...")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
