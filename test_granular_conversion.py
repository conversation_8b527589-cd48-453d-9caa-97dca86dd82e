#!/usr/bin/env python3
"""
Test script to verify the granular conversion data extraction fix
"""

import sys
import os
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

def test_conversion_action_query():
    """Test the new conversion_action query approach"""
    
    # Test customer ID (replace with actual)
    customer_id = "7129258052"  # Remove dashes
    start_date = "2024-07-01"
    end_date = "2024-07-21"
    
    try:
        # Initialize Google Ads client
        client = GoogleAdsClient.load_from_storage("google-ads.yaml")
        ga_service = client.get_service("GoogleAdsService")
        
        print(f"🔍 Testing conversion_action query for customer ID: {customer_id}")
        print(f"📅 Date range: {start_date} to {end_date}")
        
        # New query using conversion_action resource
        query = f"""
        SELECT
            segments.date,
            conversion_action.resource_name,
            conversion_action.name,
            conversion_action.category,
            conversion_action.type,
            conversion_action.status,
            metrics.all_conversions,
            metrics.all_conversions_value
        FROM conversion_action 
        WHERE segments.date BETWEEN '{start_date}' AND '{end_date}'
        AND metrics.all_conversions > 0
        ORDER BY segments.date, conversion_action.name
        """
        
        print(f"\n📝 Query:")
        print(query)
        
        # Make the request
        search_request = client.get_type("SearchGoogleAdsRequest")
        search_request.customer_id = customer_id
        search_request.query = query
        
        print(f"\n🚀 Executing query...")
        results = ga_service.search(request=search_request)
        
        # Process results
        data = []
        conversion_actions_seen = set()
        
        for row in results:
            # Date
            date = row.segments.date
            
            # Conversion action details from conversion_action resource
            conversion_action_resource = row.conversion_action.resource_name
            conversion_action_name = row.conversion_action.name
            conversion_action_category = row.conversion_action.category.name if hasattr(row.conversion_action.category, 'name') else str(row.conversion_action.category)
            conversion_action_type = row.conversion_action.type.name if hasattr(row.conversion_action.type, 'name') else str(row.conversion_action.type)
            conversion_action_status = row.conversion_action.status.name if hasattr(row.conversion_action.status, 'name') else str(row.conversion_action.status)
            
            # Available metrics from conversion_action resource
            all_conversions = row.metrics.all_conversions
            all_conversion_value = row.metrics.all_conversions_value
            
            # Track unique conversion actions
            conversion_actions_seen.add(conversion_action_name)
            
            data.append({
                'Date': date,
                'Conversion Action Resource': conversion_action_resource,
                'Conversion Action Name': conversion_action_name,
                'Conversion Action Category': conversion_action_category,
                'Conversion Action Type': conversion_action_type,
                'Conversion Action Status': conversion_action_status,
                'All Conversions': all_conversions,
                'All Conversion Value': all_conversion_value
            })
        
        # Display results
        if not data:
            print("❌ No granular conversion data found for the specified date range.")
        else:
            print(f"✅ Granular conversion data extraction complete!")
            print(f"📊 Found {len(data)} records across {len(conversion_actions_seen)} conversion actions")
            print(f"🎯 Conversion Actions Found:")
            
            # Group by conversion action
            action_summary = {}
            for record in data:
                action_name = record['Conversion Action Name']
                if action_name not in action_summary:
                    action_summary[action_name] = {
                        'conversions': 0,
                        'value': 0,
                        'category': record['Conversion Action Category'],
                        'type': record['Conversion Action Type'],
                        'status': record['Conversion Action Status']
                    }
                action_summary[action_name]['conversions'] += record['All Conversions']
                action_summary[action_name]['value'] += record['All Conversion Value']
            
            for action, summary in action_summary.items():
                print(f"   • {action}:")
                print(f"     - Category: {summary['category']}")
                print(f"     - Type: {summary['type']}")
                print(f"     - Status: {summary['status']}")
                print(f"     - Total Conversions: {summary['conversions']:.2f}")
                print(f"     - Total Value: ${summary['value']:.2f}")
                print()
            
            # Calculate totals
            total_conversions = sum(record['All Conversions'] for record in data)
            total_value = sum(record['All Conversion Value'] for record in data)
            
            print(f"🎯 Total All Conversions: {total_conversions:.2f}")
            print(f"💎 Total All Conversion Value: ${total_value:.2f}")
            print(f"📝 Note: Campaign-level metrics (cost, clicks, impressions) are not available")
            print(f"   when querying conversion action data directly.")
            
            # Show sample data
            print(f"\n📋 Sample data (first 3 rows):")
            for i, record in enumerate(data[:3]):
                print(f"Row {i+1}:")
                for key, value in record.items():
                    print(f"  {key}: {value}")
                print()
        
        return True
        
    except GoogleAdsException as ex:
        print(f"❌ Google Ads API Error:")
        print(f"Request ID: {ex.request_id}")
        print(f"Error: {ex.error}")
        
        for error in ex.failure.errors:
            print(f"Error details: {error.error_code} - {error.message}")
            if hasattr(error, 'location'):
                for field_path_element in error.location.field_path_elements:
                    print(f"  Field: {field_path_element.field_name}")
        
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Granular Conversion Data Extraction Fix")
    print("=" * 60)
    
    success = test_conversion_action_query()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("🎉 The conversion_action query approach works correctly!")
    else:
        print("\n❌ Test failed!")
        print("🔧 The query needs further debugging.")
