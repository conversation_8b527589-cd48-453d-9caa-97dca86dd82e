#!/usr/bin/env python3
"""
Test script for the notes parser functionality
"""

import re

def parse_notes_for_client_info(notes_text):
    """
    Parse notes text to extract business name and account ID
    
    Expected format:
    Business Name
    Repair Lift
    XXX-XXX-XXXX
    
    Examples:
    - "we fix wireless\nRepair Lift\n************"
    - "Cell Phone Fix\nRepair Lift\n703-049-0350"
    - "Colorado Gadget Fix\nRepair Lift\n847-763-5663"
    
    Returns:
    - business_name: First line, properly capitalized
    - account_id: Phone number (XXX-XXX-XXXX format)
    """
    if not notes_text or not notes_text.strip():
        return None, None
        
    try:
        # Split by various line break patterns
        lines = []
        for line in notes_text.replace('\r\n', '\n').replace('\r', '\n').split('\n'):
            line = line.strip()
            if line:  # Only add non-empty lines
                lines.append(line)
        
        if len(lines) < 2:
            return None, None
            
        # Extract business name (first line)
        business_name = lines[0].strip()
        
        # Capitalize first letter of each word if not already capitalized
        if business_name:
            # Split by spaces and capitalize each word
            words = business_name.split()
            capitalized_words = []
            for word in words:
                if word and not word[0].isupper():
                    capitalized_words.append(word.capitalize())
                else:
                    capitalized_words.append(word)
            business_name = ' '.join(capitalized_words)
        
        # Extract account ID (look for phone number pattern)
        account_id = None
        phone_pattern = r'\b\d{3}-\d{3}-\d{4}\b'  # XXX-XXX-XXXX format
        
        for line in lines:
            match = re.search(phone_pattern, line)
            if match:
                account_id = match.group()
                break
        
        return business_name, account_id
        
    except Exception as e:
        print(f"❌ Error parsing notes: {str(e)}")
        return None, None

def test_examples():
    """Test the parser with the provided examples"""
    test_cases = [
        "we fix wireless\nRepair Lift\n************",
        "Cell Phone Fix\nRepair Lift\n703-049-0350", 
        "Colorado Gadget Fix\nRepair Lift\n847-763-5663",
        # Test with different line breaks
        "we fix wireless\r\nRepair Lift\r\n************",
        # Test with extra spaces
        "  we fix wireless  \n  Repair Lift  \n  ************  ",
        # Test with missing parts
        "Just Business Name\nRepair Lift",
        "Business Name\nRepair Lift\nNo phone here",
        # Test empty
        "",
        # Test single line
        "Single line only"
    ]
    
    print("🧪 Testing Notes Parser")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}:")
        print(f"Input: {repr(test_case)}")
        
        business_name, account_id = parse_notes_for_client_info(test_case)
        
        print(f"Business Name: {business_name}")
        print(f"Account ID: {account_id}")
        print("-" * 30)

if __name__ == "__main__":
    test_examples()
