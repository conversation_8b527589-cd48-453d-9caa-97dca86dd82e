/**
 * CENTRALIZED CLIENT CONFIGURATION FOR GADGET REPAIR LV
 * Single source of truth for all client-specific settings
 *
 * Generated by RL Dashboard Customizer v3.0
 * Client: Gadget Repair LV
 * Base ID: appL4rTljQgGkjTtp
 * Generated: 2025-07-20 18:29:31
 */

console.log('🎯 LOADING GADGET REPAIR LV CONFIG FROM CORRECT FILE!');
console.log('📁 File: Gadget_Repair_LV/client-config.js');
console.log('🏢 Client: Gadget Repair LV');
console.log('🗄️ Base ID: appL4rTljQgGkjTtp');

// Legacy AIRTABLE_CONFIG for backward compatibility with script.js
window.AIRTABLE_CONFIG = {
    baseId: 'appL4rTljQgGkjTtp',
    tables: {
            ghl: 'tblQJeHV2ZCHoaSoy',                           // GHL
            googleAds: 'tblF9Ej3DKJYhhl4i',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
    }
};

window.CLIENT_CONFIG = {
    // ===== CLIENT INFORMATION =====
    clientName: 'Gadget Repair LV',
    businessName: 'Gadget Repair LV',
    storeName: 'Gadget Repair LV', // Display name for header

    // ===== BUSINESS LOCATIONS =====
    // Note: Will be dynamically populated from GHL table data
    locations: [], // Auto-populated from actual GHL data

    // ===== GOOGLE ADS LOCATION CONFIGURATION =====
    // Configuration for extracting locations from Google Ads campaign names
    googleAdsLocations: {
        // List of location names to extract from campaign names
        // These should match the location names used in your Google Ads campaigns
        names: ["Gadget Repair LV"],

        // Optional: Custom icons for each location (emoji or FontAwesome class)
        icons: {"Gadget Repair LV": "\ud83d\udd27"},

        // Optional: Custom colors for location cards
        colors: {"Gadget Repair LV": "#4CAF50"},

        // Optional: Location aliases (alternative names that should map to the same location)
        aliases: {
            'Location 1': ['Location 1 Shop', 'Location 1 Store'],
            'Location 2': ['Location 2 Shop', 'Location 2 Store'],
            'Location 3': ['Location 3 Shop', 'Location 3 Store']
        },

        // Matching options
        caseSensitive: false, // Whether location matching should be case-sensitive
        partialMatch: true,   // Whether to allow partial matches

        // Default icon for unknown locations
        defaultIcon: '📍',
        defaultColor: '#6c757d',

        // Hide location performance section if location is "main location"
        hideLocationPerformance: true
    },

    // ===== AIRTABLE CONFIGURATION =====
    airtable: {
        baseId: 'appL4rTljQgGkjTtp',

        // Table IDs - set to null to disable a data source
        tables: {
            ghl: 'tblQJeHV2ZCHoaSoy',                           // GHL
            googleAds: 'tblF9Ej3DKJYhhl4i',                     // GOOGLEADS
            pos: null,                                // POS - DISABLED
            metaAds: null,                            // METAADS - DISABLED
        }
    },

    // ===== DATA SOURCE CONFIGURATION =====
    dataSources: {
        // Enabled data sources (customizer will set based on client needs)
        enabled: ["ghl", "googleAds"],

        // Disabled data sources (will be hidden from UI)
        disabled: ["pos", "metaAds"],

        // Default date ranges for each source
        defaultDateRanges: {
            ghl: 'last-14',
            googleAds: 'last-30',
            pos: 'last-30',
            metaAds: 'last-30'
        }
    },

    // ===== HELPER FUNCTIONS =====

    /**
     * Check if a data source is enabled
     */
    isEnabled: function(dataSource) {
        return this.dataSources.enabled.includes(dataSource);
    },

    /**
     * Get table ID for a specific data source
     */
    getTableId: function(dataSource) {
        return this.airtable.tables[dataSource] || null;
    },

    /**
     * Get base ID
     */
    getBaseId: function() {
        return this.airtable.baseId;
    },

    /**
     * Update store name (for customizer use)
     */
    updateStoreName: function(newStoreName) {
        this.storeName = newStoreName;
        this.businessName = newStoreName;
        this.clientName = newStoreName;

        // Update header immediately if DOM is ready
        const storeNameElement = document.getElementById('store-name');
        if (storeNameElement) {
            storeNameElement.textContent = newStoreName;
            console.log('[CONFIG] Store name updated to:', newStoreName);
        }
    },

    /**
     * Populate location dropdowns from GHL data
     */
    populateLocationsFromData: function(ghlData) {
        // Check if we're in single location mode
        const hideLocationPerformance = this.googleAdsLocations && this.googleAdsLocations.hideLocationPerformance;

        if (hideLocationPerformance) {
            console.log('[CONFIG] Single location mode detected - using business name for location dropdown');
            this.updateLocationDropdownsForSingleLocation();
            return;
        }

        if (!ghlData || !Array.isArray(ghlData)) {
            console.warn('[CONFIG] No GHL data provided for location population');
            return;
        }

        // Extract unique locations from GHL data with multiple field name variations
        const locationFields = ['Location', 'location', 'LOCATION', 'store', 'Store', 'branch', 'Branch'];
        const locations = [...new Set(
            ghlData
                .map(record => {
                    // Try multiple possible field names for location
                    for (const field of locationFields) {
                        if (record[field] && typeof record[field] === 'string' && record[field].trim()) {
                            return record[field].trim();
                        }
                    }
                    return null;
                })
                .filter(location => location)
        )].sort();

        this.locations = locations;
        console.log('[CONFIG] Populated locations from GHL data:', locations);
        console.log('[CONFIG] Total unique locations found:', locations.length);

        if (locations.length === 0) {
            console.warn('[CONFIG] No locations found in GHL data. Check field names.');
            // Log sample record to help debug field names
            if (ghlData.length > 0) {
                console.log('[CONFIG] Sample GHL record fields:', Object.keys(ghlData[0]));
            }
            return;
        }

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Store the "All Locations" option
                const allOptionText = dropdown.querySelector('option[value="all"]')?.textContent || 'All Locations';

                // Clear ALL existing options (including hardcoded ones)
                dropdown.innerHTML = '';

                // Re-add "All Locations" option first
                const allOption = document.createElement('option');
                allOption.value = 'all';
                allOption.textContent = allOptionText;
                dropdown.appendChild(allOption);

                // Add dynamic location options
                locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location;
                    option.textContent = location;
                    dropdown.appendChild(option);
                });

                console.log(`[CONFIG] Updated dropdown: ${dropdownId} with ${locations.length} locations`);
            } else {
                console.warn(`[CONFIG] Dropdown not found: ${dropdownId}`);
            }
        });

        // Force a refresh of any existing filters
        this.refreshLocationFilters();
    },

    /**
     * Refresh location filters after population
     */
    refreshLocationFilters: function() {
        // Trigger change events to refresh any dependent UI elements
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Reset to "all" and trigger change event
                dropdown.value = 'all';
                dropdown.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });

        console.log('[CONFIG] Location filters refreshed');
    },

    /**
     * Update location dropdowns for single location mode
     */
    updateLocationDropdownsForSingleLocation: function() {
        // Get the business name from configuration
        const businessName = this.businessName || this.clientName || 'Store';
        const locationName = businessName.includes('Store') ? businessName : `${businessName} Store`;

        console.log(`[CONFIG] Updating location dropdowns for single location: ${locationName}`);

        // Update all location dropdowns
        const dropdownIds = ['location-filter', 'report-location', 'compare-location'];
        dropdownIds.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // Clear existing options
                dropdown.innerHTML = '';

                // Add single location option
                const option = document.createElement('option');
                option.value = 'all';
                option.textContent = locationName;
                dropdown.appendChild(option);

                console.log(`[CONFIG] Updated dropdown: ${dropdownId} with single location: ${locationName}`);
            } else {
                console.warn(`[CONFIG] Dropdown not found: ${dropdownId}`);
            }
        });
    },

    /**
     * Clear cached data for fresh configuration
     */
    clearCache: function() {
        // Clear any cached data that might interfere with new configuration
        if (typeof localStorage !== 'undefined') {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('airtable') || key.includes('dashboard'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            console.log('[CONFIG] Cleared cached data for fresh configuration');
        }
    },

    // ===== GOOGLE ADS LOCATION CONFIGURATION HELPERS =====

    /**
     * Update Google Ads location configuration for a new client
     * @param {Array} locationNames - Array of location names (e.g., ['Downtown', 'Uptown', 'Westside'])
     * @param {Object} options - Optional configuration (icons, colors, aliases, etc.)
     */
    updateGoogleAdsLocations: function(locationNames, options = {}) {
        if (!Array.isArray(locationNames) || locationNames.length === 0) {
            console.error('[CONFIG] Invalid location names provided. Must be a non-empty array.');
            return false;
        }

        // Update the location names
        this.googleAdsLocations.names = locationNames;

        // Update optional configurations
        if (options.icons && typeof options.icons === 'object') {
            this.googleAdsLocations.icons = { ...this.googleAdsLocations.icons, ...options.icons };
        }

        if (options.colors && typeof options.colors === 'object') {
            this.googleAdsLocations.colors = { ...this.googleAdsLocations.colors, ...options.colors };
        }

        if (options.aliases && typeof options.aliases === 'object') {
            this.googleAdsLocations.aliases = { ...this.googleAdsLocations.aliases, ...options.aliases };
        }

        if (typeof options.caseSensitive === 'boolean') {
            this.googleAdsLocations.caseSensitive = options.caseSensitive;
        }

        if (typeof options.partialMatch === 'boolean') {
            this.googleAdsLocations.partialMatch = options.partialMatch;
        }

        if (options.defaultIcon) {
            this.googleAdsLocations.defaultIcon = options.defaultIcon;
        }

        if (options.defaultColor) {
            this.googleAdsLocations.defaultColor = options.defaultColor;
        }

        console.log('[CONFIG] Updated Google Ads locations:', this.googleAdsLocations.names);
        console.log('[CONFIG] Location configuration updated successfully');

        return true;
    },

    /**
     * Get current Google Ads location configuration
     */
    getGoogleAdsLocations: function() {
        return this.googleAdsLocations;
    },

    // Tab visibility management
    updateTabVisibility: function(dataAvailability = {}) {
        console.log('[CONFIG] Updating tab visibility based on config and data availability');

        const tabMappings = {
            'pos': [
                // Sales Report tab button and content (POS is integrated into Sales Report)
                'button[data-tab="sales-report"]', // Tab button
                'sales-report', // Tab content
                // POS sections within Sales Report tab (actual HTML elements)
                '#locationRevenueChart', '#locationTransactionsChart',
                '.chart-card:has(#locationRevenueChart)', '.chart-card:has(#locationTransactionsChart)'
            ],
            'metaAds': [
                'button[data-tab="meta-ads-report"]', // Tab button
                'meta-ads-report' // Tab content
            ],
            'googleAds': [
                'button[data-tab="google-ads-report"]', // Tab button
                'google-ads-report' // Tab content
            ],
            'ghl': [
                'button[data-tab="lead-report"]', // Tab button
                'lead-report' // Tab content
            ],
            'masterOverview': [
                'button[data-tab="master-overview"]', // Tab button
                'master-overview' // Tab content
            ]
        };

        // Check each data source
        Object.keys(tabMappings).forEach(dataSource => {
            try {
                const isEnabled = this.isEnabled(dataSource);
                const hasData = dataAvailability[dataSource] || false;
                const shouldShow = this.shouldShowTab(dataSource, hasData);

                const elements = tabMappings[dataSource];
                this.updateTabElements(elements, shouldShow, dataSource);
            } catch (error) {
                console.warn(`[CONFIG] Error processing tab visibility for ${dataSource}:`, error);
                // Hide the tab if there's an error
                const elements = tabMappings[dataSource];
                this.updateTabElements(elements, false, dataSource);
            }
        });

        // Handle Master Overview visibility based on available data sources
        this.updateMasterOverviewVisibility(dataAvailability);

        // Set smart default tab after all visibility updates
        this.setSmartDefaultTab(dataAvailability);

        // Hide Performance by Location section if configured
        this.updateLocationPerformanceVisibility();
    },

    shouldShowTab: function(dataSource, hasData = false) {
        // Check if data source is enabled in configuration
        if (!this.isEnabled(dataSource)) {
            console.log(`[CONFIG] Hiding ${dataSource} tab: disabled in configuration`);
            return false;
        }

        // For enabled sources, check if they have data
        if (!hasData) {
            console.log(`[CONFIG] Hiding ${dataSource} tab: enabled but no data available`);
            return false;
        }

        // Show if enabled and has data (or data status unknown)
        console.log(`[CONFIG] Showing ${dataSource} tab: enabled and has data`);
        return true;
    },

    updateMasterOverviewVisibility: function(dataAvailability) {
        // Count how many data sources have data
        const enabledSources = ['ghl', 'googleAds', 'pos', 'metaAds'];
        const sourcesWithData = enabledSources.filter(source => {
            return this.isEnabled(source) && dataAvailability[source];
        });

        console.log(`[CONFIG] Data sources with data: ${sourcesWithData.length} (${sourcesWithData.join(', ')})`);

        // Hide Master Overview if only one or no data sources have data
        const shouldShowMasterOverview = sourcesWithData.length > 1;

        if (shouldShowMasterOverview) {
            console.log('[CONFIG] Showing Master Overview: multiple data sources available');
        } else {
            console.log('[CONFIG] Hiding Master Overview: only one or no data sources available');
        }

        // Update Master Overview tab visibility
        const masterOverviewElements = ['button[data-tab="master-overview"]', 'master-overview'];
        this.updateTabElements(masterOverviewElements, shouldShowMasterOverview, 'masterOverview');
    },

    setSmartDefaultTab: function(dataAvailability) {
        // Priority order: GHL > Google Ads > POS > Meta Ads
        const tabPriority = [
            { source: 'ghl', tabId: 'lead-report' },
            { source: 'googleAds', tabId: 'google-ads-report' },
            { source: 'pos', tabId: 'sales-report' },
            { source: 'metaAds', tabId: 'meta-ads-report' }
        ];

        // Find the highest priority tab that has data
        let defaultTabId = null;
        for (const tab of tabPriority) {
            if (this.isEnabled(tab.source) && dataAvailability[tab.source]) {
                defaultTabId = tab.tabId;
                console.log(`[CONFIG] Setting default tab to: ${defaultTabId} (${tab.source} has data)`);
                break;
            }
        }

        // If no data sources have data, fall back to the first enabled source
        if (!defaultTabId) {
            for (const tab of tabPriority) {
                if (this.isEnabled(tab.source)) {
                    defaultTabId = tab.tabId;
                    console.log(`[CONFIG] Fallback default tab to: ${defaultTabId} (${tab.source} is enabled)`);
                    break;
                }
            }
        }

        // Apply the default tab selection
        if (defaultTabId) {
            this.activateDefaultTab(defaultTabId);
        }
    },

    activateDefaultTab: function(tabId) {
        // Remove active class from all tabs
        const allTabButtons = document.querySelectorAll('.tab-button');
        const allTabContents = document.querySelectorAll('.tab-content');

        allTabButtons.forEach(btn => btn.classList.remove('active'));
        allTabContents.forEach(content => content.classList.remove('active'));

        // Activate the selected tab
        const targetButton = document.querySelector(`button[data-tab="${tabId}"]`);
        const targetContent = document.getElementById(tabId);

        if (targetButton && targetContent) {
            targetButton.classList.add('active');
            targetContent.classList.add('active');
            console.log(`[CONFIG] Activated default tab: ${tabId}`);
        } else {
            console.warn(`[CONFIG] Could not find tab elements for: ${tabId}`);
        }
    },

    updateTabElements: function(elements, shouldShow, dataSource) {
        elements.forEach(selector => {
            // Handle different selector types
            let element;
            if (selector.startsWith('button[')) {
                element = document.querySelector(selector);
            } else if (selector.startsWith('.') || selector.startsWith('#')) {
                // CSS selector (class or ID)
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (shouldShow) {
                        el.style.display = '';
                        el.classList.remove('hidden');
                    } else {
                        el.style.display = 'none';
                        el.classList.add('hidden');
                        console.log(`[CONFIG] Hidden element: ${selector} (${dataSource})`);
                    }
                });
                return; // Skip the single element logic below
            } else {
                element = document.getElementById(selector);
            }

            if (element) {
                if (shouldShow) {
                    element.style.display = '';
                    element.classList.remove('hidden');
                } else {
                    element.style.display = 'none';
                    element.classList.add('hidden');
                    console.log(`[CONFIG] Hidden tab element: ${selector} (${dataSource})`);
                }
            }
        });
    },

    // Special function to handle POS content within Sales Report
    updateSalesReportPOSContent: function(hasData = false) {
        const shouldShow = this.shouldShowTab('pos', hasData);

        if (!shouldShow) {
            // Hide POS-specific content and show alternative message
            console.log('[CONFIG] Hiding POS content in Sales Report...');

            // Hide the Revenue by Location chart
            const revenueChart = document.getElementById('locationRevenueChart');
            if (revenueChart) {
                const revenueCard = revenueChart.closest('.chart-card');
                if (revenueCard) {
                    revenueCard.style.display = 'none';
                    revenueCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Revenue by Location chart');
                }
            }

            // Hide the Transactions by Location chart
            const transactionsChart = document.getElementById('locationTransactionsChart');
            if (transactionsChart) {
                const transactionsCard = transactionsChart.closest('.chart-card');
                if (transactionsCard) {
                    transactionsCard.style.display = 'none';
                    transactionsCard.classList.add('hidden');
                    console.log('[CONFIG] Hidden Transactions by Location chart');
                }
            }

            // Add a replacement message for the hidden POS content
            const chartRow = document.querySelector('#sales-report .chart-row');
            if (chartRow) {
                // Remove existing POS disabled message if any
                const existingMessage = chartRow.querySelector('.pos-disabled-message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                // Add a message explaining POS is not available for this client
                const posMessage = document.createElement('div');
                posMessage.className = 'pos-disabled-message chart-card';
                posMessage.style.cssText = 'text-align: center; padding: 2rem; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 1rem 0;';
                posMessage.innerHTML = `
                    <h3 style="color: #6c757d; margin-bottom: 1rem;">📊 Sales Report - Lead Data Only</h3>
                    <p style="color: #6c757d; margin: 0;">This dashboard is configured to show lead conversion data. POS integration is not enabled for this client.</p>
                `;

                // Add the message to replace the hidden charts
                chartRow.appendChild(posMessage);
                console.log('[CONFIG] Added POS disabled message to Sales Report');
            }

            console.log('[CONFIG] POS content hidden in Sales Report - showing lead-only view');
        }
    },

    updateLocationPerformanceVisibility: function() {
        // Check if location performance should be hidden
        const hideLocationPerformance = this.googleAdsLocations && this.googleAdsLocations.hideLocationPerformance;

        if (hideLocationPerformance) {
            console.log('[CONFIG] Hiding Performance by Location section due to configuration');

            // Hide the entire location performance section
            const locationSection = document.getElementById('location-performance-section');
            if (locationSection) {
                locationSection.style.display = 'none';
                locationSection.classList.add('hidden');
            }
        } else {
            console.log('[CONFIG] Showing Performance by Location section');

            // Show the location performance section
            const locationSection = document.getElementById('location-performance-section');
            if (locationSection) {
                locationSection.style.display = 'block';
                locationSection.classList.remove('hidden');
            }
        }
    }
};

// Initialize configuration when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Clear any cached data for fresh start
    window.CLIENT_CONFIG.clearCache();

    // Update store name in header when DOM is ready
    const storeNameElement = document.getElementById('store-name');
    if (storeNameElement && window.CLIENT_CONFIG.storeName) {
        storeNameElement.textContent = window.CLIENT_CONFIG.storeName;
        console.log('[CONFIG] Updated store name in header:', window.CLIENT_CONFIG.storeName);
    }
});

console.log('[CONFIG] Client configuration loaded:', window.CLIENT_CONFIG.clientName);
console.log('[CONFIG] Enabled data sources:', window.CLIENT_CONFIG.dataSources.enabled);
console.log('[CONFIG] Base ID:', window.CLIENT_CONFIG.getBaseId());
