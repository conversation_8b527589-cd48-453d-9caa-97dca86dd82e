2025-07-20 19:24:26 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-20 19:24:26 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\Tools\output\Gadget_Repair_LV
2025-07-20 19:24:26 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-20 19:24:26 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-20 19:24:26 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-20 19:24:26 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-20 19:24:26 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "GET / HTTP/1.1" 200 -
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "GET /client-config.js HTTP/1.1" 200 -
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-20 19:24:30 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:30] "GET /script.js HTTP/1.1" 200 -
2025-07-20 19:24:31 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-20 19:24:31 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:31] "GET /api/airtable/records?baseId=appL4rTljQgGkjTtp&tableId=tblF9Ej3DKJYhhl4i HTTP/1.1" 200 -
2025-07-20 19:24:49 | INFO     | root | Server-side pagination complete: 2980 total records from 30 pages
2025-07-20 19:24:49 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:49] "GET /api/airtable/records?baseId=appL4rTljQgGkjTtp&tableId=tblQJeHV2ZCHoaSoy HTTP/1.1" 200 -
2025-07-20 19:24:53 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 19:24:53] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
