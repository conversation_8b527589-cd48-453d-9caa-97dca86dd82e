2025-07-20 17:56:06 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-20 17:56:06 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\Tools\output\Fix_Devices_georgetown
2025-07-20 17:56:06 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-20 17:56:06 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-20 17:56:06 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-20 17:56:06 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-20 17:56:06 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-20 17:56:12 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:12] "GET / HTTP/1.1" 200 -
2025-07-20 17:56:12 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:12] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-20 17:56:13 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:13] "GET /client-config.js HTTP/1.1" 200 -
2025-07-20 17:56:13 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:13] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-20 17:56:13 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:13] "GET /styles.css HTTP/1.1" 200 -
2025-07-20 17:56:13 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:13] "GET /script.js HTTP/1.1" 200 -
2025-07-20 17:56:14 | INFO     | root | Server-side pagination complete: 0 total records from 1 pages
2025-07-20 17:56:14 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:14] "GET /api/airtable/records?baseId=appdqnkW48U1w9ZAx&tableId=tblJTqbxeqm5lEaHQ HTTP/1.1" 200 -
2025-07-20 17:56:16 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:16] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-20 17:56:16 | INFO     | root | Server-side pagination complete: 479 total records from 5 pages
2025-07-20 17:56:16 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:16] "GET /api/airtable/records?baseId=appdqnkW48U1w9ZAx&tableId=tblE0GD3uSy9BcLre HTTP/1.1" 200 -
2025-07-20 17:56:19 | INFO     | werkzeug | 127.0.0.1 - - [20/Jul/2025 17:56:19] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
